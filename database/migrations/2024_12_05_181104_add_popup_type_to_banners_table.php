<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            Schema::table('banners', function (Blueprint $table) {
                $table->enum('type',['banner', 'slider', 'popup'])->change();
            });
        } else {
            // SQLite and other database implementations
            // For SQLite, we need to recreate the column
            if (Schema::hasColumn('banners', 'type')) {
                // Create a temporary column
                Schema::table('banners', function (Blueprint $table) {
                    $table->string('type_new')->default('banner');
                });

                // Copy data from old column to new column, ensuring values are within allowed set
                DB::table('banners')
                    ->whereIn('type', ['banner', 'slider'])
                    ->update(['type_new' => DB::raw('type')]);

                // Set any other values to 'banner'
                DB::table('banners')
                    ->whereNotIn('type', ['banner', 'slider', 'popup'])
                    ->update(['type_new' => 'banner']);

                // Update any 'popup' values
                DB::table('banners')
                    ->where('type', 'popup')
                    ->update(['type_new' => 'popup']);

                // Drop the old column
                Schema::table('banners', function (Blueprint $table) {
                    $table->dropColumn('type');
                });

                // Rename the new column to the original name
                Schema::table('banners', function (Blueprint $table) {
                    $table->renameColumn('type_new', 'type');
                });

                // Add a check constraint (if supported)
                if ($driver === 'pgsql') {
                    DB::statement("ALTER TABLE banners ADD CONSTRAINT check_banner_type CHECK (type IN ('banner', 'slider', 'popup'))");
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            Schema::table('banners', function (Blueprint $table) {
                $table->enum('type',['banner', 'slider'])->change();
            });
        } else {
            // SQLite and other database implementations
            // For SQLite, we need to recreate the column
            if (Schema::hasColumn('banners', 'type')) {
                // Create a temporary column
                Schema::table('banners', function (Blueprint $table) {
                    $table->string('type_new')->default('banner');
                });

                // Copy data from old column to new column, ensuring values are within allowed set
                DB::table('banners')
                    ->whereIn('type', ['banner', 'slider'])
                    ->update(['type_new' => DB::raw('type')]);

                // Set any 'popup' values to 'banner'
                DB::table('banners')
                    ->where('type', 'popup')
                    ->update(['type_new' => 'banner']);

                // Drop the old column
                Schema::table('banners', function (Blueprint $table) {
                    $table->dropColumn('type');
                });

                // Rename the new column to the original name
                Schema::table('banners', function (Blueprint $table) {
                    $table->renameColumn('type_new', 'type');
                });

                // Add a check constraint (if supported)
                if ($driver === 'pgsql') {
                    DB::statement("ALTER TABLE banners ADD CONSTRAINT check_banner_type CHECK (type IN ('banner', 'slider'))");
                }
            }
        }
    }
};
