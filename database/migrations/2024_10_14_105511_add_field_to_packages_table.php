<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->string('tour_languages')->nullable()->after('duration');
            $table->integer('group_size')->nullable()->after('duration');
            $table->string('tour_type')->after('duration');
            $table->string('location')->nullable()->after('duration');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            $table->dropColumn('tour_languages');
            $table->dropColumn('group_size');
            $table->dropColumn('tour_type');
            $table->dropColumn('location');
        });
    }
};
