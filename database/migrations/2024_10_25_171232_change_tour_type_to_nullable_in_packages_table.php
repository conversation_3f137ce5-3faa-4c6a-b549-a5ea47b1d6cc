<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            Schema::table('packages', function (Blueprint $table) {
                $table->string('tour_type')->nullable()->change();
            });
        } else {
            // SQLite and other database implementations
            // For SQLite, we need to recreate the column
            if (Schema::hasColumn('packages', 'tour_type')) {
                // Create a temporary column
                Schema::table('packages', function (Blueprint $table) {
                    $table->string('tour_type_new')->nullable();
                });

                // Copy data from old column to new column
                DB::table('packages')
                    ->whereNotNull('tour_type')
                    ->update(['tour_type_new' => DB::raw('tour_type')]);

                // Drop the old column
                Schema::table('packages', function (Blueprint $table) {
                    $table->dropColumn('tour_type');
                });

                // Rename the new column to the original name
                Schema::table('packages', function (Blueprint $table) {
                    $table->renameColumn('tour_type_new', 'tour_type');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            Schema::table('packages', function (Blueprint $table) {
                $table->string('tour_type')->nullable(false)->change();
            });
        } else {
            // SQLite and other database implementations
            // For SQLite, we need to recreate the column
            if (Schema::hasColumn('packages', 'tour_type')) {
                // Create a temporary column
                Schema::table('packages', function (Blueprint $table) {
                    $table->string('tour_type_new')->nullable(false)->default('');
                });

                // Copy data from old column to new column
                DB::table('packages')
                    ->whereNotNull('tour_type')
                    ->update(['tour_type_new' => DB::raw('tour_type')]);

                // Drop the old column
                Schema::table('packages', function (Blueprint $table) {
                    $table->dropColumn('tour_type');
                });

                // Rename the new column to the original name
                Schema::table('packages', function (Blueprint $table) {
                    $table->renameColumn('tour_type_new', 'tour_type');
                });
            }
        }
    }
};
