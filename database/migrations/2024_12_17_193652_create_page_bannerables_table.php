<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('page_bannerables', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('order')->default(0);
            $table->unsignedBigInteger('page_banner_id');
            $table->unsignedBigInteger('page_bannerable_id');
            $table->string('page_bannerable_type');
            $table->timestamps();

            $table->foreign('page_banner_id')
                ->references('id')
                ->on('page_banners')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('page_bannerables');
    }
};
