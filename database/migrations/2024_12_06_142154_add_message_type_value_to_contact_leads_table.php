<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            Schema::table('contact_leads', function (Blueprint $table) {
                $table->enum('message_type', ['travel', 'food', 'contact', 'create_package'])->change();
            });
        } else {
            // SQLite and other database implementations
            // For SQLite, we need to recreate the column
            if (Schema::hasColumn('contact_leads', 'message_type')) {
                // Create a temporary column
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->string('message_type_new')->default('contact');
                });

                // Copy data from old column to new column, ensuring values are within allowed set
                DB::table('contact_leads')
                    ->whereIn('message_type', ['travel', 'food', 'contact'])
                    ->update(['message_type_new' => DB::raw('message_type')]);

                // Set any other values to 'contact'
                DB::table('contact_leads')
                    ->whereNotIn('message_type', ['travel', 'food', 'contact', 'create_package'])
                    ->update(['message_type_new' => 'contact']);

                // Update any 'create_package' values
                DB::table('contact_leads')
                    ->where('message_type', 'create_package')
                    ->update(['message_type_new' => 'create_package']);

                // Drop the old column
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->dropColumn('message_type');
                });

                // Rename the new column to the original name
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->renameColumn('message_type_new', 'message_type');
                });

                // Add a check constraint (if supported)
                if ($driver === 'pgsql') {
                    DB::statement("ALTER TABLE contact_leads ADD CONSTRAINT check_message_type CHECK (message_type IN ('travel', 'food', 'contact', 'create_package'))");
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            Schema::table('contact_leads', function (Blueprint $table) {
                $table->enum('message_type', ['travel', 'food', 'contact'])->change();
            });
        } else {
            // SQLite and other database implementations
            // For SQLite, we need to recreate the column
            if (Schema::hasColumn('contact_leads', 'message_type')) {
                // Create a temporary column
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->string('message_type_new')->default('contact');
                });

                // Copy data from old column to new column, ensuring values are within allowed set
                DB::table('contact_leads')
                    ->whereIn('message_type', ['travel', 'food', 'contact'])
                    ->update(['message_type_new' => DB::raw('message_type')]);

                // Set any 'create_package' values to 'contact'
                DB::table('contact_leads')
                    ->where('message_type', 'create_package')
                    ->update(['message_type_new' => 'contact']);

                // Drop the old column
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->dropColumn('message_type');
                });

                // Rename the new column to the original name
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->renameColumn('message_type_new', 'message_type');
                });

                // Add a check constraint (if supported)
                if ($driver === 'pgsql') {
                    DB::statement("ALTER TABLE contact_leads ADD CONSTRAINT check_message_type CHECK (message_type IN ('travel', 'food', 'contact'))");
                }
            }
        }
    }
};
