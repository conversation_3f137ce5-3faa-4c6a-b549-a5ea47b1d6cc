<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            Schema::table('contact_leads', function (Blueprint $table) {
                $table->integer('persons')->nullable()->change();
            });
        } else {
            // SQLite and other database implementations
            // For SQLite, we need to recreate the column
            if (Schema::hasColumn('contact_leads', 'persons')) {
                // Create a temporary column
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->integer('persons_new')->nullable();
                });

                // Copy data from old column to new column
                DB::table('contact_leads')
                    ->whereNotNull('persons')
                    ->update(['persons_new' => DB::raw('persons')]);

                // Drop the old column
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->dropColumn('persons');
                });

                // Rename the new column to the original name
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->renameColumn('persons_new', 'persons');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            Schema::table('contact_leads', function (Blueprint $table) {
                $table->integer('persons')->nullable(false)->change();
            });
        } else {
            // SQLite and other database implementations
            // For SQLite, we need to recreate the column
            if (Schema::hasColumn('contact_leads', 'persons')) {
                // Create a temporary column
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->integer('persons_new')->nullable(false)->default(0);
                });

                // Copy data from old column to new column
                DB::table('contact_leads')
                    ->whereNotNull('persons')
                    ->update(['persons_new' => DB::raw('persons')]);

                // Set default value for NULL values
                DB::table('contact_leads')
                    ->whereNull('persons')
                    ->update(['persons_new' => 0]);

                // Drop the old column
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->dropColumn('persons');
                });

                // Rename the new column to the original name
                Schema::table('contact_leads', function (Blueprint $table) {
                    $table->renameColumn('persons_new', 'persons');
                });
            }
        }
    }
};
