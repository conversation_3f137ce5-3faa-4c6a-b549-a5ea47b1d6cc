<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            DB::statement("ALTER TABLE packages MODIFY COLUMN type ENUM('travel', 'food', 'cruise') DEFAULT 'travel'");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packages', function (Blueprint $table) {
            DB::statement("ALTER TABLE packages MODIFY COLUMN type ENUM('travel', 'food') DEFAULT 'travel'");
        });
    }
};
