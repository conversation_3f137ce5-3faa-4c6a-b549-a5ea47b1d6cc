<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('image_galleries', function (Blueprint $table) {
            $table->id();
            $table->boolean('published')->default(false);
            $table->boolean('featured')->default(false);
            $table->json('title');
            $table->json('content')->nullable();
            $table->unsignedSmallInteger('order')->default(0);
            $table->unsignedBigInteger('galleriable_id');
            $table->string('galleriable_type');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('image_galleries');
    }
};
