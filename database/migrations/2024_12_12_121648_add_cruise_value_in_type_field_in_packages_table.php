<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            DB::statement("ALTER TABLE packages MODIFY COLUMN type ENUM('travel', 'food', 'cruise') DEFAULT 'travel'");
        } else {
            // SQLite and other database implementations
            // First, check if the column exists
            if (Schema::hasColumn('packages', 'type')) {
                // Create a temporary column
                Schema::table('packages', function (Blueprint $table) {
                    $table->string('type_new')->default('travel');
                });

                // Copy data from old column to new column
                DB::table('packages')
                    ->whereIn('type', ['travel', 'food'])
                    ->update(['type_new' => DB::raw('type')]);

                // Set any other values to 'travel'
                DB::table('packages')
                    ->whereNotIn('type', ['travel', 'food', 'cruise'])
                    ->update(['type_new' => 'travel']);

                // Set 'cruise' values if they exist
                DB::table('packages')
                    ->where('type', 'cruise')
                    ->update(['type_new' => 'cruise']);

                // Drop the old column
                Schema::table('packages', function (Blueprint $table) {
                    $table->dropColumn('type');
                });

                // Rename the new column to the original name
                Schema::table('packages', function (Blueprint $table) {
                    $table->renameColumn('type_new', 'type');
                });
            } else {
                // If the column doesn't exist, create it
                Schema::table('packages', function (Blueprint $table) {
                    $table->string('type')->default('travel');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get the database driver
        $driver = DB::connection()->getDriverName();

        if ($driver === 'mysql') {
            // MySQL-specific implementation
            DB::statement("ALTER TABLE packages MODIFY COLUMN type ENUM('travel', 'food') DEFAULT 'travel'");
        } else {
            // SQLite and other database implementations
            // For SQLite, we need to recreate the column
            if (Schema::hasColumn('packages', 'type')) {
                // Create a temporary column
                Schema::table('packages', function (Blueprint $table) {
                    $table->string('type_new')->default('travel');
                });

                // Copy data from old column to new column, but convert 'cruise' to 'travel'
                DB::table('packages')
                    ->whereIn('type', ['travel', 'food'])
                    ->update(['type_new' => DB::raw('type')]);

                // Set 'cruise' values to 'travel'
                DB::table('packages')
                    ->where('type', 'cruise')
                    ->update(['type_new' => 'travel']);

                // Drop the old column
                Schema::table('packages', function (Blueprint $table) {
                    $table->dropColumn('type');
                });

                // Rename the new column to the original name
                Schema::table('packages', function (Blueprint $table) {
                    $table->renameColumn('type_new', 'type');
                });
            }
        }
    }
};
