<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('posts', function (Blueprint $table) {
            $table->id();
            $table->boolean('published')->default(false);
            $table->boolean('featured')->default(false);
            $table->json('title');
            $table->json('slug');
            $table->json('meta_title')->nullable();
            $table->json('meta_description')->nullable();
            $table->json('tagline')->nullable();
            $table->json('content')->nullable();
            $table->timestamp('published_at')->useCurrent();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('posts');
    }
};
