<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();
            $table->boolean('published')->default(false);
            $table->boolean('featured')->default(false);
            $table->json('title');
            $table->json('subtitle')->nullable();
            $table->json('slug');
            $table->json('meta_title')->nullable();
            $table->json('meta_description')->nullable();
            $table->json('tagline')->nullable();
            $table->json('content')->nullable();
            $table->json('itinerary')->nullable();
            $table->json('info')->nullable();
            $table->json('included')->nullable();
            $table->json('not_included')->nullable();
            $table->string('sku')->nullable();
            $table->integer('price')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
