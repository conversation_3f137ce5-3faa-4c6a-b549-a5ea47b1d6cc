<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Faq>
 */
class FaqFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'question' => [
                'el' => fake()->sentence(12, false),
                'en' => fake()->sentence(12, false),
            ],
            'answer' => [
                'el' => fake()->sentence(45, false),
                'en' => fake()->sentence(45, false),
            ]
        ];
    }
}
