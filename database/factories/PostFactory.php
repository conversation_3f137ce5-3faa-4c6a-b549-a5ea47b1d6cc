<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => fake()->sentence(3, false),
            'meta_title' => fake()->sentence(3, false),
            'tagline' => fake()->sentence(5, false),
            'meta_description' => fake()->sentence(5, false),
            'content' => fake()->paragraph(5),
        ];
    }

}
