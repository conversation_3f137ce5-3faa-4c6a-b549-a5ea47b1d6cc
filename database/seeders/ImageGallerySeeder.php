<?php

namespace Database\Seeders;

use Carbon\Carbon;
use App\Models\Image;
use App\Models\Package;
use App\Models\ImageGallery;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class ImageGallerySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $foodtours = Package::food()->get();

        foreach ($foodtours as $foodtour) {

            // create Image Gallery
            $imageGallery = ImageGallery::updateOrCreate([
                'galleriable_type' => 'App\Models\Package',
                'galleriable_id' => $foodtour->id,
            ], [
                'title' => [
                    'el' => $foodtour->getTranslation('title', 'el'),
                    'en' => $foodtour->getTranslation('title', 'en'),
                ],
                'galleriable_type' => 'App\Models\Package',
                'galleriable_id' => $foodtour->id,
                'published' => 1,
            ]);


            // find food tour images
            $images = $foodtour->images->where('main', 0);

            // find and move the file to galleries folder
            foreach ($images as $image) {
                Storage::copy(
                    'packages' . '/' . Carbon::parse($image->updated_at)->year . '/' . Carbon::parse($image->updated_at)->month . '/' . $image->filename,
                    'galleries/' . $imageGallery->id . '/' . $image->filename
                );

                $image->update([
                    'imageable_type' => 'App\Models\ImageGallery',
                    'imageable_id' => $imageGallery->id,
                    'updated_at' => Carbon::create(2024, 12, 31),
                ]);
            }
        }
    }
}
