<?php

namespace Database\Seeders;

use App\Models\Alias;
use App\Models\Country;
use Illuminate\Database\Seeder;

class WorldGeographyAliasesSeeder extends Seeder
{
    /**
     * Create aliases for some major countries.
     */
    public function run(): void
    {
        // USA
        $usa = Country::where('iso_code', 'USA')->first();

        $aliases = [
            ['name' => 'USA', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'U.S.A.', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'America', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'United States of America', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'United States', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'ΗΠΑ', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'Η.Π.Α.', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'Ηνωμένες Πολιτείες', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'Ηνωμένες Πολιτείες Αμερικής', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'ΗΠΑ', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'Η.Π.Α.', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'Ηνωμένες Πολιτείες', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'Ενωμένες Πολιτείες', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'Ηνωμένες Πολιτείες Αμερικής', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
            ['name' => 'Ενωμένες Πολιτείες Αμερικής', 'aliasable_id' => $usa->id, 'aliasable_type' => Country::class],
        ];

        Alias::upsert($aliases, ['name', 'aliasable_type'], ['aliasable_id']);

        // United Kingdom
        $uk = Country::where('iso_code', 'GBR')->first();

        $aliases = array_merge($aliases, [
            ['name' => 'UK', 'aliasable_id' => $uk->id, 'aliasable_type' => Country::class],
            ['name' => 'U.K.', 'aliasable_id' => $uk->id, 'aliasable_type' => Country::class],
            ['name' => 'United Kingdom', 'aliasable_id' => $uk->id, 'aliasable_type' => Country::class],
            ['name' => 'Αγγλία', 'aliasable_id' => $uk->id, 'aliasable_type' => Country::class],
            ['name' => 'Βρετανία', 'aliasable_id' => $uk->id, 'aliasable_type' => Country::class],
            ['name' => 'Ηνωμένο Βασίλειο', 'aliasable_id' => $uk->id, 'aliasable_type' => Country::class],
            ['name' => 'Ενωμένο Βασίλειο', 'aliasable_id' => $uk->id, 'aliasable_type' => Country::class],
            ['name' => 'Ηνωμένο Βασίλειο Βρετανίας', 'aliasable_id' => $uk->id, 'aliasable_type' => Country::class],
            ['name' => 'Ενωμένο Βασίλειο Βρετανίας', 'aliasable_id' => $uk->id, 'aliasable_type' => Country::class],
        ]);

        Alias::upsert($aliases, ['name', 'aliasable_type'], ['aliasable_id']);
    }
}
