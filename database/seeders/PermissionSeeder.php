<?php

namespace Database\Seeders;

use Spatie\Permission\Models\Permission;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Permission::updateOrCreate(
            [
                'name' => 'manage pages',
                'module' => 'Cerberus',
            ],
        );
        Permission::updateOrCreate(
            [
                'name' => 'manage posts',
                'module' => 'Cerberus',
            ],
        );
        Permission::updateOrCreate(
            [
                'name' => 'manage holiday packages',
                'module' => 'Cerberus',
            ],
        );
        Permission::updateOrCreate(
            [
                'name' => 'manage products',
                'module' => 'Cerberus',
            ],
        );
        Permission::updateOrCreate(
            [
                'name' => 'manage faqs',
                'module' => 'Cerberus',
            ],
        );
        Permission::updateOrCreate(
            [
                'name' => 'manage admins',
                'module' => 'Cerberus',
            ],
        );
        Permission::updateOrCreate(
            [
                'name' => 'manage endusers',
                'module' => 'Cerberus',
            ],
        );
        Permission::updateOrCreate(
            [
                'name' => 'manage menus',
                'module' => 'Cerberus',
            ],
        );
        Permission::updateOrCreate(
            [
                'name' => 'manage leads',
                'module' => 'Cerberus',
            ],
        );

        Permission::updateOrCreate(
            [
                'name' => 'manage faqs',
                'module' => 'Cerberus',
            ],
        );
   
        Permission::updateOrCreate(
            [
                'name' => 'manage pages',
                'module' => 'Cerberus',
            ],
        );
 
        Permission::updateOrCreate(
            [
                'name' => 'manage banners',
                'module' => 'Cerberus',
            ],
        );

        // super admin has full permissions
        $super_admin_role = Role::where('name', 'super admin')->first();

        if(!empty($super_admin_role))
        {
            $permissions = Permission::all();
            $super_admin_role->syncPermissions($permissions);
        }

        // admin permissions
        $admin_role = Role::where('name', 'admin')->first();

        if(!empty($admin_role))
        {
            $permissions = Permission::whereNotIn('name', ['manage admins'])
                ->get();
            $admin_role->syncPermissions($permissions);
        }
    }
}
