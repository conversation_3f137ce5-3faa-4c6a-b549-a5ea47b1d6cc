<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class WorldGeographiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // first, empty the tables
        // note that it is important to delete the "child" tables first to respect foreign key constraints
        DB::table('countries')->delete();
        DB::table('subregions')->delete();
        DB::table('continents')->delete();

        $json = File::get(database_path('seeders/data/all.json'));
        $countries = json_decode($json, true);

        echo "Total countries/territories: " . count($countries) . PHP_EOL;

        // Caches to avoid duplicate inserts
        $continentMap = [];
        $subregionMap = [];

        foreach ($countries as $entry)
        {
            // Skip if essential values are missing
            if (empty($entry['region']) || empty($entry['sub-region']))
            {
                continue;
            }

            // === Handle Continent ===
            $continentName = $entry['region'];
            // create the continent if it doesn't exist in the cache
            if (!isset($continentMap[$continentName]))
            {
                $continentId = DB::table('continents')->insertGetId([
                    'name'          => $continentName,
                    'created_at'    => now(),
                    'updated_at'    => now(),
                ]);
                $continentMap[$continentName] = $continentId;
            }
            // else get the id from the cache
            $continentId = $continentMap[$continentName];

            // === Handle Subregion ===
            $subregionName = $entry['sub-region'];
            // create the subregion if it doesn't exist in the cache
            if (!isset($subregionMap[$subregionName]))
            {
                $subregionId = DB::table('subregions')->insertGetId([
                    'name'          => $subregionName,
                    'continent_id'  => $continentId,
                    'created_at'    => now(),
                    'updated_at'    => now(),
                ]);
                $subregionMap[$subregionName] = $subregionId;
            }
            // else get the id from the cache
            $subregionId = $subregionMap[$subregionName];

            // === Insert Country ===
            DB::table('countries')->insert([
                'name'          => $entry['name'],
                'iso_code'      => $entry['alpha-3'],
                'subregion_id'  => $subregionId,
                'created_at'    => now(),
                'updated_at'    => now(),
            ]);
        }

    }
}
