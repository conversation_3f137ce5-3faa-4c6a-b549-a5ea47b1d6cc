<?php

namespace Database\Seeders;

use App\Models\Menu;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Menu::updateOrCreate([
            'title' => 'Navigation Menu Gr',
            'name' => 'nav-menu-gr',
            'locale' => 'el',
            'published' => 1
        ]);
        
        Menu::updateOrCreate([
            'title' => 'Navigation Menu En',
            'name' => 'nav-menu-en',
            'locale' => 'en',
            'published' => 1
        ]);
       
        Menu::updateOrCreate([
            'title' => 'Footer Menu Gr',
            'name' => 'footer-menu-gr',
            'locale' => 'el',
            'published' => 1
        ]);
        
        Menu::updateOrCreate([
            'title' => 'Footer Menu En',
            'name' => 'footer-menu-en',
            'locale' => 'en',
            'published' => 1
        ]);
    }
}
