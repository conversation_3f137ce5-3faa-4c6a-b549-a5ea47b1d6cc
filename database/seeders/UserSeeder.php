<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // super admins
        User::updateOrCreate(
            [
                'email' => '<EMAIL>',
            ],
            [
                'name'      => 'bmenekl',
                 'type'      => 'dev',
                'password'  => '12345678',
                ]
        )
        ->assignRole('super admin');
        User::updateOrCreate(
            [
                'email' => '<EMAIL>',
            ],
            [
                'name'      => '<PERSON>',
                 'type'      => 'dev',
                'password'  => '12345678',
                ]
        )
        ->assignRole('super admin');

        // admins
//        User::updateOrCreate(
//            [
//                'email' => '<EMAIL>',
//            ],
//            [
//                'name'      => 'Admin Settiston',
//                'type'      => 'admin',
//                'password'  => Hash::make('12345678'),
//            ]
//        )
//            ->assignRole('admin');

        // endusers
//        User::updateOrCreate(
//            [
//                'email' => '<EMAIL>',
//            ],
//            [
//                'name'      => 'enduser',
//                'type'      => 'enduser',
//                'password'  => Hash::make('12345678'),
//            ]
//        );
    }
}
