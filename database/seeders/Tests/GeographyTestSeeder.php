<?php

namespace Database\Seeders\Tests;

use App\Models\Alias;
use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Package;
use App\Models\Region;
use App\Models\Subregion;
use Illuminate\Database\Seeder;

class GeographyTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create continents
        $europe = Continent::create(['name' => 'Europe']);
        $asia = Continent::create(['name' => 'Asia']);

        // Create subregions
        $southernEurope = Subregion::create([
            'name' => 'Southern Europe',
            'continent_id' => $europe->id
        ]);

        $westernEurope = Subregion::create([
            'name' => 'Western Europe',
            'continent_id' => $europe->id
        ]);

        $eastAsia = Subregion::create([
            'name' => 'East Asia',
            'continent_id' => $asia->id
        ]);

        // Create countries
        $italy = Country::create([
            'name' => 'Italy',
            'iso_code' => 'IT',
            'subregion_id' => $southernEurope->id
        ]);

        $france = Country::create([
            'name' => 'France',
            'iso_code' => 'FR',
            'subregion_id' => $westernEurope->id
        ]);

        $japan = Country::create([
            'name' => 'Japan',
            'iso_code' => 'JP',
            'subregion_id' => $eastAsia->id
        ]);

        // Create regions
        $tuscany = Region::create([
            'name' => 'Tuscany',
            'country_id' => $italy->id
        ]);

        $lombardy = Region::create([
            'name' => 'Lombardy',
            'country_id' => $italy->id
        ]);

        $provence = Region::create([
            'name' => 'Provence',
            'country_id' => $france->id
        ]);

        $kansai = Region::create([
            'name' => 'Kansai',
            'country_id' => $japan->id
        ]);

        // Create cities
        $florence = City::create([
            'name' => 'Florence',
            'region_id' => $tuscany->id
        ]);

        $siena = City::create([
            'name' => 'Siena',
            'region_id' => $tuscany->id
        ]);

        $milan = City::create([
            'name' => 'Milan',
            'region_id' => $lombardy->id
        ]);

        $nice = City::create([
            'name' => 'Nice',
            'region_id' => $provence->id
        ]);

        $kyoto = City::create([
            'name' => 'Kyoto',
            'region_id' => $kansai->id
        ]);

        // Create packages
        $italianPackage = Package::create([
            'title' => ['en' => 'Italian Adventure'],
            'slug' => ['en' => 'italian-adventure'],
            'published' => true,
            'price' => 199900, // $1,999.00
            'duration' => 7,
        ]);

        $europeanPackage = Package::create([
            'title' => ['en' => 'European Discovery'],
            'slug' => ['en' => 'european-discovery'],
            'published' => true,
            'price' => 299900, // $2,999.00
            'duration' => 14,
        ]);

        $asianPackage = Package::create([
            'title' => ['en' => 'Asian Experience'],
            'slug' => ['en' => 'asian-experience'],
            'published' => true,
            'price' => 399900, // $3,999.00
            'duration' => 10,
        ]);

        $worldPackage = Package::create([
            'title' => ['en' => 'World Tour'],
            'slug' => ['en' => 'world-tour'],
            'published' => true,
            'price' => 999900, // $9,999.00
            'duration' => 30,
        ]);

        // Attach cities to packages
        $italianPackage->cities()->attach([$florence->id, $siena->id, $milan->id]);
        $europeanPackage->cities()->attach([$florence->id, $nice->id]);
        $asianPackage->cities()->attach([$kyoto->id]);
        $worldPackage->cities()->attach([$florence->id, $nice->id, $kyoto->id]);
    }
}
