<?php

namespace Database\Seeders\Tests;

use App\Models\Alias;
use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Package;
use App\Models\Region;
use App\Models\Subregion;
use Illuminate\Database\Seeder;

class GeographyTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create continents
        $europe = Continent::create(['name' => 'Europe']);
        $asia = Continent::create(['name' => 'Asia']);

        // Create subregions
        $southernEurope = Subregion::create([
            'name' => 'Southern Europe',
            'continent_id' => $europe->id
        ]);

        $westernEurope = Subregion::create([
            'name' => 'Western Europe',
            'continent_id' => $europe->id
        ]);

        $eastAsia = Subregion::create([
            'name' => 'East Asia',
            'continent_id' => $asia->id
        ]);

        // Create countries
        $italy = Country::create([
            'name' => 'Italy',
            'iso_code' => 'IT',
            'subregion_id' => $southernEurope->id
        ]);

        $france = Country::create([
            'name' => 'France',
            'iso_code' => 'FR',
            'subregion_id' => $westernEurope->id
        ]);

        $japan = Country::create([
            'name' => 'Japan',
            'iso_code' => 'JP',
            'subregion_id' => $eastAsia->id
        ]);

        // Create regions
        $tuscany = Region::create([
            'name' => 'Tuscany',
            'country_id' => $italy->id
        ]);

        $lombardy = Region::create([
            'name' => 'Lombardy',
            'country_id' => $italy->id
        ]);

        $provence = Region::create([
            'name' => 'Provence',
            'country_id' => $france->id
        ]);

        $kansai = Region::create([
            'name' => 'Kansai',
            'country_id' => $japan->id
        ]);

        // Create cities
        $florence = City::create([
            'name' => 'Florence',
            'region_id' => $tuscany->id
        ]);

        $siena = City::create([
            'name' => 'Siena',
            'region_id' => $tuscany->id
        ]);

        $milan = City::create([
            'name' => 'Milan',
            'region_id' => $lombardy->id
        ]);

        $nice = City::create([
            'name' => 'Nice',
            'region_id' => $provence->id
        ]);

        $kyoto = City::create([
            'name' => 'Kyoto',
            'region_id' => $kansai->id
        ]);

        // Create packages
        $italianPackage = Package::create([
            'title' => ['en' => 'Italian Adventure'],
            'slug' => ['en' => 'italian-adventure'],
            'published' => true,
            'price' => 199900, // $1,999.00
            'duration' => 7,
        ]);

        $europeanPackage = Package::create([
            'title' => ['en' => 'European Discovery'],
            'slug' => ['en' => 'european-discovery'],
            'published' => true,
            'price' => 299900, // $2,999.00
            'duration' => 14,
        ]);

        $asianPackage = Package::create([
            'title' => ['en' => 'Asian Experience'],
            'slug' => ['en' => 'asian-experience'],
            'published' => true,
            'price' => 399900, // $3,999.00
            'duration' => 10,
        ]);

        $worldPackage = Package::create([
            'title' => ['en' => 'World Tour'],
            'slug' => ['en' => 'world-tour'],
            'published' => true,
            'price' => 999900, // $9,999.00
            'duration' => 30,
        ]);

        // Attach cities to packages
        $italianPackage->cities()->attach([$florence->id, $siena->id, $milan->id]);
        $europeanPackage->cities()->attach([$florence->id, $nice->id]);
        $asianPackage->cities()->attach([$kyoto->id]);
        $worldPackage->cities()->attach([$florence->id, $nice->id, $kyoto->id]);

        // Create aliases for all geographic entities
        $this->createAliases();
    }

    /**
     * Create aliases for all geographic entities
     */
    private function createAliases(): void
    {
        // Continent aliases
        $europe = Continent::where('name', 'Europe')->first();
        $asia = Continent::where('name', 'Asia')->first();

        $europe->aliases()->createMany([
            ['name' => 'EU'],
            ['name' => 'Europa'],
            ['name' => 'European Continent'],
            ['name' => 'Old World']
        ]);

        $asia->aliases()->createMany([
            ['name' => 'AS'],
            ['name' => 'Asian Continent'],
            ['name' => 'Orient'],
            ['name' => 'Far East']
        ]);

        // Subregion aliases
        $southernEurope = Subregion::where('name', 'Southern Europe')->first();
        $westernEurope = Subregion::where('name', 'Western Europe')->first();
        $eastAsia = Subregion::where('name', 'East Asia')->first();

        $southernEurope->aliases()->createMany([
            ['name' => 'South Europe'],
            ['name' => 'Mediterranean Europe'],
            ['name' => 'Southern EU'],
            ['name' => 'Sud Europa']
        ]);

        $westernEurope->aliases()->createMany([
            ['name' => 'West Europe'],
            ['name' => 'Western EU'],
            ['name' => 'Atlantic Europe'],
            ['name' => 'Ouest Europe']
        ]);

        $eastAsia->aliases()->createMany([
            ['name' => 'Eastern Asia'],
            ['name' => 'Far East Asia'],
            ['name' => 'Pacific Asia'],
            ['name' => 'Northeast Asia']
        ]);

        // Country aliases
        $italy = Country::where('name', 'Italy')->first();
        $france = Country::where('name', 'France')->first();
        $japan = Country::where('name', 'Japan')->first();

        $italy->aliases()->createMany([
            ['name' => 'IT'],
            ['name' => 'Italia'],
            ['name' => 'Italian Republic'],
            ['name' => 'Repubblica Italiana'],
            ['name' => 'Boot Country']
        ]);

        $france->aliases()->createMany([
            ['name' => 'FR'],
            ['name' => 'French Republic'],
            ['name' => 'République française'],
            ['name' => 'Hexagon'],
            ['name' => 'Gaul']
        ]);

        $japan->aliases()->createMany([
            ['name' => 'JP'],
            ['name' => 'Nippon'],
            ['name' => 'Nihon'],
            ['name' => 'Land of Rising Sun'],
            ['name' => '日本']
        ]);

        // Region aliases
        $tuscany = Region::where('name', 'Tuscany')->first();
        $lombardy = Region::where('name', 'Lombardy')->first();
        $provence = Region::where('name', 'Provence')->first();
        $kansai = Region::where('name', 'Kansai')->first();

        $tuscany->aliases()->createMany([
            ['name' => 'Toscana'],
            ['name' => 'Tuscan Region'],
            ['name' => 'Heart of Italy'],
            ['name' => 'Renaissance Land']
        ]);

        $lombardy->aliases()->createMany([
            ['name' => 'Lombardia'],
            ['name' => 'Lombard Region'],
            ['name' => 'Northern Italy'],
            ['name' => 'Milan Region']
        ]);

        $provence->aliases()->createMany([
            ['name' => 'Provence-Alpes-Côte d\'Azur'],
            ['name' => 'PACA'],
            ['name' => 'French Riviera'],
            ['name' => 'Côte d\'Azur']
        ]);

        $kansai->aliases()->createMany([
            ['name' => 'Kinki'],
            ['name' => 'Kansai Region'],
            ['name' => 'Western Japan'],
            ['name' => '関西']
        ]);

        // City aliases
        $florence = City::where('name', 'Florence')->first();
        $siena = City::where('name', 'Siena')->first();
        $milan = City::where('name', 'Milan')->first();
        $nice = City::where('name', 'Nice')->first();
        $kyoto = City::where('name', 'Kyoto')->first();

        $florence->aliases()->createMany([
            ['name' => 'Firenze'],
            ['name' => 'Cradle of Renaissance'],
            ['name' => 'Athens of Middle Ages'],
            ['name' => 'City of Lilies']
        ]);

        $siena->aliases()->createMany([
            ['name' => 'Medieval Gem'],
            ['name' => 'Palio City'],
            ['name' => 'Tuscan Hill Town'],
            ['name' => 'Sienna']
        ]);

        $milan->aliases()->createMany([
            ['name' => 'Milano'],
            ['name' => 'Fashion Capital'],
            ['name' => 'Design Capital'],
            ['name' => 'Economic Capital']
        ]);

        $nice->aliases()->createMany([
            ['name' => 'Nizza'],
            ['name' => 'Queen of Riviera'],
            ['name' => 'Pearl of French Riviera'],
            ['name' => 'Côte d\'Azur Capital']
        ]);

        $kyoto->aliases()->createMany([
            ['name' => '京都'],
            ['name' => 'Ancient Capital'],
            ['name' => 'City of Temples'],
            ['name' => 'Cultural Heart']
        ]);
    }
}
