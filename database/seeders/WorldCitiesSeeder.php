<?php

namespace Database\Seeders;

use App\Models\Country;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class WorldCitiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // first, empty the tables
        // note that it is important to delete the "child" tables first to respect foreign key constraints
        DB::table('cities')->delete();
        DB::table('regions')->delete();
//        DB::table('subregions')->delete();
//        DB::table('subregions')->delete();
//        DB::table('continents')->delete();

        $json = File::get(database_path('seeders/data/cities.json'));
        $cities = json_decode($json, true);

        echo "Total cities: " . count($cities) . PHP_EOL;


        // manual hacks
        // netherlands
        $netherlands = Country::where('name', 'Ολλανδία, Βασίλειο του')->first();
        if (!empty($netherlands))
        {
            $netherlands->update(['name' => 'Ολλανδία']);
            echo 'Netherlands fixed' . PHP_EOL . "\n";
        }
        // tonga
        $tonga = Country::where('name', 'Έφτασε')->first();
        if (!empty($tonga))
        {
            $tonga->update(['name' => 'Τόνγκα']);
            echo 'Tonga fixed' . PHP_EOL . "\n";
        }
        // venezuela
        $venezuela = Country::where('name', 'Βενεζουέλα, Μπολιβαριανή Δημοκρατία της')->first();
        if (!empty($venezuela))
        {
            $venezuela->update(['name' => 'Βενεζουέλα']);
            echo 'Venezuela fixed' . PHP_EOL . "\n";
        }
        // bolivia
        $bolivia = Country::where('name', 'Βολιβία, Πολυεθνικό Κράτος')->first();
        if (!empty($bolivia))
        {
            $bolivia->update(['name' => 'Βολιβία']);
            echo 'Bolivia fixed' . PHP_EOL . "\n";
        }
        // yemen
        $yemen = Country::where('name', 'Γέμενη')->first();
        if (!empty($yemen))
        {
            $yemen->update(['name' => 'Υεμένη']);
            echo 'Yemen fixed' . PHP_EOL . "\n";
        }
        // united kingdom
        $uk = Country::where('name', 'Ηνωμένο Βασίλειο της Μεγάλης Βρετανίας και της Βόρειας Ιρλανδίας')->first();
        if (!empty($uk))
        {
            $uk->update(['name' => 'Ηνωμένο Βασίλειο Βρετανίας']);
            echo 'UK fixed' . PHP_EOL . "\n";
        }
        // jamaica
        $jamaica = Country::where('name', 'Ιαμαϊκή')->first();
        if (!empty($jamaica))
        {
            $jamaica->update(['name' => 'Τζαμάικα']);
            echo 'Jamaica fixed' . PHP_EOL . "\n";
        }
        // iran
        $iran = Country::where('name', 'Ιράν, Ισλαμική Δημοκρατία του')->first();
        if (!empty($iran))
        {
            $iran->update(['name' => 'Ιράν']);
            echo 'Iran fixed' . PHP_EOL . "\n";
        }
        // kongo
        $kongo = Country::where('name', 'Κονγκό, Λαϊκή Δημοκρατία του')->first();
        if (!empty($kongo))
        {
            $kongo->update(['name' => 'Κονγκό']);
            echo 'Kongo fixed' . PHP_EOL . "\n";
        }
        // south korea
        $southKorea = Country::where('name', 'Κορέα, Δημοκρατία της')->first();
        if (!empty($southKorea))
        {
            $southKorea->update(['name' => 'Νότια Κορέα']);
            echo 'South Korea fixed' . PHP_EOL . "\n";
        }
        // north korea
        $northKorea = Country::where('name', 'Κορέα, Λαϊκή Δημοκρατία της Κορέας')->first();
        if (!empty($northKorea))
        {
            $northKorea->update(['name' => 'Βόρεια Κορέα']);
            echo 'North Korea fixed' . PHP_EOL . "\n";
        }
        // curacao
        $curacao = Country::where('name', 'Κουράσω')->first();
        if (!empty($curacao))
        {
            $curacao->update(['name' => 'Κουρασάο']);
            echo 'Curacao fixed' . PHP_EOL . "\n";
        }
        // laos
        $laos = Country::where('name', 'Λαϊκή Δημοκρατία του Λάος')->first();
        if (!empty($laos))
        {
            $laos->update(['name' => 'Λάος']);
            echo 'Laos fixed' . PHP_EOL . "\n";
        }
        // micronesia
        $micronesia = Country::where('name', 'Μικρονησία, Ομόσπονδες Πολιτείες')->first();
        if (!empty($micronesia))
        {
            $micronesia->update(['name' => 'Μικρονησία']);
            echo 'Micronesia fixed' . PHP_EOL . "\n";
        }
        // moldavia
        $moldavia = Country::where('name', 'Μολδαβία, Δημοκρατία της')->first();
        if (!empty($moldavia))
        {
            $moldavia->update(['name' => 'Μολδαβία']);
            echo 'Moldavia fixed' . PHP_EOL . "\n";
        }
        // palestine
        $palestine = Country::where('name', 'Παλαιστίνη, Κράτος του')->first();
        if (!empty($palestine))
        {
            $palestine->update(['name' => 'Παλαιστίνη']);
            echo 'Palestine fixed' . PHP_EOL . "\n";
        }
        // syria
        $syria = Country::where('name', 'Συριακή Αραβική Δημοκρατία')->first();
        if (!empty($syria))
        {
            $syria->update(['name' => 'Συρία']);
            echo 'Syria fixed' . PHP_EOL . "\n";
        }
        // tanzania
        $tanzania = Country::where('name', 'Τανζανία, Ηνωμένη Δημοκρατία της')->first();
        if (!empty($tanzania))
        {
            $tanzania->update(['name' => 'Τανζανία']);
            echo 'Tanzania fixed' . PHP_EOL . "\n";
        }
        // czech republic
        $czechRepublic = Country::where('name', 'Τσεχική Δημοκρατία')->first();
        if (!empty($czechRepublic))
        {
            $czechRepublic->update(['name' => 'Τσεχία']);
            echo 'Czech Republic fixed' . PHP_EOL . "\n";
        }
        // chile
        $chile = Country::where('name', 'χιλή')->first();
        if (!empty($chile))
        {
            $chile->update(['name' => 'Χιλή']);
            echo 'Chile fixed' . PHP_EOL . "\n";
        }
        // thailand
        $thailand = Country::where('name', 'Σιάμ')->first();
        if (!empty($thailand))
        {
            $thailand->update(['name' => 'Ταϊλάνδη']);
            echo 'Thailand fixed' . PHP_EOL . "\n";
        }
        // st martin
        $stMartin = Country::where('name', 'Άγιος Μαρτίνος (γαλλικό τμήμα)')->first();
        if (!empty($stMartin))
        {
            $stMartin->update(['name' => 'Άγιος Μαρτίνος']);
            echo 'St Martin fixed' . PHP_EOL . "\n";
        }
        // barbados
        $barbados = Country::where('name', 'Μπαρμπάντος')->first();
        if (!empty($barbados))
        {
            $barbados->update(['name' => 'Μπαρμπέιντος']);
            echo 'Barbados fixed' . PHP_EOL . "\n";
        }
        // british virgin islands
        $britishVirginIslands = Country::where('name', 'Παρθένοι Νήσοι (Βρετανικά)')->first();
        if (!empty($britishVirginIslands))
        {
            $britishVirginIslands->update(['name' => 'Βρετανικές Παρθένοι Νήσοι']);
            echo 'British Virgin Islands fixed' . PHP_EOL . "\n";
        }

        // Caches to avoid duplicate inserts
        $continentMap   = [];
        $subregionMap   = [];
        $countryMap     = [];
        $regionMap      = [];

        foreach ($cities as $entry)
        {
            // Skip if essential values are missing
            if (
                empty($entry['continent'])
                || empty($entry['subregion'])
                || empty($entry['country'])
                || empty($entry['region'])
                || empty($entry['city'])
            )
            {
                echo "Missing values for " . $entry['city'] . PHP_EOL . "\n";
                continue;
            }

            // === Handle Continent ===
//            $continentName = $entry['continent'];
//            // create the continent if it doesn't exist in the cache
//            if (!isset($continentMap[$continentName]))
//            {
////                $continentId = DB::table('continents')->insertGetId([
////                    'name'          => $continentName,
////                    'created_at'    => now(),
////                    'updated_at'    => now(),
////                ]);
////                $continentMap[$continentName] = $continentId;
//                $continentMap[$continentName] = 1;
//            }
//            // else get the id from the cache
//            $continentId = $continentMap[$continentName];

            // === Handle Subregion ===
//            $subregionName = $entry['subregion'];
//            // create the subregion if it doesn't exist in the cache
//            if (!isset($subregionMap[$subregionName]))
//            {
////                $subregionId = DB::table('subregions')->insertGetId([
////                    'name'          => $subregionName,
////                    'continent_id'  => $continentId,
////                    'created_at'    => now(),
////                    'updated_at'    => now(),
////                ]);
////                $subregionMap[$subregionName] = $subregionId;
//                $subregionMap[$subregionName] = 2;
//            }
//            // else get the id from the cache
//            $subregionId = $subregionMap[$subregionName];

            // === Handle Country ===
            $countryName = $entry['country'];

            if (!isset($countryMap[$countryName]))
            {
                // fetch country from db
                $countryId = Country::where('name', $countryName)->first();

                if(empty($countryId))
                {
                    echo "Country not found: " . $countryName . PHP_EOL . "\n";
                    continue;
                }
                $countryMap[$countryName] = $countryId->id;
            }
            // else get the id from the cache
            $countryId = $countryMap[$countryName];

            // === Handle Region ===
            $regionName = $entry['region'];
            // create the region if it doesn't exist in the cache
            if (!isset($regionMap[$regionName]))
            {
                $regionId = DB::table('regions')->insertGetId([
                    'name'          => $regionName,
                    'country_id'    => $countryId,
                    'created_at'    => now(),
                    'updated_at'    => now(),
                ]);
                $regionMap[$regionName] = $regionId;
            }
            // else get the id from the cache
            $regionId = $regionMap[$regionName];

            // === Insert City ===
            DB::table('cities')->insert([
                'name'          => $entry['city'],
                'region_id'  => $regionId,
                'created_at'    => now(),
                'updated_at'    => now(),
            ]);
        }

//            dd($continentMap);
//            dd($subregionMap);
//            dd($countryMap);
//            dd($regionMap);
    }
}
