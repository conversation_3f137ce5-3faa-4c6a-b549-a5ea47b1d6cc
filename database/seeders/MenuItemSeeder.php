<?php

namespace Database\Seeders;

use App\Models\MenuItem;
use Illuminate\Database\Seeder;

class MenuItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $itemsGr = [
            [
                'menu_id' => 1,
                'title' => 'Προορισμοί',
                'type' => 'travel_index',
                'link' => '/travel',
                'order' => 1
            ],
            [
                'menu_id' => 1,
                'title' => 'Περίοδος / Εορτές',
                'type' => null,
                'link' => null,
                'order' => 2
            ],
            [
                'menu_id' => 1,
                'title' => 'Food Tours',
                'type' => null,
                'link' => null,
                'order' => 3
            ],
            [
                'menu_id' => 1,
                'title' => 'Κρουαζιέρες',
                'type' => null,
                'link' => null,
                'order' => 4
            ],
            [
                'menu_id' => 1,
                'title' => 'Blog',
                'type' => 'posts_index',
                'link' => '/blog',
                'order' => 4
            ],
            [
                'menu_id' => 1,
                'title' => 'Επικοινωνία',
                'type' => null,
                'link' => null,
                'order' => 4
            ],
        ];

        $itemsEn = [
            [
                'menu_id' => 2,
                'title' => 'Destinations',
                'type' => 'travel_index',
                'link' => '/travel',
                'order' => 1
            ],
            [
                'menu_id' => 2,
                'title' => 'Seasons / Holidays',
                'type' => null,
                'link' => null,
                'order' => 2
            ],
            [
                'menu_id' => 2,
                'title' => 'Food Tours',
                'type' => null,
                'link' => null,
                'order' => 3
            ],
            [
                'menu_id' => 2,
                'title' => 'Cruises',
                'type' => null,
                'link' => null,
                'order' => 4
            ],
            [
                'menu_id' => 2,
                'title' => 'Blog',
                'type' => 'posts_index',
                'link' => '/blog',
                'order' => 4
            ],
            [
                'menu_id' => 2,
                'title' => 'Επικοινωνία',
                'type' => null,
                'link' => null,
                'order' => 4
            ],
        ];

        foreach ($itemsGr as $item) {
            
            MenuItem::updateOrCreate(
                ['menu_id' => 1, 'title' => $item['title']],
                $item + [
                    'published' => 1,
                ]
            );
        }
       
        foreach ($itemsEn as $item) {
            
            MenuItem::updateOrCreate(
                ['menu_id' => 2, 'title' => $item['title']],
                $item + [
                    'published' => 1,
                ]
            );
        }
    }
}
