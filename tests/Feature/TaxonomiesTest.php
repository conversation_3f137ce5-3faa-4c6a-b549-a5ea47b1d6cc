<?php

namespace Tests\Feature;

use App\Models\Tag;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TaxonomiesTest extends TestCase
{

    use RefreshDatabase, WithFaker;

    private $admin;

    protected function setUp(): void
    {
        parent::setUp();

        $this->withoutExceptionHandling();

        $this->admin = User::factory()->create(['type' => 'admin']);
    }

    /**
     * A basic feature test example.
     */
    public function test_non_admin_can_see_tags_index(): void
    {
        $response = $this->actingAs($this->admin)->get('/cerby/tags');

        $response->assertStatus(200);
    }

    /**
     * A basic feature test example.
     */
    public function test_non_admin_can_see_categories_index(): void
    {
        $response = $this->actingAs($this->admin)->get('/cerby/categories');

        $response->assertStatus(200);
    }

    /** test */
    public function test_admin_can_create_category()
    {
        $attributes = [
            'title' => [
                'el' => 'Category A'
            ],
    
        ];
        $response = $this->actingAs($this->admin)->post(route('cerberus.categories.store'), $attributes);

        $response->assertStatus(302);
        $this->assertDatabaseHas('tags', ['title->el' => $attributes['title']['el']]);
    }

    /** test */
    public function test_admin_can_create_tag()
    {
        $attributes = [
            'title' => [
                'el' => 'Tag A'
            ],
    
        ];
        $response = $this->actingAs($this->admin)->post(route('cerberus.tags.store'), $attributes);

        $response->assertStatus(302);
        $this->assertDatabaseHas('tags', ['title->el' => $attributes['title']['el']]);
    }

    /** test */
    public function test_admin_can_update_tag_or_category()
    {
        $tag = Tag::create([
            'title' => ['el' => 'title' ]
            
        ]);

        $response = $this->actingAs($this->admin)->patch(route('cerberus.tags.update', $tag), ['title' => ['el' => 'New Title'], 'slug' => ['el' => 'new-slug']]);

        $response->assertStatus(302);
    }
    

    /** test */
    public function test_admin_can_see_packages_ordering_page_of_category_or_tag()
    {
        $category = Tag::factory()->create();

        $response = $this->actingAs($this->admin)->get( route('cerberus.packages.order', $category->id));

        $response->assertStatus(200);
    }
    
}
