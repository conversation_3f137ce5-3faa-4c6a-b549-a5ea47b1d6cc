<?php

namespace Tests\Feature\Cerby;

use App\Models\Package;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TravelPackagesTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private $admin;

    protected function setUp(): void
    {
        parent::setUp();

        $this->withoutExceptionHandling();

        $this->admin = User::factory()->create(['type' => 'admin']);
    }
    /**
     * A basic feature test example.
     */
    public function test_admin_can_see_travel_packages_index(): void
    {
        $response = $this->actingAs($this->admin)->get('/cerby/packages');

        $response->assertStatus(200);
    }

    /** test */
    public function test_admin_can_create_travel_package()
    {
        $response = $this->actingAs($this->admin)->get('/cerby/packages/create');

        $response->assertStatus(200);
    }

    /** test */
    public function test_admin_can_store_travel_package()
    {

        $title = 'Package Title';

        $attributes = [
            'title' => [
                'el' => $title,
            ],
        ];

        $response = $this->actingAs($this->admin)->post(route('cerberus.packages.store'), $attributes);

        $this->assertDatabaseHas('packages', ['title->el' => $title]);

        $response->assertStatus(302);
        $response->assertRedirect(route('cerberus.packages.edit', 1));
    }

    /** test */
    public function test_admin_can_edit_travel_packages()
    {
        $title = 'Travel Package';

        $package = Package::create(['title' => $title]);

        $response = $this->actingAs($this->admin)->get(route('cerberus.packages.edit', $package));

        $response->assertStatus(200);
    }

    /** test */
    public function test_admin_can_update_package()
    {
        $package = Package::create([
            'title' => [
                'el' => 'Package Title',

            ]
        ]);

        $attributes = [
            'type' => 'travel',
            'title' => [
                'el' => 'Τίτλος Πακέτου',
            ],
            'slug' => [
                'el' => $package->slug
            ],
            'locale' => 'el',
            'tagline' => [
                'el' => 'Greek Tagline'
            ],
            'content' => [
                'el' => 'Greek Content for Package'
            ]
        ];

        $response = $this->actingAs($this->admin)->patch(route('cerberus.packages.update', $package), $attributes);

        $this->assertDatabaseHas('packages', ['content->el' => $attributes['content']['el']]);

        $response->assertStatus(302);


    }

    /** test */
    public function test_admin_can_delete_package()
    {
        $package = Package::create([
            'title' => [
                'el' => 'Package Title',

            ]
        ]);

        $response = $this->actingAs($this->admin)->delete(route('cerberus.packages.destroy', $package));

        $response->assertStatus(302);
    }
    
}
