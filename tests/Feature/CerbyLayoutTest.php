<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CerbyLayoutTest extends TestCase
{

    use RefreshDatabase;

    private $admin;

    protected function setUp(): void
    {
        parent::setUp();
    
        $this->admin = User::factory()->create(['type' => 'admin']);
    }
    

    /**
     * A basic feature test example.
     */
    public function test_non_admins_cannot_see_dashboard(): void
    {
        $response = $this->get('/cerby');

        $response->assertStatus(302);
    }

    /** test */
    public function test_admin_can_see_dashboard()
    {   

        $response = $this->actingAs($this->admin)->get('/cerby');

        $response->assertStatus(200);

    }


}
