<?php

namespace Tests\Feature;

use App\Models\Alias;
use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Region;
use App\Models\Subregion;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AutoAliasableTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that creating a continent automatically creates an alias
     */
    public function test_continent_creation_creates_auto_alias(): void
    {
        $continent = Continent::create(['name' => 'North America']);

        // Check that an alias was automatically created
        $alias = Alias::where('name', 'North America')
            ->where('aliasable_type', Continent::class)
            ->where('aliasable_id', $continent->id)
            ->first();

        $this->assertNotNull($alias);
        $this->assertEquals('North America', $alias->name);
        $this->assertEquals($continent->id, $alias->aliasable_id);
        $this->assertEquals(Continent::class, $alias->aliasable_type);
    }

    /**
     * Test that updating a continent name creates/updates the auto alias
     */
    public function test_continent_name_update_creates_auto_alias(): void
    {
        $continent = Continent::create(['name' => 'North America']);

        // Update the continent name
        $continent->update(['name' => 'North American Continent']);

        // Check that the alias was updated
        $alias = Alias::where('name', 'North American Continent')
            ->where('aliasable_type', Continent::class)
            ->where('aliasable_id', $continent->id)
            ->first();

        $this->assertNotNull($alias);
        $this->assertEquals('North American Continent', $alias->name);

        // Check that the old alias no longer exists
//        $oldAlias = Alias::where('name', 'North America')
//            ->where('aliasable_type', Continent::class)
//            ->first();
//
//        $this->assertNull($oldAlias);
    }

    /**
     * Test that updating a continent without changing name doesn't create duplicate aliases
     */
    public function test_continent_update_without_name_change_no_duplicate_alias(): void
    {
        $continent = Continent::create(['name' => 'South America']);

        // Count initial aliases
        $initialAliasCount = Alias::where('name', 'South America')
            ->where('aliasable_type', Continent::class)
            ->count();

        $this->assertEquals(1, $initialAliasCount);

        // Update something other than name (this would be another field if it existed)
        $continent->touch(); // This triggers updated event but doesn't change name

        // Count aliases after update
        $finalAliasCount = Alias::where('name', 'South America')
            ->where('aliasable_type', Continent::class)
            ->count();

        $this->assertEquals(1, $finalAliasCount);
    }

    /**
     * Test auto alias creation for all geography types
     */
    public function test_auto_alias_creation_for_all_geography_types(): void
    {
        // Create a full geography hierarchy
        $continent = Continent::create(['name' => 'Test Continent']);
        $subregion = Subregion::create(['name' => 'Test Subregion', 'continent_id' => $continent->id]);
        $country = Country::create(['name' => 'Test Country', 'subregion_id' => $subregion->id]);
        $region = Region::create(['name' => 'Test Region', 'country_id' => $country->id]);
        $city = City::create(['name' => 'Test City', 'region_id' => $region->id]);

        // Check that aliases were created for all
        $continentAlias = Alias::where('name', 'Test Continent')
            ->where('aliasable_type', Continent::class)
            ->first();
        $this->assertNotNull($continentAlias);

        $subregionAlias = Alias::where('name', 'Test Subregion')
            ->where('aliasable_type', Subregion::class)
            ->first();
        $this->assertNotNull($subregionAlias);

        $countryAlias = Alias::where('name', 'Test Country')
            ->where('aliasable_type', Country::class)
            ->first();
        $this->assertNotNull($countryAlias);

        $regionAlias = Alias::where('name', 'Test Region')
            ->where('aliasable_type', Region::class)
            ->first();
        $this->assertNotNull($regionAlias);

        $cityAlias = Alias::where('name', 'Test City')
            ->where('aliasable_type', City::class)
            ->first();
        $this->assertNotNull($cityAlias);
    }

    /**
     * Test that empty names don't create aliases
     */
    public function test_empty_name_does_not_create_alias(): void
    {
        // This should not be possible with validation, but test the trait behavior
        $continent = new Continent();
        $continent->name = '';
        $continent->save();

        $alias = Alias::where('aliasable_type', Continent::class)
            ->where('aliasable_id', $continent->id)
            ->first();

        $this->assertNull($alias);
    }

    /**
     * Test that updateOrCreate prevents duplicate aliases with same name and type
     */
    public function test_update_or_create_prevents_duplicates(): void
    {
        // Create a continent and a country with the same name (different types)
        $continent = Continent::create(['name' => 'Same Name']);
        $subregion = Subregion::create(['name' => 'Test Subregion', 'continent_id' => $continent->id]);
        $country = Country::create(['name' => 'Same Name', 'subregion_id' => $subregion->id]);

        // Check that we have exactly 2 aliases (one for continent, one for country)
        $aliasCount = Alias::where('name', 'Same Name')->count();
        $this->assertEquals(2, $aliasCount);

        // Check that we have one alias for each type
        $continentAliasCount = Alias::where('name', 'Same Name')
            ->where('aliasable_type', Continent::class)
            ->count();
        $this->assertEquals(1, $continentAliasCount);

        $countryAliasCount = Alias::where('name', 'Same Name')
            ->where('aliasable_type', Country::class)
            ->count();
        $this->assertEquals(1, $countryAliasCount);

        // Update the continent's name to the same value (should not create duplicate)
        $continent->update(['name' => 'Same Name']);

        // Should still have exactly 2 aliases total
        $aliasCount = Alias::where('name', 'Same Name')->count();
        $this->assertEquals(2, $aliasCount);
    }

    /**
     * Test that the auto-created alias can be retrieved through the aliasable relationship
     */
    public function test_auto_alias_relationship_works(): void
    {
        $continent = Continent::create(['name' => 'Test Continent']);
        $subregion = Subregion::create(['name' => 'Test Subregion', 'continent_id' => $continent->id]);
        $country = Country::create(['name' => 'Test Country', 'subregion_id' => $subregion->id]);

        $alias = Alias::where('name', 'Test Country')
            ->where('aliasable_type', Country::class)
            ->first();

        $this->assertNotNull($alias);
        $this->assertInstanceOf(Country::class, $alias->aliasable);
        $this->assertEquals($country->id, $alias->aliasable->id);
        $this->assertEquals('Test Country', $alias->aliasable->name);
    }
}
