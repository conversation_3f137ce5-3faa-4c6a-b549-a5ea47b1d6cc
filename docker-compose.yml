version: '3.5'
services:
  lara_travel:
    container_name: www.lara_travel.local
    build:
      context: .
      dockerfile: docker/app/Dockerfile
      args:
        - PUID=1000
    volumes:
      - lara_travel_cache:/var/www/.composer:rw # composer cache
      - .:/var/www/html:rw
      - ./docker/app/prepare-container.sh:/dde/prepare-container.sh # runtime preparations after volumes have been mounted
    depends_on:
      - lara_travel_db
    networks:
      - n1
    environment:
      - VIRTUAL_HOST=lara_travel.local,www.lara_travel.local
      - VIRTUAL_PORT=80
      - COMPOSER_MEMORY_LIMIT=-1
    entrypoint: /usr/local/bin/custom-entrypoint.sh
  lara_travel_db:
    build:
      context: .
      dockerfile: docker/db/Dockerfile
    container_name: db.lara_travel.local
    environment:
      - MYSQL_ROOT_PASSWORD=0
      - MYSQL_DATABASE=lara_travel
      - VIRTUAL_HOST=db.lara_travel.local
      - VIRTUAL_PORT=3306
      - TZ=Europe/Athens
    networks:
      - n1
    volumes:
      - lara_travel_db_data:/var/lib/mysql
      - ./docker/db/conf/mysqld.cnf:/etc/mysql/mysql.conf.d/mysqld.cnf # runtime add conf file
    ports:
      - "33371:3306"
  lara_travel_proxy:
    image: jwilder/nginx-proxy
    container_name: proxy.lara_travel.local
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/proxy/nginx.conf:/etc/nginx/nginx.conf
      - /var/run/docker.sock:/tmp/docker.sock:ro
    networks:
      - n1
    logging:
      driver: "json-file"
      options:
        max-size: "2m"
networks:
  n1:
    name: dockerdevenv_default
    external: true
volumes:
  lara_travel_cache:
  lara_travel_db_data:

