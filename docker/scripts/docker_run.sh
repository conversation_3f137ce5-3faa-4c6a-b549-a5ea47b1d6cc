#!/bin/bash

CONTAINER="www.lara_travel.local"
CLICOMMAND="sh"
ARRGS=""
echo "What do you want to do?";
echo "1. Composer install";
echo "2. Composer require package";
echo "3. Run artisan migrate";
echo "4. Run artisan command";
echo "5. Run npm ci";
echo "6. Run vite build";
echo "7. Run any command";
echo "8. Create DB";
echo "9. Drop DB";


if [ "$1" == 1 ] || [ "$1" == 2 ] || [ "$1" == 3 ] || [ "$1" == 4 ] || [ "$1" == 5 ] || [ "$1" == 6 ] || [ "$1" == 7 ] || [ "$1" == 8 ] || [ "$1" == 9 ]
  then
    action=$1
else
    read action;
fi;

if [ "$action" == 1 ]
	then
		echo "Running composer install..."
		COMMAND="docker_composer_install.sh";
elif [ "$action" == 2 ]
	then
    echo "What package do you wish to install?"
    read ARRGS
    echo "Running composer require $ARRGS..."
    COMMAND="docker_composer_require.sh";
elif [ "$action" == 3 ]
	then
		COMMAND="docker_artisan_migrate.sh";
elif [ "$action" == 4 ]
	then
    echo "What php artisan :command do you wish to run? (Type it in double quotes)"
    read ARRGS
    echo "Running php artisan $ARRGS..."
    COMMAND="docker_artisan_command.sh";
elif [ "$action" == 5 ]
	then
		echo "Running npm ci..."
		COMMAND="docker_npm_install.sh";
elif [ "$action" == 6 ]
	then
		echo "Running npm build..."
		COMMAND="docker_npm_build.sh";
elif [ "$action" == 7 ]
	then
    echo "What :command do you wish to run? (Type it in double quotes)"
    read ARRGS
    echo "Running $ARRGS..."
    COMMAND="docker_command.sh";
elif [ "$action" == 8 ]
	then
		echo "Creating DB..."
		CLICOMMAND="php"
		COMMAND="docker_create_db.php";
elif [ "$action" == 9 ]
	then
		echo "Destroying DB..."
		CLICOMMAND="php"
		COMMAND="docker_destroy_db.php";
else
    echo "Invalid option.";
    exit;
fi;

# check OS
if [ -d "/c/Program Files/Docker/Docker/Resources/bin" ]; then
    DOCKER_COMMAND=""
elif [ -d /c/windows/system32 ]; then
    cd "C:\Program Files\Docker Toolbox"
    DOCKER_COMMAND=start.sh
else
    DOCKER_COMMAND=""
fi

# Try to find running docker image
echo "Trying to locate docker image: $CONTAINER"
DOCKER_IMAGE=`$DOCKER_COMMAND docker ps | grep -w $CONTAINER | awk '{print $1}'`

if [[ -z "$DOCKER_IMAGE" ]];
    then
        echo 'Container is not running. Exiting'
        exit
fi;
echo "Located docker image: "$DOCKER_IMAGE
# Run composer install in the container
$DOCKER_COMMAND docker exec -u www-data $DOCKER_IMAGE bash -c "$CLICOMMAND /var/www/html/docker/scripts/$COMMAND $ARRGS"
