<x-cerberus::cerberus>
    <x-slot:header>
        <h1>Επεξεργασία Κατηγορίας {{ $category->getTranslation('title', 'el') }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />


    <form action="{{ route('cerberus.categories.update', $category) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PATCH')


        <div class="px-4 sticky top-0 z-50 mb-4 bg-white flex flex-wrap justify-center md:justify-between items-center">
            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.categories.index') }}" />


            <div class="flex justify-end gap-2 m-4 p-2 bg-white">
                <x-cerberus::form.link href="{{ route('cerberus.packages.order', $category) }}"
                    class="border border-pacific-500  hover:bg-pacific-600 text-slate-600 font-semibold hover:text-white">
                    Σειρά εμφάνισης πακέτων
                </x-cerberus::form.link>
                <x-cerberus::form.link href="{{ route('cerberus.banners.order', $category) }}"
                    class="border border-pacific-500  hover:bg-pacific-600 text-slate-600 font-semibold hover:text-white">
                    Σειρά εμφάνισης banners
                </x-cerberus::form.link>
            </div>

        </div>

        <div class="flex justify-evenly mt-4">
            @foreach (LocaleConfig::getLocales() as $locale)
                <div class="w-[48%]">
                    <livewire:cerberus::titles.edit-title :model="$category" :locale="$locale" />

                    <x-cerberus::form.field-block x-data>
                        <div class="flex justify-between items-center mb-2">
                            <x-cerberus::form.label for="content[{{ $locale }}]">
                                Content {{ __('base.' . $locale) }}
                            </x-cerberus::form.label>
                            @if ($locale == 'en')
                                <div>
                                    <livewire:cerberus::elements.suggest-translation :content="$category->getTranslation('content', 'el')"
                                        :id="'content'" />

                                    <span
                                        x-on:translate-content.window="tinyMCE.get('content[en]').setContent($event.detail.translation)"></span>
                                </div>
                            @endif
                        </div>
                        <x-cerberus::form.textarea id="content[{{ $locale }}]" name="content[{{ $locale }}]"
                            class="editor">
                            {{ old('content.' . $locale, $category->getTranslation('content', $locale)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="content.{{ $locale }}]" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::seo.seo-attributes :model="$category" :locale="$locale" />
                </div>
            @endforeach
        </div>

        <div>
            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="image">
                    MAIN IMAGE

                    @if ($category->mainImage())
                        <img src="{{ asset('storage/' . $category->imageMd()) }}" alt="">
                        <label class="block mb-3" for="deleteImage">
                            Επιλέξτε για διαγραφή της εικόνας
                            <input type="checkbox" name="deleteImage" id="deleteImage">
                        </label>
                    @endif
                </x-cerberus::form.label>
                <x-cerberus::form.input id="image" name="image" type="file" />
                <x-cerberus::form.error value="image" />
            </x-cerberus::form.field-block>

            <div class="flex justify-between">
                @foreach (LocaleConfig::getLocales() as $locale)
                    <div class="w-[48%]">
                        <x-cerberus::form.field-block>
                            <x-cerberus::form.label for="alt[{{ $locale }}]">
                                Alt {{ __('base.' . $locale) }}
                            </x-cerberus::form.label>
                            <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                                value="{{ old('alt.' . $locale, $category->mainImage()?->getTranslation('alt', $locale)) }}" />
                            <x-cerberus::form.error value="alt[{{ $locale }}]" />
                        </x-cerberus::form.field-block>
                    </div>
                @endforeach
            </div>
        </div>

        <div>
            <livewire:cerberus::elements.featured-button :model="$category" />
        </div>

        <div class="px-4">
            @include('cerberus::faqs.partials.__faqs-selection', ['model' => $category])
        </div>

        {{-- SEO CHECKER --}}
   
        <x-cerberus::seo.seo-checker :model="$category" />
        

        {{-- DEVS AREA --}}
        @if (auth()->user()->type == 'dev')
            <x-cerberus::form.title-block class="bg-sun-700">
                <h3 class="text-white">DEVS ONLY</h3>
            </x-cerberus::form.title-block>

            <x-cerberus::form.field-block>
                <x-cerberus::form.info>
                    Προσοχή! Βεβαιωθείτε ότι τα αρχεία του template υπάρχουν!
                </x-cerberus::form.info>
                <x-cerberus::form.label for="template">
                    Template
                </x-cerberus::form.label>
                <select name="template" id="template" class="w-32">
                    <option value="generic" @selected($category->template == 'generic')>Generic</option>
                    <option value="lgbtq" @selected($category->template == 'lgbtq')>LGBTQ</option>
                    <option value="cruises" @selected($category->template == 'cruises')>Cruises</option>
                    <option value="individual" @selected($category->template == 'individual')>Individual</option>
                </select>
                <x-cerberus::form.error value="template" />
            </x-cerberus::form.field-block>
        @endif

        <div class="flex items-center justify-between px-4">

            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.categories.index') }}" />


    </form>

    <x-cerberus::form.delete deleteRoute="{{ route('cerberus.categories.destroy', $category) }}" />
    </div>


</x-cerberus::cerberus>
