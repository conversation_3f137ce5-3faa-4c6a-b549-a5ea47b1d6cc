<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΠΡΟΣΘΗΚΗ PHOTO GALLERY ΓΙΑ ΤΟ ΠΛΟΙΟ: {{ $ship->title }}</h1>
    </x-slot:header>

    <form action="{{ route('cerberus.ship.store.gallery', $ship) }}" method="POST" enctype="multipart/form-data">
        @csrf

        <div class="flex justify-between items-0center">
            @foreach (LocaleConfig::getLocales() as $locale)
                <x-cerberus::form.field-block class="w-1/2">
                    <div>
                        <x-cerberus::form.label for="title[{{ $locale }}]">
                            Title {{ __('base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="title[{{ $locale }}]" name="title[{{ $locale }}]"
                            value="{{ old('title.' . $locale) }}" />
                        <x-cerberus::form.error value="title[{{ $locale }}]" />
                    </div>

                    <div class="mt-4">
                        <x-cerberus::form.label for="content[{{ $locale }}]">
                            Content {{ __('base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="content[{{ $locale }}]"
                            name="content[{{ $locale }}]" class="editor">
                            {{ old('content.' . $locale) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="content[{{ $locale }}]" />
                    </div>
                </x-cerberus::form.field-block>
            @endforeach
        </div>

        <x-cerberus::form.field-block>
          <x-cerberus::form.label
            for="images"
           >
            Images
           </x-cerberus::form.label>
           <x-cerberus::form.input
             id="images"
             name="images[]"
             type="file"
             multiple
             value="{{ old('images') }}"
            />
            <x-cerberus::form.error value="images.*" />
            <x-cerberus::form.error value="images" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.field-block>
            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.ships.edit', $ship) }}" />
                
        </x-cerberus::form.field-block>

    </form>

</x-cerberus::cerberus>
