<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΕΠΕΞΕΡΓΑΣΙΑ PHOTO GALLERY {{ $gallery->getTranslation('title', 'el') }} ΓΙΑ ΤΟ ΠΛΟΙΟ: {{ $ship->title }}
        </h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" />

    <form action="{{ route('cerberus.ship.update.gallery', [$ship, $gallery]) }}" method="POST"
        enctype="multipart/form-data">
        @csrf
        @method('PATCH')

        <div class="flex justify-between items-0center">
            @foreach (LocaleConfig::getLocales() as $locale)
                <x-cerberus::form.field-block class="w-1/2">
                    <div>
                        <x-cerberus::form.label for="title[{{ $locale }}]">
                            Title {{ __('base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="title[{{ $locale }}]" name="title[{{ $locale }}]"
                            value="{{ old('title.' . $locale, $gallery->getTranslation('title', $locale)) }}" />
                        <x-cerberus::form.error value="title[{{ $locale }}]" />
                    </div>

                    <div class="mt-4">
                        <x-cerberus::form.label for="content[{{ $locale }}]">
                            Content {{ __('base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="content[{{ $locale }}]"
                            name="content[{{ $locale }}]" class="editor">
                            {{ old('content.' . $locale, $gallery->getTranslation('content', $locale)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="content[{{ $locale }}]" />
                    </div>
                </x-cerberus::form.field-block>
            @endforeach
        </div>

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="images">

                <x-cerberus::form.title-block id="gallery">
                    <h3 class="text-white">Gallery Images</h3>
                </x-cerberus::form.title-block>
                <x-cerberus::form.field-block>
                    <x-cerberus::form.info>
                        Αλλαγή σειράς με drag and drop <br />
                        Πατήστε το κουμπί Update για να αποθηκευτούν οι αλλαγές σε κάθε εικόνα
                    </x-cerberus::form.info>
                </x-cerberus::form.field-block>

                {{-- Livewire Component --}}
                <livewire:cerberus::ships.gallery :ship="$ship" :gallery="$gallery" />

            </x-cerberus::form.label>
            <x-cerberus::form.input id="images" name="images[]" type="file" multiple />
            <x-cerberus::form.error value="images.*" />
            <x-cerberus::form.error value="images" />
        </x-cerberus::form.field-block>





        <div class="flex justify-between items-center px-4">

            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.ships.edit', $ship) }}" />

    </form>
    <x-cerberus::form.delete deleteRoute="{{ route('cerberus.ship.destroy.gallery', [$ship, $gallery]) }}" />

    </div>




</x-cerberus::cerberus>
