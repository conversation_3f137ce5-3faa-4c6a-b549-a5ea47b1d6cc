<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΕΠΕΞΕΡΓΑΣΙΑ ΠΛΟΙΟΥ: {{ $ship->title }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.errors />
    
    @if ($errors->any())
        @foreach ($errors->all() as $error)
            {{ $error }}
        @endforeach
    @endif

    <form action="{{ route('cerberus.ships.update', $ship) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PATCH')



        <div class="px-4 sticky top-0 z-50 mb-4 bg-white flex flex-wrap justify-center md:justify-between items-center">
            <x-cerberus::form.buttons returnRoute="{{ route('cerberus.ships.edit', $ship) }}"
                cancelRoute="{{ route('cerberus.ships.index') }}" />

            <div class="pb-2 grow flex flex-wrap justify-end gap-2 md:gap-6">

                <x-cerberus::form.link id="preview" href="{{ route('el.ship.show', $ship) }}"
                    class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white"
                    target="_blank">
                    ΠΡΟΕΠΙΣΚΟΠΗΣΗ ΠΛΟΙΟΥ
                </x-cerberus::form.link>
            </div>


        </div>

        <div class="lg:grid lg:grid-cols-12 lg:gap-4">

            <div class="col-span-8">
                <x-cerberus::form.title-block>
                    <h3 class="text-white">MAIN CONTENT</h3>
                </x-cerberus::form.title-block>

                <div>
                    <input type="hidden" name="locale" value="{{ $locale }}">
                </div>

                <livewire:cerberus::titles.edit-title :model="$ship" :locale="$locale" />


                <x-cerberus::form.field-block x-data>
                    <x-cerberus::form.label for="content[{{ $locale }}]" class="flex justify-between items-center">
                        CONTENT {{ __('cerberus::base.' . $locale) }}

                        <div>
                            <livewire:cerberus::elements.suggest-translation :content="$ship->getTranslation('content', 'el')"
                                :id="'content'" />
                           
                                <span
                                x-on:translate-content.window="tinyMCE.get('content[en]').setContent($event.detail.translation)"></span>
                        </div>
                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="content[{{ $locale }}]" name="content[{{ $locale }}]"
                        data-height="900" class="editor">
                        {{ old('content.' . $locale, $ship->getTranslation('content', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="content.'.{{ $locale }}" />

                    <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $ship->getTranslation('content', 'el') !!}</div>
                </x-cerberus::form.field-block>


                <x-cerberus::form.title-block>
                    <h3 class="text-white">SHIP FEATURES</h3>
                </x-cerberus::form.title-block>


                <x-cerberus::form.field-block>

                    {{-- Υπάρχοντα Ship Features  --}}

                    <livewire:cerberus::ships.ship-features :locale="$locale" :ship="$ship" />

                    {{-- Creating new feature blocks with Alpine Js --}}

                    <div x-data="{
                        items: [],
                        adding: false,
                        count: {{ $ship->features->count() - 1 }},
                        initEditor() {
                            setTimeout(() => {
                                tinymce.init({
                                    selector: '.editor',
                                    plugins: 'lists link image code',
                                    toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | code',
                                });
                            }, 0);
                        }
                    }" x-init="initEditor()" @items-updated.window="initEditor()">
                        <template x-for="item in items" :key="item">
                            <div class="border-t-2 pb-3">
                                <div class="mx-2 mb-4 bg-slate-200 py-2 px-3 rounded-md">
                                    Feature <span
                                        x-text="items.indexOf(item)+{{ $ship->features->count() + 1 }}"></span>
                                </div>
                                <div>
                                    <x-cerberus::form.label>Title</x-cerberus::form.label>
                                    <x-cerberus::form.input x-bind:name="`feature[${item+count}][title]`"
                                        value="" />
                                </div>
                                <div>
                                    <x-cerberus::form.label>Content</x-cerberus::form.label>
                                    <x-cerberus::form.textarea x-bind:name="`feature[${item+count}][content]`"
                                        value="" class="editor">
                                    </x-cerberus::form.textarea>
                                </div>

                                <x-cerberus::form.field-block>
                                    <x-cerberus::form.label for="">
                                        Image
                                    </x-cerberus::form.label>
                                    <x-cerberus::form.input id=""
                                        x-bind:name="`feature[${item+count}][image]`" type="file" />
                                    <x-cerberus::form.error value="" />
                                </x-cerberus::form.field-block>

                                <x-cerberus::form.field-block>
                                    <x-cerberus::form.label for="">
                                        Alt
                                    </x-cerberus::form.label>
                                    <x-cerberus::form.input id=""
                                        x-bind:name="`feature[${item+count}][alt]`"
                                        value="" />
                                    <x-cerberus::form.error value="" />
                                </x-cerberus::form.field-block>

                                <button @click.prevent="items.splice(items.indexOf(item), 1).sort()"
                                    class="ml-3 float-right bg-red-500 p-1 text-white rounded-lg">Remove</button>
                            </div>
                        </template>
                        <button @click.prevent="items.push(items.length+1); $dispatch('items-updated')" id="addStore">
                            <x-cerberus::icons.add-icon /> Add Feature
                        </button>
                    </div>



                </x-cerberus::form.field-block>


                {{-- SEO ATTRIBUTES --}}
                <x-cerberus::seo.seo-attributes :model="$ship" :locale="$locale" />
            </div>
            <div class="col-span-4">
                <x-cerberus::form.title-block class="p-3">
                    <h3 class="text-white">MAIN IMAGE ATTRIBUTES</h3>
                </x-cerberus::form.title-block>

                <x-cerberus::form.field-block class="p-3">
                    <x-cerberus::form.label for="image">
                        ΚΕΝΤΡΙΚΗ ΕΙΚΟΝΑ
                        @if ($ship->mainImage())
                            <img src="{{ asset('storage/' . $ship->imageSm()) }}" alt="">
                            <label class="block mb-3" for="deleteImage">
                                Επιλέξτε για διαγραφή της εικόνας
                                <input type="checkbox" name="deleteImage" id="deleteImage">
                            </label>
                        @endif
                    </x-cerberus::form.label>

                    <x-cerberus::form.input id="image" name="image" type="file"
                        value="{{ $ship->mainImage() }}" />
                    <x-cerberus::form.error value="image" />
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block x-data>
                    <x-cerberus::form.label for="alt[{{ $locale }}]" class="flex justify-between items-center">
                        ALT TAG {{ __('cerberus::base.' . $locale) }}

                        <div>
                            <livewire:cerberus::elements.suggest-translation :content="$ship->mainImage()?->getTranslation('alt', 'el')"
                                :id="'alt'" />
                           
                            <span
                                x-on:translate-alt.window="document.getElementById('alt[en]').value = $event.detail.translation"></span>
                        </div>
                    </x-cerberus::form.label>

                    <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                        value="{{ old('alt.' . $locale, $ship?->mainImage()?->getTranslation('alt', $locale, false)) }}" />
                    <x-cerberus::form.error value="alt.{{ $locale }}" />

                </x-cerberus::form.field-block>


                <x-cerberus::form.title-block>
                    <h3 class="text-white">PUBLISHING OPTIONS</h3>
                </x-cerberus::form.title-block>
                <div class="">
                    <livewire:cerberus::elements.publish-button :model="$ship" />
                    <livewire:cerberus::elements.featured-button :model="$ship" />

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="created_at">
                            ΗΜΕΡΟΜΗΝΙΑ ΔΗΜΙΟΥΡΓΙΑΣ
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="created_at" name="created_at" disabled class="!bg-slate-300"
                            value="{{ old('published_at', \Carbon\Carbon::parse($ship->created_at)->format('d-m-Y H:i')) }}" />
                        <x-cerberus::form.error value="published_at" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="created_at">
                            ΤΕΛΕΥΤΑΙΑ ΕΝΗΜΕΡΩΣΗ
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="created_at" name="created_at" disabled class="!bg-slate-300"
                            value="{{ old('published_at', \Carbon\Carbon::parse($ship->updated_at)->format('d-m-Y H:i')) }}" />
                        <x-cerberus::form.error value="published_at" />
                    </x-cerberus::form.field-block>

                </div>
            </div>
        </div>


        <div class="flex items-center justify-between px-4">

            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.ships.index') }}" />


    </form>

    <x-cerberus::form.delete deleteRoute="{{ route('cerberus.ships.destroy', $ship) }}" />

    </div>

</x-cerberus::cerberus>
