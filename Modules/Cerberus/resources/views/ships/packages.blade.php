<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΑΝΤΙΣΤΟΙΧΙΣΗ ΠΑΚΕΤΩΝ ΣΤΟ ΠΛΟΙΟ: {{ $ship->title }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.errors />


    <form action="{{ route('cerberus.ship.assign.packages', $ship) }}" method="POST" class="px-2 bg-white">


        <div class="px-4 sticky top-0 z-50 mb-4 bg-white flex flex-wrap justify-center md:justify-between items-center">
            <x-cerberus::form.buttons translationRoute="{{ route('cerberus.ships.en.edit', $ship) }}"
                cancelRoute="{{ route('cerberus.ships.edit', $ship) }}" />

            <div class="pb-2 grow flex flex-wrap justify-end gap-2 md:gap-6">

            </div>


        </div>


        @csrf
        <x-cerberus::form.field-block>
            <livewire:cerberus::ships.packages :ship="$ship" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.ships.edit', $ship) }}" />
    </form>


</x-cerberus::cerberus>
