<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="max-w-[1920px] block mx-auto">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <link rel="icon" type="image/png" href="{{ asset('images/favicon/favicon-96x96.png') }}" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="{{ asset('images/favicon/favicon.svg') }}" />
    <link rel="shortcut icon" href="{{ asset('images/favicon/favicon.ico') }}" />
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/favicon/apple-touch-icon.png') }}" />
    <link rel="manifest" href="{{ asset('images/favicon/site.webmanifest') }}" />


    @livewireStyles

    @vite(['Modules/Cerberus/resources/assets/css/cerby.css', 'resources/js/app.js'])

    @yield('head')

</head>

@php
    \Carbon\Carbon::setlocale(app()->getLocale());
@endphp

<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        @include('cerberus::layouts.navigation')

        <div class="md:grid md:grid-cols-12">

            <div class="hidden md:block md:col-span-2 bg-gray-800 text-white min-h-screen relative">
                @include('cerberus::layouts.dashboard-menu')

            </div>

            <div class="md:col-span-10">
                <!-- Page Heading -->
                @if (isset($header))
                    <header class="bg-white dark:bg-gray-800 shadow">
                        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                            {{ $header }}
                        </div>
                    </header>
                @endif

                <!-- Page Content -->
                <main>
                    {{ $slot }}
                </main>
            </div>

        </div>
        @livewireScripts
        @yield('scripts')
</body>

</html>
