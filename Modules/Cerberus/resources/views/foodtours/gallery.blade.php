<x-cerberus::cerberus>
    <x-slot:header>
        <h1>Image Gallery για το πακέτο {{ $foodtour->getTranslation('title', 'el') }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" />

    <livewire:cerberus::images.gallery :foodtour="$foodtour" />


    <div class="p-4 bg-white">
        <form action="{{ route('cerberus.foodtour.store.gallery', $foodtour) }}" method="POST"
            enctype="multipart/form-data">
            @csrf

            <input type="file" name="images[]" multiple>
            <x-cerberus::form.error value="images.*" />


            <div class="flex gap-2 items-center mt-8">
                <x-cerberus::form.button
                    class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
                    Upload Images
                </x-cerberus::form.button>
                <x-cerberus::form.link href="{{ route('cerberus.foodtours.edit', $foodtour) }}"
                    class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
                    </svg>
                    Επιστροφή
                </x-cerberus::form.link>
            </div>

        </form>
    </div>

</x-cerberus::cerberus>
