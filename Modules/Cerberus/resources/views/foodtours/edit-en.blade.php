<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΜΕΤΑΦΡΑΣΗ FOOD TOUR: {{ $foodtour->title }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.errors />

    @if ($errors->any())
        @foreach ($errors->all() as $error)
            {{ $error }}
        @endforeach
    @endif
    
    <form action="{{ route('cerberus.foodtours.update', $foodtour) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PATCH')        

        <div class="px-4 sticky top-0 z-50 bg-white flex justify-between items-center">
            <x-cerberus::form.buttons returnRoute="{{ route('cerberus.foodtours.edit', $foodtour) }}"
                cancelRoute="{{ route('cerberus.foodtours.index') }}" />

            <div class="pb-2 grow flex flex-wrap justify-end gap-2 md:gap-6">

                <x-cerberus::form.link id="preview" href="{{ route('en.foodtours.show', $foodtour) }}"
                   class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white" target="_blank">
                    ΠΡΟΕΠΙΣΚΟΠΗΣΗ ΠΑΚΕΤΟΥ
                </x-cerberus::form.link>
            </div>
        </div>

        <div class="lg:grid lg:grid-cols-12 lg:gap-4">

            <div class="col-span-8">

                <x-cerberus::form.title-block>
                    <h3 class="text-white">MAIN CONTENT</h3>
                </x-cerberus::form.title-block>
                <div>
                    <input type="hidden" name="locale" value="{{ $locale }}">
                </div>

                <livewire:cerberus::titles.edit-title :model="$foodtour" :locale="$locale" :showGreek="true" />

                <x-cerberus::form.field-block x-data>
                    <x-cerberus::form.label for="subtitle[{{ $locale }}]" class="flex justify-between items-center">
                        SUBTITLE {{ __('cerberus::base.' . $locale) }}

                        <div>
                            <livewire:cerberus::elements.suggest-translation :content="$foodtour->getTranslation('subtitle', 'el')"
                                :id="'subtitle'" />
                           
                            <span
                                x-on:translate-subtitle.window="document.getElementById('subtitle[en]').value = $event.detail.translation"></span>
                        </div>
                    </x-cerberus::form.label>
                    <x-cerberus::form.input id="subtitle[{{ $locale }}]" name="subtitle[{{ $locale }}]"
                        value="{{ old('subtitle.' . $locale, $foodtour->getTranslation('subtitle', $locale, false)) }}" />
                    <x-cerberus::form.error value="subtitle.{{ $locale }}" />
                    <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $foodtour->getTranslation('subtitle', 'el') !!}</div>
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block x-data>
                    <x-cerberus::form.label for="content[{{ $locale }}]" class="flex justify-between items-center">
                        CONTENT {{ __('cerberus::base.' . $locale) }}

                        <div>
                            <livewire:cerberus::elements.suggest-translation :content="$foodtour->getTranslation('content', 'el')"
                                :id="'content'" />
                           
                                <span
                                x-on:translate-content.window="tinyMCE.get('content[en]').setContent($event.detail.translation)"></span>
                        </div>

                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="content[{{ $locale }}]" name="content[{{ $locale }}]"
                        class="editor">
                        {{ old('content.' . $locale, $foodtour->getTranslation('content', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="content.{{ $locale }}" />
                    
                    <div class="px-3 py-1 mt-1 bg-gray-200 h-[200px] overflow-scroll">Ελληνικά: {!! $foodtour->getTranslation('content', 'el') !!}</div>
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block x-data>
                    <x-cerberus::form.label for="tagline[{{ $locale }}]" class="flex justify-between items-center">
                        TAGLINE {{ __('cerberus::base.' . $locale) }}

                        <div>
                            <livewire:cerberus::elements.suggest-translation :content="$foodtour->getTranslation('tagline', 'el')"
                                :id="'tagline'" />
                           
                            <span
                                x-on:translate-tagline.window="document.getElementById('tagline[en]').value = $event.detail.translation"></span>
                        </div>
                    </x-cerberus::form.label>

                    <x-cerberus::form.info>
                        Ιδανικό μέγεθος: 30-40 λέξεις
                    </x-cerberus::form.info>

                    <x-cerberus::form.textarea id="tagline[{{ $locale }}]" name="tagline[{{ $locale }}]">
                        {{ old('tagline.' . $locale, $foodtour->getTranslation('tagline', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="tagline. {{ $locale }}" />
                    <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $foodtour->getTranslation('tagline', 'el') !!}</div>
                </x-cerberus::form.field-block>
           

                <x-cerberus::form.field-block x-data>
                    <x-cerberus::form.label for="info[{{ $locale }}]" class="flex justify-between items-center">
                        BEST MOMENTS {{ __('cerberus::base.' . $locale) }}

                        <div>
                            <livewire:cerberus::elements.suggest-translation :content="$foodtour->getTranslation('info', 'el')"
                                :id="'info'" />
                           
                            <span
                                x-on:translate-info.window="tinyMCE.get('info[en]').setContent($event.detail.translation)"></span>
                        </div>

                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="info[{{ $locale }}]" name="info[{{ $locale }}]"
                        class="editor">
                        {{ old('info.' . $locale, $foodtour->getTranslation('info', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="info.'.{{ $locale }}" />
                    <div class="px-3 py-1 mt-1 bg-gray-200 h-[200px] overflow-scroll">Ελληνικά: {!! $foodtour->getTranslation('info', 'el') !!}</div>
                </x-cerberus::form.field-block>

                <div class="flex">
                    <x-cerberus::form.field-block class="lg:w-1/2" x-data>
                        <x-cerberus::form.label for="included[{{ $locale }}]" class="flex justify-between items-center">
                            INCLUDED {{ __('cerberus::base.' . $locale) }}

                            <div>
                                <livewire:cerberus::elements.suggest-translation :content="$foodtour->getTranslation('included', 'el')"
                                    :id="'included'" />
                               
                                <span
                                    x-on:translate-included.window="tinyMCE.get('included[en]').setContent($event.detail.translation)"></span>
                            </div>

                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="included[{{ $locale }}]" data-height="500"
                            name="included[{{ $locale }}]" class="editor">
                            {{ old('included.' . $locale, $foodtour->getTranslation('included', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="included.'.{{ $locale }}" />
                        <div class="px-3 py-1 mt-1 bg-gray-200 h-[200px] overflow-scroll">Ελληνικά: {!! $foodtour->getTranslation('included', 'el') !!}</div>
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block class="lg:w-1/2" x-data>
                        <x-cerberus::form.label for="not_included[{{ $locale }}]" class="flex justify-between items-center">
                            NOT INCLUDED {{ __('cerberus::base.' . $locale) }}

                            <div>
                                <livewire:cerberus::elements.suggest-translation :content="$foodtour->getTranslation('not_included', 'el')"
                                    :id="'not_included'" />
                               
                                <span
                                    x-on:translate-not_included.window="tinyMCE.get('not_included[en]').setContent($event.detail.translation)"></span>
                            </div>

                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="not_included[{{ $locale }}]" data-height="500"
                            name="not_included[{{ $locale }}]" class="editor">
                            {{ old('not_included.' . $locale, $foodtour->getTranslation('not_included', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="not_included.'.{{ $locale }}" />
                        <div class="px-3 py-1 mt-1 bg-gray-200 h-[200px] overflow-scroll">Ελληνικά: {!! $foodtour->getTranslation('not_included', 'el') !!}</div>
                    </x-cerberus::form.field-block>
                </div>


                <livewire:cerberus::packages.itinerary-steps :package="$foodtour" :locale="$locale" :showGreek="true" :isFoodTour="true" />

               

                {{-- SEO ATTRIBUTES --}}
                <x-cerberus::seo.seo-attributes :model="$foodtour" :locale="$locale" />

            </div>
            <div class="col-span-4">

                <x-cerberus::form.title-block class="p-3">
                    <h3 class="text-white">MAIN IMAGE ATTRIBUTES</h3>
                </x-cerberus::form.title-block>

                <x-cerberus::form.field-block class="p-3">
                    <x-cerberus::form.label for="image">
                        ΚΕΝΤΡΙΚΗ ΕΙΚΟΝΑ
                        @if ($foodtour->mainImage())
                            <img src="{{ asset('storage/' . $foodtour->imageSm()) }}" alt="">
                        @endif
                    </x-cerberus::form.label>
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="alt[{{ $locale }}]">
                        ALT TAG {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                        value="{{ old('alt.' . $locale, $foodtour?->mainImage()?->getTranslation('alt', $locale, false)) }}" />
                    <x-cerberus::form.error value="alt.{{ $locale }}" />
                    <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά:
                        {{ $foodtour->mainImage()?->getTranslation('alt', 'el') }}
                    </div>
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.link 
                        class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white"
                        href="{{ route('cerberus.foodtour.show.gallery', $foodtour) }}">
                        IMAGE GALLERY ({{ $foodtour->galleryImages()->count() }} {{ $foodtour->galleryImages()->count() > 1 ? 'IMAGES' : 'IMAGE' }})
                    </x-cerberus::form.link>
                </x-cerberus::form.field-block>
            </div>

        </div>


        <div class="flex items-center justify-between px-4">

            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.foodtours.index') }}" />


    </form>

    <form action="{{ route('cerberus.packages.clear.translation', $foodtour) }}" method="POST">
        @csrf
        <x-cerberus::form.button class="bg-slate-400 text-white hover:bg-slate-500"
            onclick="return confirm('Θέλετε σίγουρα να διαγράψετε τη μετάφραση;');">
            Κατάργηση Μετάφρασης
        </x-cerberus::form.button>
    </form>

    </div>

</x-cerberus::cerberus>
