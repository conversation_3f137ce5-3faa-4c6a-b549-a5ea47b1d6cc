<x-cerberus::cerberus>
    <x-slot:header>
        <h1>
            @if ($lead->message_type == 'travel')
                TRAVEL
            @elseif ($lead->message_type == 'food')
                FOOD TOURS
            @elseif ($lead->message_type == 'contact')
                CONTACT FORM
            @elseif ($lead->message_type == 'create_package')
                CREATE PACKAGE
            @endif LEAD ID {{ $lead->id }}
        </h1>
        <h4 class="text-center mt-2">Αποστολή: {{ Carbon\Carbon::parse($lead->created_at)->format('d-m-Y') }}</h4>
    </x-slot:header>

    <div class="p-3 m-3 bg-white ">
        <table class="table-auto border-separate border border-slate-400">
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Όνομα</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->first_name }}</td>
            </tr>
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Επώνυμο</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->last_name }}</td>
            </tr>
            @if ($lead->message_type == 'travel' || $lead->message_type == 'food')
                <tr>
                    <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Ταξιδιωτικό Πακέτο</th>
                    <td class="border border-slate-300 px-4 py-3"><a
                            class="text-pacific-900 font-semibold hover:text-pacific-600"
                            href="{{ $lead->package ? route('el.travel.show', $lead->package) : '' }}"
                            target="_blank">{{ $lead->package?->title }}</a></td>
                </tr>

                <tr>
                    <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Επιθυμητή Ημερομηνία</th>
                    <td class="border border-slate-300 px-4 py-3">{{ $lead->depart_date }}</td>
                </tr>
            @endif
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Email</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->email }}</td>
            </tr>
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Κινητό</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->mobile }}</td>
            </tr>
            @if ($lead->message_type !== 'contact')
                @if ($lead->message_type == 'travel' || $lead->message_type == 'food')
                    <tr>
                        <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Διεύθυνση</th>
                        <td class="border border-slate-300 px-4 py-3">{{ $lead->address }}</td>
                    </tr>
                    <tr>
                        <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Πόλη</th>
                        <td class="border border-slate-300 px-4 py-3">{{ $lead->city }}</td>
                    </tr>
                @endif
                <tr>
                    <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Αριθμός ατόμων</th>
                    <td class="border border-slate-300 px-4 py-3">{{ $lead->persons }}</td>
                </tr>
                <tr>
                    <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Παιδιά</th>
                    <td class="border border-slate-300 px-4 py-3">{{ $lead->kids }}</td>
                </tr>
            @endif
            @if ($lead->message_type == 'contact' || $lead->message_type == 'create_package' )
                <tr>
                    <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">{{ $lead->message_type == 'contact' ? 'Θέμα' : 'Προορισμός' }}</th>
                    <td class="border border-slate-300 px-4 py-3">{{ $lead->subject }}</td>
                </tr>
            @endif
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Μήνυμα/Σχόλια</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->message }}</td>
            </tr>

            @if ($lead->message_type == 'create_package')
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Προτιμώμενη επικοινωνία μέσω</th>
                <td class="border border-slate-300 px-4 py-3">{{ __('form.contact_by.'.$lead->contact_by) }}</td>
            </tr>
            @endif
        </table>


        <div class="mt-8">
            <x-cerberus::form.link link="{{ route('cerberus.leads.' . $lead->message_type) }}"
                class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
                </svg>
                {{ __('Επιστροφή') }}
            </x-cerberus::form.link>
        </div>
    </div>
</x-cerberus::cerberus>
