<x-cerberus::cerberus>
    <x-slot:header>
        <h1>
            CHATBOT LEAD ID {{ $lead->id }}
        </h1>
        <h4 class="text-center mt-2">Αποστολή: {{ Carbon\Carbon::parse($lead->created_at)->format('d-m-Y') }}</h4>
    </x-slot:header>

    <div class="p-3 m-3 bg-white ">
        <table class="table-auto border-separate border border-slate-400">
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Όνομα</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->name }}</td>
            </tr>        

            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Email</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->email }}</td>
            </tr>
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Κινητό</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->phone }}</td>
            </tr>
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Προορισμός</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->destination }}</td>
            </tr>
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Ημερομηνίες</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->dates }}</td>
            </tr>

            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Αριθμός ατόμων</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->travelers }}</td>
            </tr>
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Παιδιά</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->underage }}</td>
            </tr>
            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Προτιμήσεις</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->preferences }}</td>
            </tr>

            <tr>
                <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Μήνυμα/Σχόλια</th>
                <td class="border border-slate-300 px-4 py-3">{{ $lead->query }}</td>
            </tr>

            @if ($lead->message_type == 'create_package')
                <tr>
                    <th class="text-left border border-slate-300 pl-2 pr-12 py-2 w-1/5">Προτιμώμενη επικοινωνία μέσω
                    </th>
                    <td class="border border-slate-300 px-4 py-3">{{ __('form.contact_by.' . $lead->contact_by) }}</td>
                </tr>
            @endif
        </table>


        <div class="mt-8">
            <x-cerberus::form.link link="{{ route('cerberus.leads.chatbot.index') }}"
                class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
                </svg>
                {{ __('Επιστροφή') }}
            </x-cerberus::form.link>
        </div>
    </div>
</x-cerberus::cerberus>
