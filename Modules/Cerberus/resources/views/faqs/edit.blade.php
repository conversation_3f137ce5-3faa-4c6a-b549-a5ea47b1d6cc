<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΕΠΕΞΕΡΓΑΣΙΑ FAQ</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.errors />

    <form action="{{ route('cerberus.faqs.update', $faq) }}" method="POST">
        @csrf
        @method('PATCH')

        @foreach (LocaleConfig::getLocales() as $locale)
            <div class="mb-6 border-b">
                <x-cerberus::form.field-block>
                    <x-cerberus::icons.flags :lang="$locale" />

                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block x-data>
                    <x-cerberus::form.label for="question[{{ $locale }}]" class="flex justify-between items-center">
                        Question {{ __('base.' . $locale) }}

                        @if ($locale == 'en')
                            <div>
                                <livewire:cerberus::elements.suggest-translation :content="$faq->getTranslation('question', 'el')" :id="'question'" />
                                <span
                                    x-on:translate-question.window="document.getElementById('question[en]').value = $event.detail.translation"></span>
                            </div>
                        @endif

                    </x-cerberus::form.label>
                    <x-cerberus::form.input id="question[{{ $locale }}]" name="question[{{ $locale }}]"
                        value="{{ old('question.' . $locale, $faq->getTranslation('question', $locale)) }}" />
                    <x-cerberus::form.error value="question.{{ $locale }}" />

                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block x-data>
                    <x-cerberus::form.label for="answer[{{ $locale }}]" class="flex justify-between">
                        Answer {{ __('base.' . $locale) }}


                        @if ($locale == 'en')
                            <div>
                                <livewire:cerberus::elements.suggest-translation :content="$faq->getTranslation('answer', 'el')" :id="'answer'" />
                                <span
                                    x-on:translate-answer.window="tinyMCE.get('answer[en]').setContent($event.detail.translation)"></span>
                            </div>
                        @endif

                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="answer[{{ $locale }}]" name="answer[{{ $locale }}]"
                        class="editor">
                        {{ $faq->getTranslation('answer', $locale) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="answer.{{ $locale }}" />
                </x-cerberus::form.field-block>
            </div>
        @endforeach

        <x-cerberus::form.field-block class="flex gap-8">

            <livewire:cerberus::elements.publish-button :model="$faq" />
            <livewire:cerberus::elements.featured-button :model="$faq" />
        </x-cerberus::form.field-block>



        @if ($locale == 'en')
            <span
                x-on:translate.window="document.getElementById('question[en]').value = $event.detail.translation"></span>
        @endif



        <div class="flex justify-between items-center px-2">
            <x-cerberus::form.field-block>
                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.faqs.index') }}" />
            </x-cerberus::form.field-block>
    </form>

    <x-cerberus::form.delete deleteRoute="{{ route('cerberus.faqs.destroy', $faq) }}" />
    </div>
</x-cerberus::cerberus>
