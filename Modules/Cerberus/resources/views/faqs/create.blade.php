<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΠΡΟΣΘΗΚΗ FAQ</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.errors />

    <form action="{{ route('cerberus.faqs.store') }}" method="POST">
        @csrf

        @foreach (LocaleConfig::getLocales() as $locale)
            <div class="mb-6 border-b">
                <x-cerberus::form.field-block>
                    <x-cerberus::icons.flags :lang="$locale" />

                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="question[{{ $locale }}]">
                        Question {{ __('base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.input id="question[{{ $locale }}]" name="question[{{ $locale }}]" value="{{ old('question.'.$locale) }}" />
                    <x-cerberus::form.error value="question.{{ $locale }}" />
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="answer[{{ $locale }}]">
                        Answer {{ __('base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="answer[{{ $locale }}]" name="answer[{{ $locale }}]" class="editor">
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="answer.{{ $locale }}" />
                </x-cerberus::form.field-block>
            </div>
        @endforeach

        <x-cerberus::form.field-block>
            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.faqs.index') }}" />
        </x-cerberus::form.field-block>
    </form>
</x-cerberus::cerberus>
