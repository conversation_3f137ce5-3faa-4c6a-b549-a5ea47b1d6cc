<x-cerberus::cerberus>
    <x-slot:header>
        <h1>Menus</h1>
        </x-slot:header>


        <div class="w-full px-4 block mx-auto bg-white mt-4">

            <x-cerberus::alerts.flash type="success" timeout=10 />

            <div class="flex justify-between p-4 gap-6 items-center">

                <div class="flex gap-2 items-center">
                    {{-- <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
                <x-cerberus::form.helper>Αναζήτηση σε τίτλο, περιεχόμενο, tagline</x-cerberus::form.helper> --}}
                </div>

                <x-cerberus::form.link href="{{ route('cerberus.menus.create') }}"
                    class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
                    Προσθήκη
                </x-cerberus::form.link>
            </div>

            <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
                <thead class="bg-gray-300 text-left">
                    <th class="p-2">ID</th>
                    <th class="p-2">Pubished</th>
                    <th class="p-2">Language</th>
                    <th class="p-2">Title</th>
                    <th class="p-2">Menu Items</th>
                    <th class="p-2"></th>
                </thead>
                <tbody class="divide-y-2 divide-x-4">
                    @foreach ($menus as $menu)
                        <tr>
                            <td class="p-2 w-12">{{ $menu->id }}</td>
                            <td class="p-2 w-12">
                                <div class="">
                                    <span
                                        class="block mx-auto w-3 h-3 rounded-full {{ $menu->published ? 'bg-green-500' : 'bg-orange-500' }}"
                                        title="{{ $menu->published ? 'Δημοσιευμένο' : 'Μη δημοσιευμένο' }}">
                                    </span>
                                </div>
                            </td>
                            <td class="p-2 w-8">

                                <x-cerberus::icons.flags :lang="$menu->locale" class="block mx-auto" />

                            </td>
                            <td class="p-2">

                                <div class="pb-2">
                                    <a class="font-bold hover:underline" href="{{ route('cerberus.menus.edit', $menu) }}">
                                        {{ $menu->title }}
                                    </a>
                                </div>
                            </td>
                            <td class="p-2">
                                <a href="{{ route('cerberus.menu-items.index', $menu) }}" class="hover:underline">
                                    {{ $menu->menuItems->count()  }}
                                    {{ __('Items') }}</a>
                            </td>
                            <td>
                                <div class="flex">
                                    <x-cerberus::form.link href="{{ route('cerberus.menus.edit', $menu) }}"
                                        class="focus:ring-transparent">
                                        <x-cerberus::icons.edit-icon />
                                    </x-cerberus::form.link>
                                    
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            {{-- {{ $menus->links() }} --}}
        </div>


</x-cerberus::cerberus>
