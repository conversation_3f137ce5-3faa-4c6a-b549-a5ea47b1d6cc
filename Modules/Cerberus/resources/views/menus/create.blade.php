<x-cerberus::cerberus>
    <x-slot:header>
        <h1>Create New Menu</h1>
    </x-slot:header>

    

    <form action="{{ route('cerberus.menus.store') }}" method="POST" class="my-8 w-4/5 mx-auto">
        @csrf

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="title">
                <div class="flex gap-2 items-center">
                    {{ __('Menu title') }}
                    <x-cerberus::form.helper>Π.χ. Greek Menu</x-cerberus::form.helper>
                </div>
            </x-cerberus::form.label>
            <x-cerberus::form.input id="title" name="title" value="{{ old('title') }}" />
            <x-cerberus::form.error value="title" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="name">
                <div class="flex gap-2 items-center">
                    {{ __('Menu name') }}
                    <x-cerberus::form.helper>Μοναδικό όνομα, Π.χ. greek-menu</x-cerberus::form.helper>
                </div>
            </x-cerberus::form.label>
            <x-cerberus::form.input id="name" name="name" value="{{ old('name') }}" />
            <x-cerberus::form.error value="name" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.field-block class="flex gap-3 items-end">
            <x-cerberus::form.label for="locale">
                {{ __('Menu Language') }}
            </x-cerberus::form.label>

            <select name="locale" id="locale" class="w-32">
                <option selected disabled>
                    {{ __('Επιλέξτε') }}
                </option>
                @foreach (LocaleConfig::getLocales() as $locale)
                    <option value="{{ $locale }}">
                        {{ __('base.'.$locale) }}
                    </option>
                @endforeach

            </select>

            <x-cerberus::form.error value="locale" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.field-block class="flex gap-3 items-end">
            <x-cerberus::form.label for="published">
                {{ __('Published') }}
            </x-cerberus::form.label>

            <select name="published" id="published" class="w-36">

                <option selected disabled>
                    {{ __('Επιλέξτε') }}
                </option>

                <option value="1">
                    {{ __('Published') }}
                </option>
                <option value="0">
                    {{ __('Unpublished') }}
                </option>


            </select>

            <x-cerberus::form.error value="published" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.menus.index') }}" />

    </form>

</x-cerberus::cerberus>
