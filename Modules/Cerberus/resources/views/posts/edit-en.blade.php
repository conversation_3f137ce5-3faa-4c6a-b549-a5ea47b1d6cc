<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΜΕΤΑΦΡΑΣΗ BLOG POST {{ $post->title }}</h1>

    </x-slot:header>

    <form action="{{ route('cerberus.posts.update', $post) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PATCH')

        <x-cerberus::alerts.flash type="success" timeout=10 />
        <x-cerberus::alerts.errors />

        <div class="px-4 sticky top-0 z-50 mb-4 bg-white flex flex-wrap justify-center md:justify-between items-center">
            <x-cerberus::form.buttons returnRoute="{{ route('cerberus.posts.edit', $post) }}"
                cancelRoute="{{ route('cerberus.posts.index') }}" />

            <div class="pb-2 grow flex flex-wrap justify-end gap-2 md:gap-6">

                <x-cerberus::form.link id="preview" href="{{ route('en.posts.show', $post->getTranslation('slug', 'en')) }}"
                    class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white"
                    target="_blank">
                    ΠΡΟΕΠΙΣΚΟΠΗΣΗ ΑΡΘΡΟΥ
                </x-cerberus::form.link>
            </div>


        </div>

        <div>
            <div class="lg:grid lg:grid-cols-12 lg:gap-4">
                <div class="col-span-8">
                    <x-cerberus::form.title-block>
                        <h3 class="text-white">MAIN CONTENT</h3>
                    </x-cerberus::form.title-block>

                    <input type="hidden" name="locale" value="{{ $locale }}">

                    <livewire:cerberus::titles.edit-title :model="$post" :locale="$locale" :showGreek="true" />

                    <x-cerberus::form.field-block x-data>
                        <x-cerberus::form.label for="content[{{ $locale }}]" class="flex justify-between">
                            CONTENT {{ __('cerberus::base.' . $locale) }}

                            @if ($locale == 'en')
                                <div>
                                    <livewire:cerberus::elements.suggest-translation :content="$post->getTranslation('content', 'el')"
                                        :id="'content'" />
                                    <span
                                        x-on:translate-content.window="tinyMCE.get('content[en]').setContent($event.detail.translation)"></span>
                                </div>
                            @endif

                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="content[{{ $locale }}]" class="editor"
                            name="content[{{ $locale }}]">
                            {{ old('content.' . $locale, $post->getTranslation('content', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="{{ 'content.' . $locale }}" />

                        <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $post->getTranslation('content', 'el') !!}</div>
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block x-data>
                        <div class="flex justify-between items-center">
                            <div>
                                <x-cerberus::form.label for="tagline[{{ $locale }}]">
                                    TAGLINE {{ __('cerberus::base.' . $locale) }}
                                </x-cerberus::form.label>
                                <x-cerberus::form.info>
                                    Ιδανικό μέγεθος: 30-40 λέξεις
                                </x-cerberus::form.info>
                            </div>
                            @if ($locale == 'en')
                                <div>
                                    <livewire:cerberus::elements.suggest-translation :content="$post->getTranslation('tagline', 'el')"
                                        :id="'tagline'" />
                                    <span
                                        x-on:translate-tagline.window="document.getElementById('tagline[en]').value = $event.detail.translation"></span>
                                </div>
                            @endif
                        </div>

                        <x-cerberus::form.textarea id="tagline[{{ $locale }}]" class=""
                            name="tagline[{{ $locale }}]">
                            {{ old('tagline.' . $locale, $post->getTranslation('tagline', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="{{ 'tagline.' . $locale }}" />

                        <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $post->getTranslation('tagline', 'el') !!}</div>

                    </x-cerberus::form.field-block>



                    {{-- SEO ATTRIBUTES --}}
                    <x-cerberus::seo.seo-attributes :model="$post" :locale="$locale" />

                </div>
                <div class="col-span-4">

                    <x-cerberus::form.title-block class="p-3">
                        <h3 class="text-white">MAIN IMAGE ATTRIBUTES</h3>
                    </x-cerberus::form.title-block>

                    <x-cerberus::form.field-block class="">

                        ΚΕΝΤΡΙΚΗ ΕΙΚΟΝΑ {{ __('cerberus::base.' . $locale) }}
                        @if ($post->mainImage())
                            <img src="{{ asset('storage/' . $post->imageSm()) }}" alt="">
                        @endif

                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block x-data>
                        <x-cerberus::form.label for="alt[{{ $locale }}]" class="flex justify-between">
                            ALT TAG {{ __('cerberus::base.' . $locale) }}

                            @if ($locale == 'en' )
                                <div>
                                    <livewire:cerberus::elements.suggest-translation :content="$post->mainImage()?->getTranslation('alt', 'el')"
                                        :id="'alt'" />
                                   
                                    <span
                                        x-on:translate-alt.window="document.getElementById('alt[en]').value = $event.detail.translation"></span>
                                </div>
                            @endif

                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                            value="{{ old('alt.' . $locale, $post?->mainImage()?->getTranslation('alt', $locale, false)) }}" />
                        <x-cerberus::form.error value="alt.{{ $locale }}" />

                        <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $post->mainImage()?->getTranslation('alt', 'el') !!}</div>
                    </x-cerberus::form.field-block>

                </div>

            </div>

            <div class="flex items-center flex-wrap justify-center gap-2 md:justify-between px-4 pb-8">

                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.posts.index') }}" />

    </form>

    <form action="{{ route('cerberus.posts.clear.translation', $post) }}" method="POST">
        @csrf
        <x-cerberus::form.button class="bg-slate-400 text-white hover:bg-slate-500"
            onclick="return confirm('Θέλετε σίγουρα να διαγράψετε τη μετάφραση;');">
            Κατάργηση Μετάφρασης
        </x-cerberus::form.button>
    </form>

    </div>

    </div>

</x-cerberus::cerberus>
