<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΕΠΕΞΕΡΓΑΣΙΑ BLOG POST {{ $post->title }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />

    <form action="{{ route('cerberus.posts.update', $post) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PATCH')

        <x-cerberus::alerts.flash type="success" timeout=10 />
        <x-cerberus::alerts.errors />

        <div class="px-4 sticky top-0 z-50 mb-4 bg-white flex flex-wrap justify-center md:justify-between items-center">
            <x-cerberus::form.buttons translationRoute="{{ route('cerberus.posts.en.edit', $post) }}"
                cancelRoute="{{ route('cerberus.posts.index') }}" />

            <div class="pb-2 grow flex flex-wrap justify-end gap-2 md:gap-6">

                <x-cerberus::form.link id="preview" href="{{ route('posts.show', $post) }}"
                    class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white"
                    target="_blank">
                    ΠΡΟΕΠΙΣΚΟΠΗΣΗ ΑΡΘΡΟΥ
                </x-cerberus::form.link>
            </div>


        </div>

        <div>
            <div class="lg:grid lg:grid-cols-12 lg:gap-4">
                <div class="col-span-8">

                    <x-cerberus::form.title-block>
                        <h3 class="text-white">MAIN CONTENT</h3>
                    </x-cerberus::form.title-block>

                    <input type="hidden" name="locale" value="{{ $locale }}">

                    <livewire:cerberus::titles.edit-title :model="$post" :locale="$locale" />

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="content[{{ $locale }}]">
                            CONTENT {{ __('cerberus::base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="content[{{ $locale }}]" class="editor" data-height="900"
                            name="content[{{ $locale }}]">
                            {{ old('content.' . $locale, $post->getTranslation('content', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="{{ 'content.' . $locale }}" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="tagline[{{ $locale }}]">
                            TAGLINE {{ __('cerberus::base.' . $locale) }}
                        </x-cerberus::form.label>

                        <x-cerberus::form.info>
                            Ιδανικό μέγεθος: 30-40 λέξεις
                        </x-cerberus::form.info>

                        <x-cerberus::form.textarea id="tagline[{{ $locale }}]" data-height="200"
                            name="tagline[{{ $locale }}]">
                            {{ old('tagline.' . $locale, $post->getTranslation('tagline', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="{{ 'tagline.' . $locale }}" />
                    </x-cerberus::form.field-block>


                    {{-- SEO ATTRIBUTES --}}
                    <x-cerberus::seo.seo-attributes :model="$post" :locale="$locale" />

                    {{-- SEO CHECKER --}}
                    <x-cerberus::seo.seo-checker :model="$post" />

                </div>

                <div class="col-span-4">
                    <x-cerberus::form.title-block class="p-3">
                        <h3 class="text-white">MAIN IMAGE ATTRIBUTES</h3>
                    </x-cerberus::form.title-block>
                    <x-cerberus::form.field-block class="p-3">
                        <x-cerberus::form.label for="image">
                            ΚΕΝΤΡΙΚΗ ΕΙΚΟΝΑ
                            @if ($post->mainImage())
                                <img src="{{ asset('storage/' . $post->imageSm()) }}" alt="">

                                <label class="block mb-3 mt-2" for="deleteImage">
                                    Επιλέξτε για διαγραφή της εικόνας
                                    <input type="checkbox" name="deleteImage" id="deleteImage">
                                </label>
                            @endif
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="image" name="image" type="file"
                            value="{{ $post->mainImage() }}" />

                        <x-cerberus::form.error value="image" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="alt[{{ $locale }}]">
                            ALT TAG {{ __('cerberus::base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                            value="{{ old('alt.' . $locale, $post?->mainImage()?->getTranslation('alt', $locale, false)) }}" />
                        <x-cerberus::form.error value="alt.{{ $locale }}" />
                    </x-cerberus::form.field-block>


                    <x-cerberus::form.title-block>
                        <h3 class="text-white">CATEGORIES & TAGS</h3>
                    </x-cerberus::form.title-block>

                    <div class="divide-y-4">

                        <livewire:cerberus::tags.show :locale="$locale" :model="$post" :category="true" />
                        <livewire:cerberus::tags.show :locale="$locale" :model="$post" />
                    </div>

                    <div class="">
                        <x-cerberus::form.title-block>
                            <h3 class="text-white">PUBLISHING OPTIONS</h3>
                        </x-cerberus::form.title-block>

                        <livewire:cerberus::elements.publish-button :model="$post" />
                        <livewire:cerberus::elements.featured-button :model="$post" />

                        <x-cerberus::form.field-block>
                            <x-cerberus::form.label for="created_at">
                                ΗΜΕΡΟΜΗΝΙΑ ΔΗΜΙΟΥΡΓΙΑΣ
                            </x-cerberus::form.label>
                            <x-cerberus::form.input id="created_at" name="created_at" disabled class="!bg-slate-300"
                                value="{{ old('published_at', \Carbon\Carbon::parse($post->created_at)->format('d-m-Y H:i')) }}" />
                            <x-cerberus::form.error value="published_at" />
                        </x-cerberus::form.field-block>

                        <x-cerberus::form.field-block>
                            <x-cerberus::form.label for="created_at">
                                ΤΕΛΕΥΤΑΙΑ ΕΝΗΜΕΡΩΣΗ
                            </x-cerberus::form.label>
                            <x-cerberus::form.input id="created_at" name="created_at" disabled class="!bg-slate-300"
                                value="{{ old('published_at', \Carbon\Carbon::parse($post->updated_at)->format('d-m-Y H:i')) }}" />
                            <x-cerberus::form.error value="published_at" />
                        </x-cerberus::form.field-block>

                        <x-cerberus::form.field-block>
                            <x-cerberus::form.label for="published_at">
                                ΗΜΕΡΟΜΗΝΙΑ ΔΗΜΟΣΙΕΥΣΗΣ
                            </x-cerberus::form.label>
                            <x-cerberus::form.input id="published_at" name="published_at"
                                value="{{ old('published_at', \Carbon\Carbon::parse($post->published_at)->format('d-m-Y H:i')) }}" />
                            <x-cerberus::form.error value="published_at" />
                        </x-cerberus::form.field-block>

                    </div>
                </div>

            </div>

            <div class="flex items-center flex-wrap justify-between px-4">

                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.posts.index') }}"
                    translationRoute="{{ route('cerberus.posts.en.edit', $post) }}" />

    </form>

    <div class="grow flex justify-end gap-6">

        <x-cerberus::form.delete deleteRoute="{{ route('cerberus.posts.destroy', $post) }}" />
    </div>


    </div>

    </div>

</x-cerberus::cerberus>
