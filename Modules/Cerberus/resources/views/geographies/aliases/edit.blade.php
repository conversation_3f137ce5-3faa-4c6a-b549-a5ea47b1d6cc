<x-cerberus::cerberus>
    <x-slot:header>
        <h1> Επεξεργα<PERSON><PERSON><PERSON> {{ $alias->name }} </h1>
    </x-slot:header>


    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />


    <div class="w-full px-4 block mx-auto bg-white mt-4">
        <form action="{{ route('cerberus.aliases.update', $alias) }}" method="POST" class="w-4/5 mx-auto">
            @csrf
            @method('PATCH')

            <x-cerberus::form.field-block>
                <div>Alias Type: {{ Str::of($alias->aliasable_type)->explode('\\')->last() }}</div>
                <input type="hidden" name="aliasbale_type" value="{{ $alias->aliasable_type }}">
                <div>Basic Name: {{ $alias->aliasable->name }}</div>
            </x-cerberus::form.field-block>

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="name">
                    Alias
                </x-cerberus::form.label>
                <x-cerberus::form.input id="name" name="name" value="{{ old('name', $alias->name) }}" />
                <x-cerberus::form.error value="name" />
            </x-cerberus::form.field-block>

            <div class="flex items-center justify-between px-4">

                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.aliases.index') }}" />
        </form>
        <x-cerberus::form.delete deleteRoute="{{ route('cerberus.aliases.destroy', $alias) }}" :prompt="$similar_aliases_count == 1 ? 'Είναι το τελευταίο alias για αυτό το μοντέλο! Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;;' : 'Θέλετε να προχωρήσετε σε διαγραφή;'" />
    </div>

    </div>


</x-cerberus::cerberus>
