<x-cerberus::cerberus>
    <x-slot:header>
        <h1> ΕΠΕΞΕΡΓΑΣΙΑ ΧΩΡΑΣ: {{ $country->name }} </h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />

    <div class="w-full px-4 block mx-auto bg-white mt-4">
        <form action="{{ route('cerberus.countries.update', $country) }}" method="POST" class="w-4/5 mx-auto">
            @csrf
            @method('PATCH')

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="name">
                    Name
                </x-cerberus::form.label>
                <x-cerberus::form.input id="name" name="name" value="{{ old('name', $country->name) }}" />
                <x-cerberus::form.error value="name" />
            </x-cerberus::form.field-block>

               <x-cerberus::form.field-block>
              <x-cerberus::form.label
                for="iso_code"
               >
                ISO Code 
               </x-cerberus::form.label>
               <x-cerberus::form.input
                 id="iso_code"
                 name="iso_code"
                 value="{{ old('iso_code', $country->iso_code) }}"
                />
                <x-cerberus::form.info>
                    ISO 3166-1 alpha-3 - 3 ψηφία
                   </x-cerberus::form.info>
                <x-cerberus::form.error value="iso_code" />
            </x-cerberus::form.field-block>

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="subregion_id">
                    SubRegion
                </x-cerberus::form.label>

                <select name="subregion_id" id="subregion_id" class="w-64">
                    <option value="" selected @disabled(true)>Select Subregion</option>

                    @foreach ($subregions as $subregion)
                        <option value="{{ $subregion->id }}" @selected(old('subregion_id', $country->subregion_id) == $subregion->id)>
                            {{ $subregion->name }}
                        </option>
                    @endforeach

                </select>
                <x-cerberus::form.error value="subregion_id" />

            </x-cerberus::form.field-block>

            <div class="flex items-center justify-between px-4">

                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.countries.index') }}" />
        </form>
        <x-cerberus::form.delete deleteRoute="{{ route('cerberus.countries.destroy', $country) }}" />
    </div>
    </div>

</x-cerberus::cerberus>
