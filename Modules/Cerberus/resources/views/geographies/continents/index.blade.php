<x-cerberus::cerberus>
    <x-slot:header>
        <h1> CONTINENTS </h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />

    <div class="w-full px-4 block mx-auto bg-white mt-4">

        <div class="mb-4 p-4 flex justify-end">

        <div>
            <x-cerberus::form.link href="{{ route('cerberus.continents.create') }}"
                class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
                Προσθήκη
            </x-cerberus::form.link>
        </div>
    </div>



        <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
            <thead class="bg-gray-300 text-left">
                <th class="p-2">ID</th>
                <th class="p-2">Continent Name</th>
                <th class="p-2">SubRegions</th>
                <th class="p-2"></th>
            </thead>
            <tbody class="divide-y-2">
                @foreach ($continents as $continent)
                    <tr>
                        <td class="p-2 w-12">{{ $continent->id }}</td>

                        <td class="p-2">
                            <div class="pb-2">
                                <a class="font-semibold hover:underline"
                                    href="{{ route('cerberus.continents.edit', $continent) }}">
                                    {{ $continent->name }}

                                </a>
                            </div>
                        </td>

                        <td class="p-2">
                            {{ $continent->subregions->count() }}
                        </td>

                        <td>
                            <div class="flex">
                                <x-cerberus::form.link href="{{ route('cerberus.regions.edit', $continent) }}"
                                    class="focus:ring-transparent">
                                    <x-cerberus::icons.edit-icon />
                                </x-cerberus::form.link>
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

</x-cerberus::cerberus>
