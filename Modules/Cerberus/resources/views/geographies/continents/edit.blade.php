<x-cerberus::cerberus>
    <x-slot:header>
        <h1> ΕΠΕΞΕΡΓΑΣΙΑ CONTINENT: {{ $continent->name }} </h1>
    </x-slot:header>


    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />

    <div class="w-full px-4 block mx-auto bg-white mt-4">

        <form action="{{ route('cerberus.continents.update', $continent) }}" method="POST" class="w-4/5 mx-auto">
            @csrf
            @method('PATCH')

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="name">
                    Name
                </x-cerberus::form.label>
                <x-cerberus::form.input id="name" name="name" value="{{ old('name', $continent->name) }}" />
                <x-cerberus::form.error value="name" />
            </x-cerberus::form.field-block>

            <div class="flex items-center justify-between px-4">

                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.continents.index') }}" />
        </form>
        <x-cerberus::form.delete deleteRoute="{{ route('cerberus.continents.destroy', $continent) }}" />
    </div>

    </div>
</x-cerberus::cerberus>
