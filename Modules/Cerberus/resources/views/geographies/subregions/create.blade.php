<x-cerberus::cerberus>
    <x-slot:header>
        <h1> ΠΡΟΣΘΗΚΗ SUBREGION </h1>
    </x-slot:header>

    <div class="w-full px-4 block mx-auto bg-white mt-4">
        <form action="{{ route('cerberus.subregions.store') }}" method="POST" class="w-4/5 mx-auto">
            @csrf

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="name">
                    Name
                </x-cerberus::form.label>
                <x-cerberus::form.input id="name" name="name" value="{{ old('name') }}" />
                <x-cerberus::form.error value="name" />
            </x-cerberus::form.field-block>

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="continent_id">
                    Continent
                </x-cerberus::form.label>

                <select name="continent_id" id="continent_id" class="w-64">
                    <option value="" selected @disabled(true)>Select Continent</option>

                    @foreach ($continents as $continent)
                        <option value="{{ $continent->id }}" @selected(old('continent_id') == $continent->id)>
                            {{ $continent->name }}
                        </option>
                    @endforeach

                </select>
                <x-cerberus::form.error value="continent_id" />

            </x-cerberus::form.field-block>

                  <div class="flex items-center justify-between px-4">

            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.subregions.index') }}" />

        </form>
    </div>
    </div>

</x-cerberus::cerberus>
