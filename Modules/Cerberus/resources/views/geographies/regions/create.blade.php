<x-cerberus::cerberus>
    <x-slot:header>
        <h1> ΠΡΟΣΘΗΚΗ REGION </h1>
    </x-slot:header>

    <div class="w-full px-4 block mx-auto bg-white mt-4">
        <form action="{{ route('cerberus.regions.store') }}" method="POST" class="w-4/5 mx-auto">
            @csrf

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="country_id">
                    Country
                </x-cerberus::form.label>

                <select name="country_id" id="country_id">
                    <option selected disabled>Select Country</option>
                    @foreach ($countries as $country)
                        <option value="{{ $country->id }}" @selected(old('country_id') == $country->id )>
                            {{ $country->name }}
                        </option>
                    @endforeach
                </select>
                <x-cerberus::form.error value="country_id" />
            </x-cerberus::form.field-block>

            <x-cerberus::form.field-block>
              <x-cerberus::form.label
                for="name"
               >
                Name
               </x-cerberus::form.label>
               <x-cerberus::form.input
                 id="name"
                 name="name"
                 value="{{ old('name') }}"
                />
                <x-cerberus::form.error value="name" />
            </x-cerberus::form.field-block>

              <div class="flex items-center justify-between px-4">

                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.regions.index') }}" />

        </form>
    </div>
    </div>
</x-cerberus::cerberus>
