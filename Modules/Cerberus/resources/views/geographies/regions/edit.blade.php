<x-cerberus::cerberus>
    <x-slot:header>
        <h1> ΕΠΕΞΕΡΓΑΣΙΑ REGION {{ $region->name }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />

    <div class="w-full px-4 block mx-auto bg-white mt-4">
        <form action="{{ route('cerberus.regions.update', $region) }}" method="POST" class="w-4/5 mx-auto">
            @csrf
            @method('PATCH')

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="country_id">
                    Country
                </x-cerberus::form.label>

                <select name="country_id" id="country_id">
                    <option selected disabled>Select Country</option>
                    @foreach ($countries as $country)
                        <option value="{{ $country->id }}" @selected(old('country_id', $region->country->id) == $country->id)>
                            {{ $country->name }}
                        </option>
                    @endforeach
                </select>
                <x-cerberus::form.error value="country_id" />
            </x-cerberus::form.field-block>

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="name">
                    Name
                </x-cerberus::form.label>
                <x-cerberus::form.input id="name" name="name" value="{{ old('name', $region->name) }}" />
                <x-cerberus::form.error value="name" />
            </x-cerberus::form.field-block>

            <div class="flex items-center justify-between px-4">

                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.regions.index') }}" />

        </form>
        <x-cerberus::form.delete deleteRoute="{{ route('cerberus.regions.destroy', $region) }}" />
    </div>
    </div>
</x-cerberus::cerberus>
