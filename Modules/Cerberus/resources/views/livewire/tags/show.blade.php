<div class="{{ !$category ? 'pt-4' : '' }}">

    <x-cerberus::form.field-block>
        <x-cerberus::form.label for="tags">
            {{ $category ? 'CATEGORIES' : 'TAGS' }}
        </x-cerberus::form.label>
        <x-cerberus::form.info class="mb-4">
            Η ενημέρωση γίνεται αυτόματα. Δεν απαιτείται αποθήκευση
        </x-cerberus::form.info>

        <div id="tags" class="flex flex-wrap gap-2">

            @foreach ($tags as $tag)
                @if ($tag->type == 'tag')
                    <div class="p-2 rounded-md cursor-pointer shadow-md hover:shadow-none
                        {{ $model->tags->contains($tag) ? 'bg-slate-400 text-white border hover:border-slate-400 hover:bg-white hover:text-slate-800' : 'border border-slate-400 hover:bg-slate-400 hover:text-white' }}"
                        wire:click="updateTag({{ $tag }})">
                        {{ $tag->getTranslation('title', $locale) }}
                    </div>
                @elseif ($tag->type == 'category')
                    <div class="p-2 rounded-md cursor-pointer shadow-md hover:shadow-none
                        {{ $model->categories->contains($tag) ? 'bg-slate-400 text-white border hover:border-slate-400 hover:bg-white hover:text-slate-800' : 'border border-slate-400 hover:bg-slate-400 hover:text-white'  }}"
                        wire:click="updateTag({{ $tag }})">
                        {{ $tag->getTranslation('title', $locale) }}
                    </div>
                @endif
            @endforeach

        </div>


    </x-cerberus::form.field-block>


</div>
