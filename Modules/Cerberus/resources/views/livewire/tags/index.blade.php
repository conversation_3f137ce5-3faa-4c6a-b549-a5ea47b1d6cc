<div class="w-full px-4 block mx-auto bg-white mt-4">

    <div>
        <div class="flex justify-between p-4 gap-6 items-center">
            <div class="flex gap-2 items-center">
                <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
                <x-cerberus::form.helper>Αναζήτηση σε τίτλο</x-cerberus::form.helper>
            </div>
            <x-cerberus::form.link href="{{ route('cerberus.tags.create') }}" class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
                Προσθήκη
            </x-cerberus::form.link>
        </div>
    </div>

    <table class="w-full table-auto divide-solid divide-white divide-y-4 mb-8">
        <thead class="bg-slate-600 text-white text-left">
            <th class="p-2">ID</th>
            <th class="p-2 text-center">Γλώσσες</th>
            <th class="p-2">Τίτλος Tag</th>
            <th class="p-2 text-center">Posts</th>
            <th class="p-2 text-center">Packages</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2 divide-slate-300" x-data="{ openRow: null }">
            @foreach ($tags as $tag)
                <tr>
                    <td class="p-2 w-12">{{ $tag->id }}</td>
                    <td class="p-2 w-18 ">
                        @foreach ($tag->locales() as $locale)
                            <div class="mb-2">
                                <x-cerberus::icons.flags :lang="$locale" class="block mx-auto" />
                            </div>
                        @endforeach
                    </td>
                    <td class="p-2">
                        @foreach ($tag->locales() as $locale)
                            <div class="mb-2">
                                <a class="font-bold cursor-pointer" href="{{ route('cerberus.tags.edit', $tag) }}">
                                    {{ $tag->getTranslation('title', $locale) }}
                                </a>
                            </div>
                        @endforeach
                    </td>
                    <td class="text-center">
                        {{ $tag->posts_count }}
                    </td>
                    <td class="text-center">
                        {{ $tag->packages_count }}
                    </td>
                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.tags.edit', $tag) }}">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            <button wire:click.prevent="deleteTag({{ $tag }})"
                                wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                <x-cerberus::icons.delete-icon class="text-red-500" />
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $tags->links() }}

</div>
