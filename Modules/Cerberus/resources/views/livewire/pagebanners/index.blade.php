<div class="w-full px-4 block mx-auto bg-white mt-4">

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex justify-between p-4 gap-6 items-center">

        {{-- <div class="flex gap-2 items-center">
            <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
            <x-cerberus::form.helper>Αναζήτηση σε τίτλο, περιεχόμενο, tagline</x-cerberus::form.helper>
        </div> --}}

        <div>
            <div class="cursor-pointer" wire:click="$toggle('featured')">

            </div>
        </div>

        <x-cerberus::form.link href="{{ route('cerberus.pagebanners.create') }}"
            class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
            Προσθήκη
        </x-cerberus::form.link>
    </div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Published</th>
            <th class="p-2">Banner Image</th>
            <th class="p-2">Categories</th>
            <th class="p-2">Tags</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2 divide-x-4">
            @foreach ($pagebanners as $banner)
                <tr>
                    <td class="p-2 w-12">{{ $banner->id }}</td>
                    <td class="p-2 w-12">
                        <div class="cursor-pointer" wire:click="togglePublished({{ $banner->id }})"
                            wire:confirm="Θέλετε σίγουρα να προχωρήσετε;">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $banner->published ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $banner->published ? 'Δημοσιευμένο' : 'Μη δημοσιευμένο' }}">
                            </span>
                        </div>
                    </td>

                    <td class="p-2">
                        @if ($banner->filename)
                            <a href="{{ route('cerberus.pagebanners.edit', $banner) }}">
                                <img src="{{ asset('storage/pagebanners/' . $banner->filename) }}" alt=""
                                    class="w-32">
                            </a>
                        @endif
                    </td>
                    <td class="p-2">
                        <div class="flex flex-wrap gap-1">
                            @foreach ($banner->categories as $category)
                                <div class="bg-slate-200 p-1 rounded">
                                    {{ $category->getTranslation('title', 'el') }}
                                </div>
                            @endforeach
                        </div>
                    </td>
                    <td class="p-2">
                        <div class="flex flex-wrap gap-1">
                            @foreach ($banner->tags as $tag)
                                <div class="bg-slate-200 p-1 rounded">
                                    {{ $tag->getTranslation('title', 'el') }}
                                </div>
                            @endforeach
                        </div>
                    </td>

                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.pagebanners.edit', $banner) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            <button wire:click.prevent="deleteBanner({{ $banner->id }})"
                                wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                <x-cerberus::icons.delete-icon class="text-red-500" />
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $pagebanners->links() }}
</div>
