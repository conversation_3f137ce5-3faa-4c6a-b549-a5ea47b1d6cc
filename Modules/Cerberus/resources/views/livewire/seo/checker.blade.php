<div class="relative">
    <x-cerberus::form.field-block class="w-full">
        <x-cerberus::form.label for="keyword">
            Target Keyword / Keyphrase
        </x-cerberus::form.label>
        <div class="flex gap-2 items-center">
            <x-cerberus::form.input id="keyword" wire:model.live="keyword" />

            <x-cerberus::form.button wire:click.prevent='checkSeo' class="bg-slate-400 text-white px-2 py-1 rounded-md">
                Use
            </x-cerberus::form.button>
        </div>

        <x-cerberus::form.error value="keyword" />
    </x-cerberus::form.field-block>

    <h3 class="ml-6 mb-3 font-semibold">SEO Analysis</h3>

    @if ($keyword)
        @include('cerberus::livewire.seo.partials.__keyword')
    @else
        <div class="flex gap-2 items-center px-3">
            <x-cerberus::seo.signal type="fail" />
            <p>Φράση-κλειδί: Παρα<PERSON><PERSON>λ<PERSON> εισάγετε μια φράση-κλειδί.</p>
        </div>
    @endif



    <div x-data="{ show: false }" class="border border-slate-300">
        <h4 @click.prevent="show = !show" class="ml-4 text-lg font-semibold py-1 shadow-md">Meta Data
        </h4>
        <div >
            {{-- Check meta title --}}
            @include('cerberus::livewire.seo.partials.__metatitle')

            {{-- Check meta description --}}
            @include('cerberus::livewire.seo.partials.__metadescription')
        </div>
    </div>

    <div x-data="{ show: false }" class="border border-slate-300">
        <h4 @click.prevent="show = !show" class="ml-4 text-lg font-semibold py-1 shadow-md">Content
        </h4>
        <div >
            {{-- Check headings --}}
            @include('cerberus::livewire.seo.partials.__headings')

            {{-- Check Content Length --}}
            @include('cerberus::livewire.seo.partials.__content')
        </div>
    </div>

    <div x-data="{ show: false }" class="border border-slate-300">
        <h4 @click.prevent="show = !show" class="ml-4 text-lg font-semibold py-1 shadow-md">Image
        </h4>
        <div >

            {{-- Check Image --}}
            @include('cerberus::livewire.seo.partials.__image')
        </div>
    </div>
</div>
