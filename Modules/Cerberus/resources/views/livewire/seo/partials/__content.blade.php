{{-- Check content word count --}}
<div class="flex gap-2 items-center px-3">
    @if ($wordCount >= 300)
        <x-cerberus::seo.signal type="pass" />
        <p>Μήκος κειμένου: Το κείμενο περιλαμβάνει {{ $wordCount }} λέξεις. Καλή δουλειά!</p>
    @elseif ($wordCount >= 250)
        <x-cerberus::seo.signal type="intermediate" />
        <p>
            Μήκος κειμένου: Το κείμενο περιέχει {{ $wordCount }} λέξεις. Αυτό είναι ελαφρώς χαμηλότερο από το
            προτεινόμενο
            ελάχιστο των 300 λέξεων. Προσθέστε λίγο περισσότερο κείμενο.
        </p>
    @elseif($wordCount >= 200)
        <x-cerberus::seo.signal type="fail" />
        <p>
            <PERSON><PERSON><PERSON><PERSON> κειμένου: Το κείμενο περιέχει {{ $wordCount }} λέξεις. Αυτό είναι κάτω από το προτεινόμενο ελάχιστο
            των
            300 λέξεων. Προσθέστε
            περισσότερο περιεχομένο.
        </p>
    @else
        <x-cerberus::seo.signal type="fail" />
        <p>
            Μήκος κειμένου: Το κείμενο περιέχει {{ $wordCount }} λέξεις. Αυτό είναι πολύ χαμηλότερο από το
            προτεινόμενο
            ελάχιστο των 300 λέξεων. Προσθέστε
            περισσότερο περιεχομένο.
        </p>
    @endif
</div>

{{-- Check 1st paragraph for keyword --}}
<div class="flex gap-2 items-center px-3">
    @if ($keyword)
        @if ($keywordInFirstParagraph)
            <x-cerberus::seo.signal type="pass" />
            <p>Φράση-κλειδί στην εισαγωγή: Εξαιρετικά!</p>
        @else
            <x-cerberus::seo.signal type="fail" />
            <p>
                Φράση-κλειδί στην εισαγωγή: Η φράση-κλειδί δεν εμφανίζεται στην πρώτη παράγραφο.
                Βεβαιωθείτε ότι το θέμα γίνεται απευθείας ξεκάθαρο.
            </p>
        @endif
    @endif

</div>

{{-- Check keyword density --}}
<div class="flex gap-2 items-center px-3">
    @if ($keyword)
        @if ($density == 0)
            <x-cerberus::seo.signal type="fail" />
            <p>Πυκνότητα φράσης-κλειδιού: Η συγκεκριμένη φράση κλειδί βρέθηκε 0 φορές. Αυτές είναι λιγότερες από το
                προτεινόμενο
                ελάχιστο για ένα κείμενο αυτής της έκτασης. Εστιάστε στη φράση-κλειδί!</p>
        @elseif ($density > 2.5)
            <x-cerberus::seo.signal type="fail" />
            <p>
                Πυκνότητα φράσης-κλειδιού: Η φράση-κλειδί βρέθηκε {{ $keywordCount }} φορές. Αυτό είναι παραπάνω από το
                συνιστώμενο
                μέγιστο για ένα κείμενο αυτής της έκτασης. Μην υπερβάλλετε!
            </p>
        @elseif ($density < 0.5)
            <x-cerberus::seo.signal type="fail" />
            <p>Πυκνότητα φράσης-κλειδιού: Η συγκεκριμένη φράση κλειδί βρέθηκε {{ $keywordCount }} φορές. Αυτές είναι
                λιγότερες από το προτεινόμενο
                ελάχιστο για ένα κείμενο αυτής της έκτασης. Εστιάστε στη φράση-κλειδί!</p>
        @else
            <x-cerberus::seo.signal type="pass" />
            <p>Πυκνότητα φράσης-κλειδιού: Η φράση-κλειδί βρέθηκε {{ $keywordCount }} φορές. Αυτό είναι υπέροχο!</p>
        @endif
    @endif

</div>
