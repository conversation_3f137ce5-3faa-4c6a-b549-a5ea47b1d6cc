 {{-- Check meta description length --}}
 <div class="flex gap-1 items-center px-3">
     @if ($metaDescriptionLength && $metaDescriptionLength > 156)
         <x-cerberus::seo.signal type="fail" />
         <p>
             Μήκος μετα-περιγραφής: Η μετα-περιγραφή είναι πάνω από 156 χαρακτήρες. Για να διασφαλίσετε πως θα είναι
             ορατη ολόκληρη η περιγραφή, θα πρέπει να μειώσετε το μήκος της!
         </p>
     @elseif ($metaDescriptionLength && $metaDescriptionLength < 156)
         <x-cerberus::seo.signal type="pass" />
         <p>
             Μήκος μετα-περιγραφής: Πολύ καλά!
         </p>
     @else
         <x-cerberus::seo.signal type="fail" />
         <p>
             Meta description length: No meta description has been specified. Search engines will display copy from the
             page instead. Make sure to write one!
         </p>
     @endif
 </div>

 {{-- Check metadescription contains keyphrase --}}
 @if ($keyword && $metaDescriptionLength)
     <div class="flex gap-1 items-center px-3">
         @if ($keywordInMetaDescription)
             <x-cerberus::seo.signal type="pass" />
             <p>
                 Φράση-κλειδί στη μετα-περιγραφή: Η φράση-κλειδί εμφανίζεται στην μετα-περιγραφή. Καλή
                 δουλειά!
             </p>
         @else
             <x-cerberus::seo.signal type="fail" />
             <p>
                 Φράση-κλειδί στη μετα-περιγραφή: Η μετα-περιγραφή έχει οριστεί, αλλά δεν διαθέτει τη φράση-κλειδί.
                 Διορθώστε το!
             </p>
         @endif
     </div>
 @endif
