{{-- Keyword Length --}}
<div class="flex gap-1 items-center px-3">
    @if ($keyLength == 0)
        <x-cerberus::seo.signal type="fail" />
    @elseif($keyLength > 4)
        <x-cerberus::seo.signal type="intermediate" />
    @else
        <x-cerberus::seo.signal type="pass" />
    @endif
    <p>Μήκος λέξης / φράσης κλειδί:
        @if ($keyLength == 0)
            Δεν έχει οριστεί φράση-κλειδί για αυτήν την σελίδα
        @elseif($keyLength > 4)
            Η φράση κλειδί περιέχει {{ $keyLength }} λέξεις περιεχομένου. Αυτό είναι περισσότερο από το
            συνιστώμενο μέγιστο 4 λέξεις περιεχομένου.
        @else
            Καλή δουλειά!
        @endif
    </p>
</div>

{{-- Keyword Used Before --}}

<div class="flex gap-1 items-center px-3">
    @if ($keyword && $keywordOccurences > 2)
        <x-cerberus::seo.signal type="fail" />
        <p>Φράση κλειδί που έχει χρησιμποιηθεί συνολικά {{ $keywordOccurences }} φορές</p>
    @elseif($keyword && $keywordOccurences > 1)
        <x-cerberus::seo.signal type="intermediate" />
        <p>Φράση κλειδί που έχει χρησιμποιηθεί συνολικά {{ $keywordOccurences }} φορές</p>
    @elseif($keyword)
        <x-cerberus::seo.signal type="pass" />
        <p>Δεν έχετε χρησιμοποιήσει αυτή τη φράση-κλειδί στο παρελθόν, εξαιρετικά.</p>

    @endif
</div>

{{-- If used show where --}}
@if ($keyword && $keywordOccurences > 1)
    <ul class="ml-8">
        @foreach ($keywordModels as $row)
            <li>
                <a href="{{ route('cerberus.' . Str::lower(Str::of($row->seoable_type)->explode('\\')->last()) . 's.edit', $row->seoable->id) }}"
                    target="_blank" class="text-pacific-600">{{ $row->seoable->title }}</a>
            </li>
        @endforeach
    </ul>
@endif
