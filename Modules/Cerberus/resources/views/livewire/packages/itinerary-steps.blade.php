<div>

    <x-cerberus::form.field-block>
        <x-cerberus::form.title-block>
            <h2 class="text-white">{{ $isFoodTour ? 'FAQ' : 'ITINERARY STEPS' }}</h2>
        </x-cerberus::form.title-block>

        <div wire:sortable="updateStepsOrder" wire:sortable.options="{ animation: 100 }">
            @foreach ($steps as $step)
                <div class="border rounded px-2 py-3" x-data="{ showStep: false }" wire:sortable.item="{{ $step->id }}"
                    wire:key="step-{{ $step->id }}">
                    <div class="flex justify-between items-center pl-2">
                        <h3 @click="showStep = !showStep"
                            class="px-4 py-2 rounded bg-gray-200 border border-slate-800 mb-2 grow flex gap-2">
                            @if ($loop->count > 1)
                                <x-cerberus::icons.move-icon />
                            @endif
                            {{ $step->title }}
                        </h3>
                        <x-cerberus::icons.delete-icon class="text-red-500 cursor-pointer"
                            wire:click="deleteStep({{ $step }})" />
                    </div>
                    <input type="hidden" name="steps[{{ $loop->iteration }}][id]" value="{{ $step->id }}">
                    <div x-show="showStep">
                        <livewire:cerberus::packages.itinerary-step-item :step="$step" :locale="$locale"
                            wire:key='{{ $step->id }}' />
                    </div>
                </div>
            @endforeach
        </div>

        

    </x-cerberus::form.field-block>

    <x-cerberus::alerts.flash type="success" timeout=10 />
</div>
