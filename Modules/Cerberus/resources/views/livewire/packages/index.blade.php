<div class="w-full px-4 block mx-auto bg-white mt-4">

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex justify-between p-4 gap-6 items-center">

        <div class="flex gap-2 items-center">
            <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
            <x-cerberus::form.helper>Αναζήτηση σε τίτλο, περιεχόμενο, tagline</x-cerberus::form.helper>
        </div>

        <div class="flex justify-center gap-4">
            <div>
                <div class="cursor-pointer" wire:click="$toggle('featured')">
                    @if ($featured)
                        <div
                            class="bg-pacific-500 hover:bg-white hover:border hover:border-slate-800 hover:text-slate-800 text-white px-2 py-1 rounded">
                            Show Featured & Not Featured</div>
                    @else
                        <div class="border border-slate-400 hover:bg-pacific-500 hover:text-white px-2 py-1 rounded">
                            Show Featured Only</div>
                    @endif
                </div>
            </div>
            <div>
                <div class="cursor-pointer" wire:click="$toggle('published')">
                    @if ($published)
                        <div
                            class="bg-pacific-700 hover:bg-white hover:border hover:border-slate-800 hover:text-slate-800 text-white px-2 py-1 rounded">
                            Show Published & Unbublished</div>
                    @else
                        <div class="border border-slate-400 hover:bg-pacific-700 hover:text-white px-2 py-1 rounded">
                            Show Published Only</div>
                    @endif
                </div>
            </div>
        </div>

        <x-cerberus::form.link href="{{ route('cerberus.packages.create') }}"
            class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
            Προσθήκη
        </x-cerberus::form.link>
    </div>

    <div class="flex flex-wrap items-center gap-6 p-4 mb-6">
        <div>
            <select wire:model.live="type" id="type" class="w-36">
                <option value="">Select type</option>
                <option value="travel">Travel</option>
                <option value="cruise">Cruise</option>
            </select>
        </div>
        <div>
            <select wire:model.live="category" id="type" class="w-full">
                <option value="">Select category</option>

                @foreach ($categories as $category)
                    <option value="{{ $category->id }}">{{ $category->title }}</option>
                @endforeach
            </select>
        </div>

        <div>
            <select wire:model.live="tag" id="type" class="w-full">
                <option value="">Select tag</option>

                @foreach ($tags as $tag)
                    <option value="{{ $tag->id }}">{{ $tag->title }}</option>
                @endforeach
            </select>
        </div>
        <div class="ml-auto">
            <x-cerberus::form.link href="" wire:click.prevent="export()"
                class="text-white bg-gray-500 hover:bg-gray-700 focus:ring-gray-700 focus:ring-offset-gray-500"
            >
                Export
            </x-cerberus::form.link>
        </div>
    </div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Pubished</th>
            <th class="p-2">Featured</th>
            <th class="p-2">Languages</th>
            <th class="p-2">Main Image</th>
            <th class="p-2">Title</th>
            <th class="p-2">Type</th>
            <th class="p-2 text-center">Export <br> <input type="checkbox" wire:model.live='selectAll'></th>
            <th class="p-2">Created At</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($packages as $package)
                <tr>
                    <td class="p-2 w-12">{{ $package->id }}</td>
                    <td class="p-2 w-12">
                        <div class="cursor-pointer" wire:click="togglePublished({{ $package }})"
                            wire:confirm="Θέλετε σίγουρα να προχωρήσετε;">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $package->published ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $package->published ? 'Δημοσιευμένο' : 'Μη δημοσιευμένο' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2 w-12">
                        <div class="cursor-pointer" wire:click="toggleFeatured({{ $package }})"
                            wire:confirm="Θέλετε σίγουρα να προχωρήσετε;">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $package->featured ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $package->featured ? 'Προβεβλημένο' : 'Μη προβεβλημένο' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2 w-18">
                        @foreach ($package->locales() as $locale)
                            <x-cerberus::icons.flags :lang="$locale" class="block mx-auto" />
                        @endforeach
                    </td>
                    <td class="p-2">
                        @if ($package->mainImage())
                            <img src="{{ asset('/storage/' . $package->imageSm()) }}" alt="" class="w-32">
                        @endif
                    </td>
                    <td class="p-2">
                        @foreach ($package->locales() as $locale)
                            <div class="pb-2">
                                <a class="font-bold"
                                    href="{{ $locale == 'el' ? route('cerberus.packages.edit', $package) : route('cerberus.packages.en.edit', $package) }}">
                                    {{ $package->getTranslation('title', $locale) }}
                                </a>
                            </div>
                        @endforeach
                    </td>
                    <td class="p-2 capitalize">
                        {{ $package->type }}
                    </td>
                    <td class="p-2 text-center">
                        <input type="checkbox" wire:model.live="selectedPackages" value="{{ $package->id }}">

                    </td>
                    <td class="p-2">
                        {{ $package->created_at }}
                    </td>
                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.packages.edit', $package) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            <button wire:click.prevent="deletePackage({{ $package->id }})"
                                wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                <x-cerberus::icons.delete-icon class="text-red-500" />
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $packages->links() }}
</div>
