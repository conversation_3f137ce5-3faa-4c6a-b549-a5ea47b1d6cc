<div>

    <x-cerberus::form.field-block x-data>
        <x-cerberus::form.label for="step-title-{{ $step->id }}" class="flex justify-between items-center">
            STEP TITLE {{ __('cerberus::base.' . $locale) }}

            @if ($locale == 'en')
                <div>
                    <livewire:cerberus::elements.suggest-translation :content="$step->getTranslation('title', 'el')" :id="'step-title-' . $step->id" />

                    <span
                        x-on:translate-step-title-{{ $step->id }}.window="document.getElementById('step-title-{{ $step->id }}').value = $event.detail.translation"></span>
                </div>
            @endif


        </x-cerberus::form.label>
        <x-cerberus::form.input id="step-title-{{ $step->id }}" wire:model='title'
            name="steps[{{ $step->order }}][title][{{ $locale }}]"
            value="{{ $step->getTranslation('title', $locale) }}" />
        @if ($locale == 'en')
            <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $step->getTranslation('title', 'el') !!}</div>
        @endif
        <x-cerberus::form.error value="title" />

    </x-cerberus::form.field-block>

    <x-cerberus::form.field-block x-data>
        <x-cerberus::form.label for="step-content-{{ $step->id }}" class="flex justify-between items-center">
            STEP CONTENT {{ __('cerberus::base.' . $locale) }}

            @if ($locale == 'en')
                <div>
                    <livewire:cerberus::elements.suggest-translation :content="$step->getTranslation('content', 'el')" :id="'step-content-' . $step->id" />

                    <span
                        x-on:translate-step-content-{{ $step->id }}.window="
                        let editor = tinymce.get('step-content-{{ $step->id }}');
                        if (editor) {
                            editor.setContent($event.detail.translation);
                        }
                    ">
                    </span>


                </div>
            @endif


        </x-cerberus::form.label>
        <x-cerberus::form.textarea id="step-content-{{ $step->id }}" wire:model='content'
            name="steps[{{ $step->order }}][content][{{ $locale }}]"
            class="h-[200px] {{ $locale == 'el' ? '' : '' }} editor">
            {{ $step->getTranslation('content', $locale) }}
        </x-cerberus::form.textarea>
        <x-cerberus::form.error value="content" />
        @if ($locale == 'en')
            <div class="px-3 py-1 mt-1 bg-gray-200 h-[200px] overflow-scroll">Ελληνικά: {!! $step->getTranslation('content', 'el') !!}</div>
        @endif

    </x-cerberus::form.field-block>


    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex gap-2">
        {{-- <x-cerberus::form.button class=" ml-4 bg-slate-800 text-white hover:bg-slate-500"
            wire:click.prevent="updateStep({{ $step }})">
            Ενημέρωση Βήματος
        </x-cerberus::form.button> --}}
        <x-cerberus::form.link @click="showStep = false" class="bg-slate-300 cursor-pointer">
            Κλείσιμο
        </x-cerberus::form.link>
    </div>


</div>
