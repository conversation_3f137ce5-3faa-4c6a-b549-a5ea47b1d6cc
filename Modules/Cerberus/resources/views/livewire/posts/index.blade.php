<div class="w-full px-4 block mx-auto mb-5 bg-white mt-4">

    <div class="flex justify-between p-4 gap-6 items-center">

        <x-cerberus::alerts.flash type="success" timeout=10 />

        <div class="flex gap-2 items-center">
            <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
            <x-cerberus::form.helper>Αναζήτηση σε τίτλο, περιεχόμενο, tagline</x-cerberus::form.helper>
        </div>

        <div>
            <div class="cursor-pointer" wire:click="$toggle('featured')">
                @if ($featured)
                    <div
                        class="bg-pacific-500 hover:bg-white hover:border hover:border-slate-800 hover:text-slate-800 text-white px-2 py-1 rounded">
                        Show All</div>
                @else
                    <div class="border border-slate-400 hover:bg-pacific-500 hover:text-white px-2 py-1 rounded">
                        Featured Only</div>
                @endif

            </div>
        </div>

        <x-cerberus::form.link href="{{ route('cerberus.posts.create') }}"
            class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
            Προσθήκη
        </x-cerberus::form.link>
    </div>

    <div class="flex flex-wrap items-center gap-6 p-4 mb-6">
 
        <div>
            <select wire:model.live="category" id="type" class="w-full">
                <option value="">Select category</option>

                @foreach ($categories as $category)
                    <option value="{{ $category->id }}">{{ $category->title }}</option>
                @endforeach
            </select>
        </div>

        <div>
            <select wire:model.live="tag" id="type" class="w-full">
                <option value="">Select tag</option>

                @foreach ($tags as $tag)
                    <option value="{{ $tag->id }}">{{ $tag->title }}</option>
                @endforeach
            </select>
        </div>
    </div>

    <table class="w-full table-auto divide-solid divide-white divide-y-4 mb-8">
        <thead class="bg-slate-600 text-white text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Published</th>
            <th class="p-2">Featured</th>
            <th class="p-2">Languages</th>
            <th class="p-2">Main Image</th>
            <th class="p-2">Title</th>
            <th class="p-2">Tagline</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2 divide-slate-300">
            @foreach ($posts as $post)
                <tr>
                    <td class="p-2 w-12">{{ $post->id }}</td>
                    <td class="p-2 w-12">
                        <div class="cursor-pointer" wire:click="togglePublished({{ $post }})"
                            wire:confirm="Θέλετε σίγουρα να προχωρήσετε;">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $post->published ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $post->published ? 'Δημοσιευμένο' : 'Μη δημοσιευμένο' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2 w-12">
                        <div class="cursor-pointer" wire:click="toggleFeatured({{ $post }})"
                            wire:confirm="Θέλετε σίγουρα να προχωρήσετε;">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $post->featured ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $post->featured ? 'Προβεβλημένο' : 'Μη προβεβλημένο' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2">
                        @foreach ($post->locales() as $locale)
                            <x-cerberus::icons.flags :lang="$locale" class="block mx-auto" />
                        @endforeach
                    </td>
                    <td class="p-2">
                        @if ($post->mainImage())
                            <img src="{{ asset('/storage/' . $post->imageSm()) }}" alt="" class="w-32">
                        @endif
                    </td>
                    <td class="p-2">
                        @foreach ($post->locales() as $locale)
                            <div class="pb-2">
                                <a class="font-bold"
                                    href="{{ $locale == 'el' ? route('cerberus.posts.edit', $post) : route('cerberus.posts.en.edit', $post) }}">
                                    {{ $post->getTranslation('title', $locale) }}
                                </a>
                            </div>
                        @endforeach
                    </td>
                    <td class="p-2">
                        @foreach ($post->locales() as $locale)
                            <div class="pb-2">
                                {!! $post->getTranslation('tagline', $locale) !!}
                            </div>
                        @endforeach

                    </td>
                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.posts.edit', $post) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            <button wire:click.prevent="deletePost({{ $post->id }})"
                                wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                <x-cerberus::icons.delete-icon class="text-red-500" />
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $posts->links() }}
</div>
