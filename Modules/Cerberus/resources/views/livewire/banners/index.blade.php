<div>
    
    <div wire:sortable="updateOrder" class="flex flex-wrap gap-6 items-center">
        @foreach ($bannerImages as $image)
            <div wire:sortable.item="{{ $image->id }}" wire:key="item-{{ $image->id }}">
                <div class="flex gap-1 items-center">
                    <div class="w-4 h-4 rounded-full 
                        {{ ($image->banner() && $image->banner()->published) 
                            || ($image->slider() && $image->slider()->published) 
                            || ($image->popup() && $image->popup()->published) ? 'bg-pacific-500' : 'bg-slate-400' }}" 
                ></div>
       
                    <div>
                        <a href="{{ route('cerberus.'.$type.'s.edit', $image->imageable_id) }}">
                            <img
                                class="w-48 border-2 border-gray-300 p-1"
                                src="{{ asset('storage/banners/'.$image->filename) }}" alt=""
                            >
                        </a>
                    </div>
                    
                </div>
            </div>
        @endforeach
    </div>

</div>
