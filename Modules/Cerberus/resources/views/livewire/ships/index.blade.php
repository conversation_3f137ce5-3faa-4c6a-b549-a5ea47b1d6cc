<div class="w-full px-4 block mx-auto bg-white mt-4">

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex justify-between p-4 gap-6 items-center">

        <div class="flex gap-2 items-center">
            <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
            <x-cerberus::form.helper>Αναζήτηση σε τίτλο, περιεχόμενο, tagline</x-cerberus::form.helper>
        </div>

   
        <x-cerberus::form.link href="{{ route('cerberus.ships.create') }}"
            class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
            Προσθήκη
        </x-cerberus::form.link>
    </div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Pubished</th>
            <th class="p-2">Featured</th>
            <th class="p-2">Languages</th>
            <th class="p-2">Main Image</th>
            <th class="p-2">Title</th>
            <th class="p-2">Created At</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2 divide-x-4">
            @foreach ($ships as $ship)
                <tr>
                    <td class="p-2 w-12">{{ $ship->id }}</td>
                    <td class="p-2 w-12">
                        <div class="cursor-pointer" wire:click="togglePublished({{ $ship }})" wire:confirm="Θέλετε σίγουρα να προχωρήσετε;">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $ship->published ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $ship->published ? 'Δημοσιευμένο' : 'Μη δημοσιευμένο' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2 w-12">
                        <div class="cursor-pointer" wire:click="toggleFeatured({{ $ship }})" wire:confirm="Θέλετε σίγουρα να προχωρήσετε;">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $ship->featured ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $ship->featured ? 'Προβεβλημένο' : 'Μη προβεβλημένο' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2 w-18">
                        @foreach ($ship->locales() as $locale)
                            <x-cerberus::icons.flags :lang="$locale" class="block mx-auto" />
                        @endforeach
                    </td>
                    <td class="p-2">
                        @if ($ship->mainImage())
                            <img src="{{ asset('/storage/' . $ship->imageSm()) }}" alt="" class="w-32">
                        @endif
                    </td>
                    <td class="p-2">
                        @foreach ($ship->locales() as $locale)
                            <div class="pb-2">
                                <a class="font-bold"
                                    href="{{ $locale == 'el' ? route('cerberus.ships.edit', $ship) : route('cerberus.ships.en.edit', $ship) }}">
                                    {{ $ship->getTranslation('title', $locale) }}
                                </a>
                            </div>
                        @endforeach
                    </td>
                    <td class="p-2">
   
                        {{ $ship->created_at }}
                    </td>
                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.ships.edit', $ship) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            <button wire:click.prevent="deleteShip({{ $ship->id }})"
                                wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                <x-cerberus::icons.delete-icon class="text-red-500" />
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{-- {{ $ships->links() }} --}}
</div>
