<div>
    <x-cerberus::alerts.flash type="success" />
    <x-cerberus::form.field-block>
        <div wire:sortable="updateOrder" class="flex gap-3 items-center flex-wrap">
            @foreach ($images as $image)
                <div wire:sortable.item="{{ $image->id }}" wire:key="item-{{ $image->id }}"
                    class="w-[23%] shadow-md p-1">

                    <input type="hidden" name="images[{{ $image->id }}][id]" value="{{ $image->id }}">

                    <div class="relative">
                        <img src="{{ asset('storage/galleries/' . $gallery->id . '/' . $image->filename) }}"
                            alt="">
                        <div
                            class="absolute bottom-1 left-1 z-10 w-6 h-6 text-center mt-1 bg-pacific-500 text-white rounded-full">
                            {{ $image->oder > 0 ? $image->order : $loop->iteration }}</div>
                    </div>

                    @foreach (LocaleConfig::getLocales() as $locale)
                        <x-cerberus::form.field-block class="mb-0">
                            <x-cerberus::form.label for="alt[{{ $locale }}]" class="text-sm">
                                Alt {{ __('base.' . $locale) }}
                            </x-cerberus::form.label>

                            <x-cerberus::form.input id="alt[{{ $locale }}]"
                                name="images[{{ $image->id }}][alt][{{ $locale }}]" 
                                value="{{ $image->getTranslation('alt', $locale) }}"
                                />
                            <x-cerberus::form.error value="alt.{{ $locale }}]" />

                        </x-cerberus::form.field-block>

                        <x-cerberus::form.field-block class="mb-0">
                            <x-cerberus::form.label for="caption[{{ $locale }}]" class="text-sm">
                                Caption {{ __('base.' . $locale) }}
                            </x-cerberus::form.label>

                            <x-cerberus::form.input id="caption[{{ $locale }}]"
                                name="images[{{ $image->id }}][caption][{{ $locale }}]" 
                                value="{{ $image->getTranslation('caption', $locale) }}"
                                />
                            <x-cerberus::form.error value="caption.{{ $locale }}]" />

                        </x-cerberus::form.field-block>
                    </button>
                    @endforeach

                    <div class="flex justify-end">
                        <button wire:click.prevent="deleteImage({{ $image->id }})"
                        wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;"
                        title="Delete Image"
                        >
                        <x-cerberus::icons.delete-icon class="text-red-500" />
                    </div>
                </div>
            @endforeach
        </div>
    </x-cerberus::form.field-block>
</div>
