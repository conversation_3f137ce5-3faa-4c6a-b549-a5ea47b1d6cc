<div>

    <x-cerberus::form.field-block x-data>
        <x-cerberus::form.label for="feature-title-{{ $feature->id }}" class="flex justify-between items-center">
            FEATURE TITLE {{ __('cerberus::base.' . $locale) }}

            @if ($locale == 'en')
                <div>
                    <livewire:cerberus::elements.suggest-translation :content="$feature->getTranslation('title', 'el')" :id="'feature-title-' . $feature->id" />

                    <span
                        x-on:translate-feature-title-{{ $feature->id }}.window="$wire.set('title' ,$event.detail.translation)"></span>
                </div>
            @endif

        </x-cerberus::form.label>
        <x-cerberus::form.input id="feature-title-{{ $feature->id }}" wire:model="title" />
        @if ($locale == 'en')
            <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $feature->getTranslation('title', 'el') !!}</div>
        @endif
        <x-cerberus::form.error value="title" />
        

    </x-cerberus::form.field-block>

    <x-cerberus::form.field-block x-data>
        <x-cerberus::form.label for="feature-content-{{ $feature->id }}" class="flex justify-between items-center">
            FEATURE CONTENT {{ __('cerberus::base.' . $locale) }}

            @if ($locale == 'en')
                <div>
                    <livewire:cerberus::elements.suggest-translation :content="$feature->getTranslation('content', 'el')" :id="'feature-content-' . $feature->id" />

                    <span
                        x-on:translate-feature-content-{{ $feature->id }}.window="$wire.set('content',$event.detail.translation)"></span>

                </div>
            @endif


        </x-cerberus::form.label>
        <x-cerberus::form.textarea id="feature-content-{{ $feature->id }}" wire:model="content"
            class="h-[200px] {{ $locale == 'el' ? '' : '' }}">
            {{ $content }}
        </x-cerberus::form.textarea>
        <x-cerberus::form.error value="content" />
        @if ($locale == 'en')
            <div class="px-3 py-1 mt-1 bg-gray-200 h-[200px] overflow-scroll">Ελληνικά: {!! $feature->getTranslation('content', 'el') !!}</div>
        @endif
        

        <div>
            <x-cerberus::form.label for="featureImage" >Image

                @if ($feature->bannerImage())
                    <img src="{{ asset('storage/features/'.$feature->bannerImage()->filename) }}" alt="">
                @endif

               
                

            </x-cerberus::form.label>
            <x-cerberus::form.input wire:model="featureImage" type="file" id="featureImage"/>
            <x-cerberus::form.error value="image" />
            @if ($featureImageName)
            {{ $featureImage->getClientOriginalName() }}
            @endif
        </div>


    </x-cerberus::form.field-block>
    
    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex gap-2">
        <x-cerberus::form.button class=" ml-4 bg-slate-800 text-white hover:bg-slate-500"
            wire:click.prevent="updateFeature({{ $feature }})">
            Ενημέρωση Feature
        </x-cerberus::form.button>
        <x-cerberus::form.link @click="showFeature = false" class="bg-slate-300 cursor-pointer">
            Κλείσιμο
        </x-cerberus::form.link>
    </div>


</div>
