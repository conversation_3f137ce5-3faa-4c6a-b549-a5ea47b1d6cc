<div>

    <div wire:sortable="updateFeaturesOrder" wire:sortable.options="{ animation: 100 }">
        @foreach ($features as $key => $feature)
            <div x-data="{ showFeature: false }" wire:sortable.item="{{ $feature->id }}" wire:key="feature-{{ $feature->id }}">
                <div 
                    class="flex justify-between items-center gap-2 mx-2 mb-4 bg-slate-200 py-2 px-3 rounded-md">
                    <div
                    @click="showFeature = !showFeature"
                    class="grow border-r border-slate-300"
                    >
                    {{ $feature->getTranslation('title', 'en') ? $feature->getTranslation('title', 'en') : 'EL - ' . $feature->getTranslation('title', 'el') }}</div>

                    <x-cerberus::icons.delete-icon class="text-red-500 hover:text-red-700 cursor-pointer"
                        wire:click="deleteFeature({{ $feature->id }})" />
                </div>

                <div x-show="showFeature">

                    <input type="hidden" name="feature[{{ $key }}][feature_id]" value="{{ $feature->id }}">

                    <x-cerberus::form.field-block x-data>
                        <x-cerberus::form.label for="title[{{ $key }}]" class="flex justify-between items-center">
                            Title
                            @if ($locale == 'en')
                                <div>
                                    <livewire:cerberus::elements.suggest-translation :content="$feature->getTranslation('title', 'el')"
                                        :id="'feature-title-' . $key" />

                                    <span
                                        x-on:translate-feature-title-{{ $key }}.window="document.getElementById('feature-title-{{ $key }}').value = $event.detail.translation"></span>
                                </div>
                            @endif
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="feature-title-{{ $key }}"
                            name="feature[{{ $key }}][title]"
                            value="{{ old('title[$key]', $feature->getTranslation('title', $locale)) }}" />
                        <x-cerberus::form.error value="feature.{{ $key }}.title" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="content[{{ $key }}]" class="flex justify-between items-center">
                            Content

                            @if ($locale == 'en')
                            <div>
                                <livewire:cerberus::elements.suggest-translation :content="$feature->getTranslation('content', 'el')"
                                    :id="'feature-content-' . $key" />

                                <span
                                    x-on:translate-feature-content-{{ $key }}.window="tinyMCE.get('feature-content-{{ $key }}').setContent($event.detail.translation)"></span>
                            </div>
                        @endif
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="feature-content-{{ $key }}"
                            name="feature[{{ $key }}][content]" class="editor">
                            {{ old('content[$key]', $feature->getTranslation('content', $locale)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="feature.{{ $key }}.content" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="feature-image[{{ $key }}]">
                            Image
                            @if ($feature->bannerImage())
                                <img src="{{ asset('storage/ships/features/' . $feature->bannerImage()->filename) }}"
                                    alt="">
                            @endif
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="feature-image[{{ $key }}]"
                            name="feature[{{ $key }}][image]" type="file" />
                        <x-cerberus::form.error value="feature.{{ $key }}.image" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="feature[{{ $key }}][alt]">
                            Alt
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="feature[{{ $key }}][alt]"
                            name="feature[{{ $key }}][alt]"
                            value="{{ old('feature.' . $key . 'alt', $feature->bannerImage()?->getTranslation('alt', $locale)) }}" />
                        <x-cerberus::form.error value="" />
                    </x-cerberus::form.field-block>
                </div>
            </div>
        @endforeach
    </div>

    <x-cerberus::alerts.flash type="success" timeout=10 />
</div>
