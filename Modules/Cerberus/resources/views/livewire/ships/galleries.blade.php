<div wire:sortable='updateOrder' wire:sortable.options="{ animation: 100 }">

    <x-cerberus::form.info>
        Αλλαγγή σειράς με drag and drop
    </x-cerberus::form.info>

    @foreach ($galleries as $gallery)
        <div wire:sortable.item="{{ $gallery->id }}" wire:key="item-{{ $gallery->id }}"
            class="border-b border-wotDark py-2 my-1 flex items-center gap-2">
            <div wire:sortable.handle class="cursor-move">
                <x-cerberus::icons.reorder-icon />
            </div>
            <div class="w-full">
                <h4 class="font-semibold mb-2">{{ $gallery->getTranslation('title', 'el') }}</h4>
                <div class="flex gap-2 items-center">
                    @foreach ($gallery->galleryImages()->get() as $image)
                        <div class="w-1/4">
                            <img src="{{ asset('storage/galleries/' . $gallery->id . '/' . $image->filename) }}" alt="">
                        </div>
                    @endforeach
                </div>
                <div class="flex justify-end gap-2 items-center">
                    <x-cerberus::form.link href="{{ route('cerberus.ship.edit.gallery', [$ship, $gallery]) }}"
                        class="mt-2 border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white">
                        Edit
                    </x-cerberus::form.link>

                    <button wire:click.prevent="deleteGallery({{ $gallery->id }})"
                        wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                        <x-cerberus::icons.delete-icon class="text-red-500" />
                    </button>
                </div>
            </div>
        </div>
    @endforeach

</div>
