<div>

    <div class="flex gap-2 items-center">
        <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
        <x-cerberus::form.helper>Αναζήτηση σε ερώτηση</x-cerberus::form.helper>
    </div>

    <div class="mt-4">
        @foreach ($packages as $package)
            <div class="w-full py-1">
                <label for="package-{{ $package->id }}" class="py-4">
                    <input id="package-{{ $package->id }}" type="checkbox" name="packages[]" value="{{ $package->id }}" 
                        @checked($ship->packages->contains($package->id))
                        class="mr-1  inline-block" >
                    {{ $package->title }}
                    </input>
                </label>
            </div>
        @endforeach
    </div>
</div>