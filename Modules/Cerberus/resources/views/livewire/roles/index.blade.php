<div class="w-4/5 block mx-auto bg-white mt-4">

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex justify-between p-4 gap-6 items-center">

        <div class="flex gap-2 items-center">
            <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
            <x-cerberus::form.helper>Αναζήτηση σε όνομα</x-cerberus::form.helper>
        </div>

        <x-cerberus::form.link href="{{ route('cerberus.roles.create') }}" class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
            Προσθήκη
        </x-cerberus::form.link>
    </div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Name</th>
            <th class="p-2">Created At</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($roles as $role)
                <tr>
                    <td class="p-2 w-12">{{ $role->id }}</td>
                    <td class="p-2">
                        <a class="font-bold" href="{{ route('cerberus.roles.edit', $role) }}">
                            {{ $role->name }}
                        </a>
                    </td>
                    <td class="p-2">{{ $role->created_at }}</td>
                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.roles.edit', $role) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            <button wire:click.prevent="deleteRole({{ $role }})"
                                wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                <x-cerberus::icons.delete-icon class="text-red-500" />
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $roles->links() }}
</div>
