<div class="w-4/5 block mx-auto bg-white mt-4">

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex justify-between p-4 gap-6 items-center">

        <div class="flex gap-2 items-center">
            <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
            <x-cerberus::form.helper>Αναζήτηση σε όνομα, email</x-cerberus::form.helper>
        </div>

        <x-cerberus::form.link href="{{ route('cerberus.admins.create') }}" class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
            Προσθήκη
        </x-cerberus::form.link>
    </div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Ενεργός</th>
            <th class="p-2">Όνομα</th>
            <th class="p-2">Email</th>
            <th class="p-2">Τύπος</th>
            <th class="p-2">Ρόλοι</th>
            <th class="p-2">Δημιουργήθηκε</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($users as $user)
                <tr>
                    <td class="p-2 w-12">{{ $user->id }}</td>
                    <td class="p-2 w-12">
                        <div class="">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $user->enabled ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $user->enabled ? 'Ενεργός' : 'Ανενεργός' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2">
                        <a class="font-bold" href="{{ route('cerberus.admins.edit', $user) }}">
                            {{ $user->name }}
                        </a>
                    </td>
                    <td class="p-2">

                        {{ $user->email }}

                    </td>
                    <td class="p-2">{{ $user->type }}</td>
                    <td class="p-2">
                        @forelse($user->getRoleNames() as $role)
                            <span class="">
                                {{ $role }}{{ !$loop->last ? ',' : '' }}
                            </span>
                        @empty
                            -
                        @endforelse
                    </td>
                    <td class="p-2">{{ \Carbon\Carbon::parse($user->created_at)->format('d-m-Y H:i') }}</td>
                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.admins.edit', $user) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            @if (Auth::user()->id != $user->id)
                                <button wire:click.prevent="deleteAdmin({{ $user }})"
                                    wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                    <x-cerberus::icons.delete-icon class="text-red-500" />
                                </button>
                            @endif
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $users->links() }}
</div>
