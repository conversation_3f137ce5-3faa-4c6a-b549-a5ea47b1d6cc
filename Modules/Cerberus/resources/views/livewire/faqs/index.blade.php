<div class="w-full px-4 block mx-auto bg-white mt-4">

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex justify-between p-4 gap-6 items-center">

        <div class="flex gap-2 items-center">
            <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
            <x-cerberus::form.helper>Αναζήτηση σε ερώτηση και απάντηση</x-cerberus::form.helper>
        </div>

        <div>
            <div class="cursor-pointer" wire:click="$toggle('featured')">
                @if ($featured)
                    <div class="bg-pacific-500 hover:bg-white hover:border hover:border-slate-800 hover:text-slate-800 text-white px-2 py-1 rounded">Show All</div>
                @else
                    <div class="border border-slate-400 hover:bg-pacific-500 hover:text-white px-2 py-1 rounded">Featured Only</div>
                @endif

            </div>
        </div>
        
        <x-cerberus::form.link href="{{ route('cerberus.faqs.create') }}"
            class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
            Προσθήκη
        </x-cerberus::form.link>
    </div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Pubished</th>
            <th class="p-2">Featured</th>
            <th class="p-2">Languages</th>
            <th class="p-2">Question</th>
            <th class="p-2">Answer</th>
            <th class="p-2">Order</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2" wire:sortable="updateOrder" wire:sortable.options="{ animation: 100 }">
            @foreach ($faqs as $faq)
                <tr wire:sortable.item="{{ $faq->id }}" wire:key="item-{{ $faq->id }}">
                    <td class="p-2 w-12">{{ $faq->id }}</td>
                    <td class="p-2 w-12">
                        <div class="cursor-pointer" wire:click="togglePublished({{ $faq }})" wire:confirm="Θέλετε σίγουρα να προχωρήσετε;">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $faq->published ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $faq->published ? 'Δημοσιευμένο' : 'Μη δημοσιευμένο' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2 w-12">
                        <div class="cursor-pointer" wire:click="toggleFeatured({{ $faq }})" wire:confirm="Θέλετε σίγουρα να προχωρήσετε;">
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $faq->featured ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $faq->featured ? 'Προβεβλημένο' : 'Μη προβεβλημένο' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2 w-6">
                        @foreach ($faq->locales() as $locale)
                            <div class="pb-2">
                                <x-cerberus::icons.flags :lang="$locale" class="block mx-auto" />
                            </div>
                        @endforeach
                    </td>
                    <td class="p-2">
                        @foreach ($faq->locales() as $locale)
                            <div class="pb-2">
                                <a class="font-semibold hover:underline" href="{{ route('cerberus.faqs.edit', $faq) }}">
                                    {{ $faq->getTranslation('question', $locale) }}

                                </a>
                            </div>
                        @endforeach
                    </td>
                    <td class="p-2">
                        @foreach ($faq->locales() as $locale)
                            <div class="pb-2">
                                {!! $faq->getTranslation('answer', $locale) !!}

                            </div>
                        @endforeach
                    </td>
                    <td class="p-2">
                        {{ $faq->order }}
                    </td>
     
                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.faqs.edit', $faq) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            <button wire:click.prevent="deleteFaq({{ $faq->id }})"
                                wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                <x-cerberus::icons.delete-icon class="text-red-500" />
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $faqs->links() }}
</div>
