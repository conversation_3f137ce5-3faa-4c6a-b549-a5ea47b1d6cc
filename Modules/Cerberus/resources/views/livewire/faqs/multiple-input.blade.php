<div>

    Selected Packages
    <div class="relative flex flex-col gap-2 mb-8">
        @if ($selectedPackages)
            @foreach ($selectedPackages as $package)
                <div>
                    <label for="">
                        {{ $package->title }}
                    <input type="hidden"  name="packages[]" value="{{ $package->id }}">
                </label>
                </div>
            @endforeach
        @endif

    </div>

    <div class="relative">

        <x-cerberus::form.field-block class="w-64">
            <x-cerberus::form.label for="searchTerm">
                Select Package
            </x-cerberus::form.label>
            <x-cerberus::form.input id="searchTerm" wire:model.live="searchTerm" />
        </x-cerberus::form.field-block>

        @if ($searchTerm)
            <div class="flex gap-4 absolute top-24 left-0 right-2 bg-white">
                <div class="max-w-1/3 w-1/3 flex flex-col p-4">

                    @foreach ($packages as $package)
                        @if (!collect($selectedPackages)->pluck('id')->contains($package->id))
                            <div class="bg-slate-200 mb-2 px-2 py-1 cursor-pointer"
                                wire:click="selectedPackagesUpdate({{ $package->id }})">
                                {{ $package->title }}
                            </div>
                        @endif
                    @endforeach
                </div>

            </div>
        @endif
    </div>


</div>
