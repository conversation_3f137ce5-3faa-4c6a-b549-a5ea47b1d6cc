<div>

    <div class="flex gap-2 items-center">
        <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
        <x-cerberus::form.helper>Αναζήτηση σε ερώτηση</x-cerberus::form.helper>
    </div>

    <div class="mt-4">
        @foreach ($faqs as $faq)
            <div class="w-full py-1">
                <label for="faq-{{ $faq->id }}" class="py-4">
                    <input id="faq-{{ $faq->id }}" type="checkbox" name="faqs[]" value="{{ $faq->id }}"
                        class="mr-1  inline-block" @checked($model->faqs->contains($faq->id))>
                    {{ $faq->question }}
                    </input>
                </label>
            </div>
        @endforeach
    </div>
</div>