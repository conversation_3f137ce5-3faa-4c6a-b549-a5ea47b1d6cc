<div class="m-2 p-2">

    <div wire:sortable="updateOrder" class="flex justify-center items-center gap-4">
        @foreach ($banners as $banner)
            <div wire:sortable.item="{{ $banner->id }}"  class="w-[350px] h-[350px] ">
               <img src="{{ asset('storage/pagebanners/' . $banner->filename) }}" alt="" class="rounded-full w-full h-full object-cover">
               <div class="text-center mt-4">
                {{ $banner->pivot->order }} 
               </div>
            </div>
        @endforeach
    </div>

    <x-cerberus::form.link link="{{ route('cerberus.'.Str::plural($tag->type).'.edit', $tag) }}"
        class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white mt-16">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
        </svg>
        {{ __('Επιστροφή') }}
    </x-cerberus::form.link>

</div>
