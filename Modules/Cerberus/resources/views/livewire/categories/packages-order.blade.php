<div class="bg-white m-2 py-4 px-2">

    <x-cerberus::alerts.flash type="success"  />

    <div class="flex justify-center">        

        <div>TRAVEL</div>
        <div wire:click="toggleType" class="w-6 bg-pacific-500 ml-2 rounded-tl-lg rounded-bl-lg cursor-pointer ">
            @if ($packageType == 'travel')
            <div class="text-center">
                <x-cerberus::icons.dot-icon />
            </div>
            @endif
            
        </div>
        <div wire:click="toggleType" class="w-6 bg-pacific-500 mr-2 rounded-tr-lg rounded-br-lg cursor-pointer ">
            @if ($packageType == 'food')
            <div class="text-center">
                <x-cerberus::icons.dot-icon />
            </div>
            @endif
        </div>
        <div>FOOD</div>
    </div>

    <x-cerberus::form.info>Αλλαγή σειράς με drag n drop</x-cerberus::form.info>
    <table class="w-full table-auto divide-solid divide-white divide-y-4 mb-8">
        <thead class="bg-slate-600 text-white text-left">
            <th class="p-2">Package Title</th>
            <th class="p-2 text-center">Package Duration</th>
            <th class="p-2 text-center"> Order</th>
        </thead>
        <tbody wire:sortable="updateOrder">
            @foreach ($packages as $package)
                <tr wire:sortable.item="{{ $package->id }}" wire:key="package-{{ $package->id }}">
                    <td class="flex gap-2 p-2">
                        <x-cerberus::icons.move-icon /> {{ $package->title }}
                    </td>
                    <td class="text-center">
                        {{ $package->duration }} <span class="ml-2">Ημέρες</span>
                    </td>
                    <td class="text-center">
                        {{ $tag->packages->where('id', $package->id)->first()->pivot->order }}
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <x-cerberus::form.link link="{{ route('cerberus.'.Str::plural($tag->type).'.edit', $tag) }}"
        class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
        </svg>
        {{ __('Επιστροφή') }}
    </x-cerberus::form.link>

</div>
