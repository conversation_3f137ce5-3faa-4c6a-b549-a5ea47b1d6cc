<div class="w-4/5 block mx-auto bg-white mt-4">

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex p-4 gap-2 items-center">
        <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
        <x-cerberus::form.helper>Αναζήτηση σε όνομα, email</x-cerberus::form.helper>
    </div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">enabled</th>
            <th class="p-2">name</th>
            <th class="p-2">email</th>
            <th class="p-2">email verified at</th>
            <th class="p-2">type</th>
            <th class="p-2">created at</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($users as $user)
                <tr>
                    <td class="p-2 w-12">{{ $user->id }}</td>
                    <td class="p-2 w-12">
                        <div
                            class="cursor-pointer"
                            wire:click="updateStatus({{ $user }})"
                            wire:confirm="{{ $user->enabled ? 'Θέλετε σίγουρα να απενεργοποιήσετε το χρήστη;' : 'Θέλετε σίγουρα να ενεργοποιήσετε το χρήστη;' }}"
                        >
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $user->enabled ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $user->enabled ? 'Ενεργός' : 'Ανενεργός' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2">{{ $user->name }}</td>
                    <td class="p-2">
                        <a class="font-bold" href="{{ route('cerberus.users.edit', $user) }}">
                            {{ $user->email }}
                        </a>
                    </td>
                    <td class="p-2 ">{{ $user->email_verified_at }}</td>
                    <td class="p-2 w-12">{{ $user->type }}</td>
                    <td class="p-2">{{ $user->created_at }}</td>
                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.users.edit', $user) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            @if (Auth::user()->id != $user->id)
                                <button wire:click.prevent="deleteEnduser({{ $user }})"
                                    wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                    <x-cerberus::icons.delete-icon class="text-red-500" />
                                </button>
                            @endif
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $users->links() }}
</div>
