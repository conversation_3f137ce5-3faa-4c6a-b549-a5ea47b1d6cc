<div class="w-full px-4 block mx-auto bg-white my-4">

    <x-cerberus::alerts.flash type="success" timeout=10 />


    <div class="flex justify-end p-4 gap-6 items-center">
        {{-- <div class="flex gap-2 items-center">
                <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
                <x-cerberus::form.helper>Αναζήτηση σε τίτλο</x-cerberus::form.helper>
            </div> --}}

        <x-cerberus::form.link
            class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800"
            href="{{ route('cerberus.menu-items.create', $menu) }}">
            Προσθήκη
        </x-cerberus::form.link>
    </div>
    @if ($menuItems->count())
        <x-cerberus::form.info class="m-4 block">
            *Αλλαγή σειράς με drag n drop
        </x-cerberus::form.info>
    @endif

    <div class="my-4 py-4 bg-white ">
        <ul wire:sortable="updateOrder" wire:sortable.options="{ animation: 100 }" class="w-1/2 mb-4">
            @foreach ($menuItems->where('parent_id', 0) as $item)
                <li wire:sortable.item="{{ $item->id }}" wire:key="item-{{ $item->id }}"
                    class="my-2 p-2 bg-gray-100">
                    <a href="{{ route('cerberus.menu-items.edit', [$menu, $item]) }}" class="hover:underline flex items-center gap-2">
                        <x-cerberus::icons.drag-icon class="fill-slate-400" />
                        {{ $item->title }}
                    </a>
                    @if ($menuItems->where('parent_id', $item->id)->count())
                        <ul wire:sortable="updateOrder" class="my-1 bg-white divide-y-4">
                            @foreach ($menuItems->where('parent_id', $item->id) as $subItem)
                                <li wire:sortable.item="{{ $subItem->id }}" wire:key="subitem-{{ $subItem->id }}"
                                    class="my-2 p-2">
                                    <a href="{{ route('cerberus.menu-items.edit', [$menu, $subItem]) }}"
                                        class="hover:underline flex items-center gap-2">
                                        <x-cerberus::icons.drag-icon class="fill-slate-400" />
                                        {{ $subItem->title }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    @endif
                </li>
            @endforeach
        </ul>




    </div>



</div>
