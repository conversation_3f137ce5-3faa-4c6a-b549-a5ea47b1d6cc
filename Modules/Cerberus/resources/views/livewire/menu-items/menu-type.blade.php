<div>
    <x-cerberus::form.field-block>
        <x-cerberus::form.label for="type">
            Select Page Type
        </x-cerberus::form.label>
        <x-cerberus::form.info>
            Επιλέξτε τον τύπο της σελίδας που θα δείχνει το συγκεκριμένο menu item.
        </x-cerberus::form.info>

        <select wire:model.live="type" name="type">
            <option value="" disabled>Επιλέξτε</option>
            <option value="posts_index">Blog Posts Index</option>
            <option value="post">Blog Post</option>
            <option value="tag">Tag Index</option>
            {{-- <option value="cruise_index">Cruises Index</option> --}}
            <option value="travel_category_index">Category Index</option>
            <option value="travel_package">Travel Package</option>
            <option value="food_index">Food Tours Index</option>
            <option value="food_show">Food Tour</option>
            <option value="static_page">Static Page</option>
            <option value="custom">Custom</option>
        </select>


        <x-cerberus::form.error value="type" />
    </x-cerberus::form.field-block>


    @if ($postsShow)
        <x-cerberus::form.field-block>
            <select wire:model.live="post" id="post" class="w-full">
                <!-- Give the disabled option a value that can match the initial null state -->
                <option value="null" disabled selected>Επιλέξτε Άρθρο</option>
                @foreach ($posts as $post)
                    <option value="{{ $post->id }}">
                        {{ $post->getTranslation('title', $lang) }}
                    </option>
                @endforeach
            </select>
        </x-cerberus::form.field-block>
    @endif


    @if ($tagsShow)
        <x-cerberus::form.field-block>
            <select wire:model.live="tag" id="tag">
                <option value="null" disabled selected>Επιλέξτε Tag</option>
                @foreach ($tags as $tag)
                    <option value="{{ $tag->id }}">
                        {{ $tag->getTranslation('title', $lang) }}
                    </option>
                @endforeach
            </select>
        </x-cerberus::form.field-block>
    @endif

    @if ($travelPackageShow)
        <x-cerberus::form.field-block>
            <select wire:model.live="travelPackage" id="travelPackage">
                <option value="null" disabled selected>Επιλέξτε Πακέτο</option>
                @foreach ($travelPackages as $travelPackage)
                    <option value="{{ $travelPackage->id }}">
                        {{ $travelPackage->getTranslation('title', $lang) }}
                    </option>
                @endforeach
            </select>
        </x-cerberus::form.field-block>
    @endif

    @if ($travelCategoryShow)
        <x-cerberus::form.field-block>
            <select wire:model.live="travelCategory" id="travelCategory">
                <option value="null" disabled selected>Επιλέξτε Κατηγορία</option>
                @foreach ($travelCategories as $travelCategory)
                    <option value="{{ $travelCategory->id }}">
                        {{ $travelCategory->getTranslation('title', $lang) }}
                    </option>
                @endforeach
            </select>
        </x-cerberus::form.field-block>
    @endif

    @if ($foodToursShow)
    <x-cerberus::form.field-block>
        <select wire:model.live="foodTour" id="foodTour" class="w-full">
            <!-- Give the disabled option a value that can match the initial null state -->
            <option value="null" disabled selected>Επιλέξτε Food Tour</option>
            @foreach ($foodTours as $foodTour)
                <option value="{{ $foodTour->id }}">
                    {{ $foodTour->getTranslation('title', $lang) }}
                </option>
            @endforeach
        </select>
    </x-cerberus::form.field-block>
@endif


    @if ($staticPageShow)
        <x-cerberus::form.field-block>
            <select wire:model.live="staticPage" id="staticPage">
                <option value="null" disabled selected >Επιλέξτε Σελίδα</option>
                @foreach ($staticPages as $page)
                    <option value="{{ $page->id }}">
                        {{ $page->getTranslation('title', $lang) }}
                    </option>
                @endforeach
            </select>
        </x-cerberus::form.field-block>
    @endif

    <x-cerberus::form.field-block>
        <x-cerberus::form.label for="link">
            Link
        </x-cerberus::form.label>
        <x-cerberus::form.info>
            Συμπληρώνεται αυτόματα, εκτός αν έχετε επιλέξει custom στον τύπο σελίδας.
        </x-cerberus::form.info>
        <x-cerberus::form.input id="link" name="link" wire:model="link"
            value="{{ old('link', $menuItem->link) }}" class="bg-gray-200" />
        <x-cerberus::form.error value="link" />
    </x-cerberus::form.field-block>

</div>
