<div class="w-full px-4 block mx-auto bg-white mt-4">

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div class="flex justify-between p-4 gap-6 items-center">

        <div class="flex gap-2 items-center">
            <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
            <x-cerberus::form.helper>Αναζήτηση σε όνομα, επώνυμο, email</x-cerberus::form.helper>
        </div>

    </div>

    <div class="mb-2 pb-2">
        <table class="w-full table-auto border divide-solid divide-black divide-y-4 mb-8">
            <thead class="bg-gray-300 text-left">
                <th scope="col" class="p-2" wire:click="sortBy('id')">
                    <div class="cursor-pointer flex gap-2 items-center">
                        ID
                        @if ($sortField == 'id' && $sortDirection == 'asc')
                            {{-- <x-cerberus::icons.up-icon /> --}}
                        @elseif ($sortField == 'id' && $sortDirection == 'desc')
                            <x-cerberus::icons.down-icon />
                        @else
                            <div class="flex flex-col">
                                <x-cerberus::icons.up-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                                <x-cerberus::icons.down-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                            </div>
                        @endif
                    </div>
                </th>
                <th scope="col" class="p-2" wire:click="sortBy('created_at')">
                    <div class="cursor-pointer flex gap-2 items-center">
                        Received
                        @if ($sortField == 'created_at' && $sortDirection == 'asc')
                            <x-cerberus::icons.up-icon />
                        @elseif ($sortField == 'created_at' && $sortDirection == 'desc')
                            <x-cerberus::icons.down-icon />
                        @else
                            <div class="flex flex-col">
                                <x-cerberus::icons.up-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                                <x-cerberus::icons.down-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                            </div>
                        @endif
                    </div>
                </th>
                <th scope="col" class="p-2 cursor-pointer flex gap-2 items-center" wire:click="sortBy('last_name')">
                    <div class="cursor-pointer flex gap-2 items-center">
                        Name
                        @if ($sortField == 'name' && $sortDirection == 'asc')
                            <x-cerberus::icons.up-icon />
                        @elseif ($sortField == 'name' && $sortDirection == 'desc')
                            <x-cerberus::icons.down-icon />
                        @else
                            <div class="flex flex-col">
                                <x-cerberus::icons.up-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                                <x-cerberus::icons.down-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                            </div>
                        @endif
                    </div>
                </th>
                <th scope="col" class="p-2 " wire:click="sortBy('email')">
                    <div class="cursor-pointer flex gap-2 items-center">
                        Email
                        @if ($sortField == 'email' && $sortDirection == 'asc')
                            <x-cerberus::icons.up-icon />
                        @elseif ($sortField == 'email' && $sortDirection == 'desc')
                            <x-cerberus::icons.down-icon />
                        @else
                            <div class="flex flex-col">
                                <x-cerberus::icons.up-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                                <x-cerberus::icons.down-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                            </div>
                        @endif
                    </div>
                </th>
                <th scope="col" class="p-2">
                    Mobile
                </th>
                @if ($type !== 'contact')
                    <th scope="col" class="p-2 " wire:click="sortBy('package_id')">
                        <div class="cursor-pointer flex gap-2 items-center">
                            ID - Package
                            @if ($sortField == 'package_id' && $sortDirection == 'asc')
                                <x-cerberus::icons.up-icon />
                            @elseif ($sortField == 'package_id' && $sortDirection == 'desc')
                                <x-cerberus::icons.down-icon />
                            @else
                                <div class="flex flex-col">
                                    <x-cerberus::icons.up-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                                    <x-cerberus::icons.down-icon class="w-4 h-4 -my-[3px] font-bold text-wotDark" />
                                </div>
                            @endif
                        </div>

                    </th>
                @endif
                <th scope="col" class="p-2">

                </th>
            </thead>
            <tbody class="divide-y-2 divide-x-2">
                @foreach ($leads as $lead)
                    <tr>
                        <td class="p-2 w-12">{{ $lead->id ?? null }}</td>

                        <td class="p-2 w-18">
                            {{ $lead->created_at }}
                        </td>
                        <td>
                            {{ $lead->fullName() }}
                        </td>
                        <td>
                            {{ $lead->email }}
                        </td>
                        <td>
                            {{ $lead->mobile }}
                        </td>
                        @if ($type !== 'contact' && $type !== 'create_package')
                            <td>
                                {{ $lead->package?->id }} -
                                {{ $lead->package?->title }}
                            </td>
                        @endif
                        <td>
                            <a class="hover:underline bg-wotDark hover:bg-pacific-800 text-white px-3 py-1 rounded"
                                href="{{ route('cerberus.leads.show', $lead) }}">
                                Details
                            </a>
                        </td>

                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>


</div>
