<div>

    <x-cerberus::form.field-block>
        <x-cerberus::form.label class="mb-2">
            ΔΗΜΟΣΙΕΥΜΕΝΟ
        </x-cerberus::form.field-block>
        <label
            class="px-8 py-2 rounded-tl rounded-bl text-slate-800 border border-slate-400 {{ !$model->published ? 'bg-slate-400 text-white' : ' text-slate-600 hover:bg-slate-400 hover:text-white cursor-pointer' }} "
            @if ($model->published) wire:click="unpublish" @endif
            wire:confirm="Το μοντέλο αυτό θα πάψει να είναι δημοσιευμένο. Είστε σίγουροι;">

            ΟΧΙ
        </label>
        <label
            class="px-8 py-2 rounded-tr rounded-br border border-pacific-500 
            {{ $model->published ? 'bg-pacific-500 text-white' : 'bg-white text-pacific-500 hover:bg-pacific-500 hover:text-white cursor-pointer' }} "
            @if (!$model->published) wire:click="publish" @endif
            wire:confirm="Το μοντέλο αυτό θα δημοσιευτεί. Είστε σίγουροι;">

            ΝΑΙ
        </label>

        <x-cerberus::form.info>
            *Live element. Δεν απαιτείται αποθήκευση
        </x-cerberus::form.info>
    </x-cerberus::form.field-block>

</div>
