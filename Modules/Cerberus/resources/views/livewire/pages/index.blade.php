<div class="w-full px-4 block mx-auto bg-white mt-4">

    <div class="flex justify-between p-4 gap-6 items-center">

        <x-cerberus::alerts.flash type="success" timeout=10 />
        <x-cerberus::alerts.flash type="error" timeout=10 />

        <div class="flex gap-2 items-center">
            <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
            <x-cerberus::form.helper>Αναζήτηση σε τίτλο και περιεχόμενο</x-cerberus::form.helper>
        </div>

        <x-cerberus::form.link href="{{ route('cerberus.pages.create') }}" class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
            Προσθήκη
        </x-cerberus::form.link>
    </div>

    <table class="w-full table-auto divide-solid divide-white divide-y-4 mb-8">
        <thead class="bg-slate-600 text-white text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Published</th>
            <th class="p-2">Languages</th>
            <th class="p-2">Title</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2 divide-slate-300">
            @foreach ($pages as $page)
                <tr>
                    <td class="p-2 w-12">{{ $page->id }}</td>
                    <td class="p-2 w-12">
                        <div>
                            <span
                                class="block mx-auto w-3 h-3 rounded-full {{ $page->published ? 'bg-green-500' : 'bg-orange-500' }}"
                                title="{{ $page->published ? 'Δημοσιευμένο' : 'Μη δημοσιευμένο' }}">
                            </span>
                        </div>
                    </td>
                    <td class="p-2 w-12">
                        @foreach ($page->locales() as $locale)
                            <x-cerberus::icons.flags :lang="$locale" class="block mx-auto" />
                        @endforeach
                    </td>

                    <td class="p-2">
                        @foreach ($page->locales() as $locale)
                            <div class="pb-2">
                                <a class="font-bold" href="{{ $locale == 'el' ? route('cerberus.pages.edit', $page) : route('cerberus.pages.en.edit', $page) }}">
                                    {{ $page->getTranslation('title', $locale) }}
                                </a>
                            </div>
                        @endforeach
                    </td>
        
                    <td class="w-12">
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.pages.edit', $page) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                            <button wire:click.prevent="deletePage({{ $page->id }})"
                                wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;">
                                <x-cerberus::icons.delete-icon class="text-red-500" />
                            </button>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

</div>
