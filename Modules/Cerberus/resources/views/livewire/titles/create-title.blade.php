<div>
    <x-cerberus::form.field-block>
        <x-cerberus::form.label for="title[el]">
            Greek Title
        </x-cerberus::form.label>
        <x-cerberus::form.input id="title[el]" name="title[el]" value="{{ old('title[el]') }}" wire:model.live="title" />
        <x-cerberus::form.error value="title[el]" />
        {{-- <x-cerberus::form.info>
            Το slug δημιουργείται αυτόματα με βάση τον τίτλο, μόλις κάνετε κλικ έξω από το πεδίο.
        </x-cerberus::form.info> --}}
    </x-cerberus::form.field-block>


    <x-cerberus::form.field-block>
        <x-cerberus::form.label for="slug[el]">
            Greek Slug
        </x-cerberus::form.label>
        <x-cerberus::form.input id="slug[el]" name="slug[el]" value="{{ Str::slug($title) }}" />
        <x-cerberus::form.error value="slug.el" />

    </x-cerberus::form.field-block>

</div>
