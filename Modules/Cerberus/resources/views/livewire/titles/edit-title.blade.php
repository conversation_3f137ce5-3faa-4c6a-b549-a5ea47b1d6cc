<div>
    <x-cerberus::form.field-block x-data>
        <x-cerberus::form.label for="title[{{ $locale }}]" class="flex justify-between">
            TITLE {{ __('cerberus::base.' . $locale) }}

            @if ($locale == 'en')
                <div>
                    <livewire:cerberus::elements.suggest-translation :content="$model->getTranslation('title', 'el')" :id="'title'" />

                    <span
                        x-on:translate-title.window="$wire.title = $event.detail.translation"></span>
                
                </div>
            @endif

        </x-cerberus::form.label>
        <x-cerberus::form.input id="title[{{ $locale }}]" name="title[{{ $locale }}]"
            wire:model.blur="title" />
        <x-cerberus::form.error value="{{ 'title.' . $locale }}" />

        @if ($showGreek)
            <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {{ $model->getTranslation('title', 'el') }}</div>
        @endif
    </x-cerberus::form.field-block>

    <x-cerberus::form.field-block>
        <x-cerberus::form.label for="slug[{{ $locale }}]">
            SLUG {{ __('cerberus::base.' . $locale) }}
        </x-cerberus::form.label>
        <x-cerberus::form.input id="slug[{{ $locale }}]" name="slug[{{ $locale }}]" wire:model.blur="slug"
            class="!bg-slate-300" />
        <x-cerberus::form.error value="{{ 'slug.' . $locale }}" />
        @if ($showGreek)
            <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {{ $model->getTranslation('slug', 'el') }}</div>
        @endif
        <x-cerberus::form.info>
            *Το slug δεν αλλάζει αυτόματα όταν αλλάζει ο τίτλος. Για να το αλλάξετε, επεξεργαστείτε το πεδίο ή διαγράψτε
            το και μετά αλλάξτε τον τίτλο.
        </x-cerberus::form.info>
    </x-cerberus::form.field-block>

</div>
