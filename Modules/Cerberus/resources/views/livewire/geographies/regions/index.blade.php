<div class="w-full px-4 block mx-auto bg-white mt-4">

    <div class="mb-4 p-4 flex flex-wrap gap-3 lg:justify-between">
        <div>
            <div class="flex gap-2 items-center">
                <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
                <x-cerberus::form.helper>Αναζήτηση σε regions</x-cerberus::form.helper>
            </div>
        </div>
        <div>
            <select wire:model.live='continent_id' id="" class="w-64">
                <option value="">Select Continent</option>
                @foreach ($continents as $continent)
                    <option value="{{ $continent->id }}">
                        {{ $continent->name }}
                    </option>
                @endforeach
            </select>
        </div>
        <div>
            <select wire:model.live='subregion_id' id="" class="w-64">
                <option value="">Select Subregion</option>
                @foreach ($subregions as $subregion)
                    <option value="{{ $subregion->id }}">
                        {{ $subregion->name }}
                    </option>
                @endforeach
            </select>
        </div>
        <div>
            <select wire:model.live='country_id' id="" class="w-64">
                <option value="">Select Country</option>
                @foreach ($countries as $country)
                    <option value="{{ $country->id }}">
                        {{ $country->name }}
                    </option>
                @endforeach
            </select>
        </div>

        <div>
            <x-cerberus::form.link href="{{ route('cerberus.regions.create') }}"
                class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
                Προσθήκη
            </x-cerberus::form.link>
        </div>
    </div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th class="p-2">ID</th>
            <th class="p-2">Region Name</th>
            <th class="p-2">Continent</th>
            <th class="p-2">SubRegion</th>
            <th class="p-2">Country</th>
            <th class="p-2">Cities</th>
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($regions as $region)
                <tr>
                    <td class="p-2 w-12">{{ $region->id }}</td>

                    <td class="p-2">
                        <div class="pb-2">
                            <a class="font-semibold hover:underline"
                                href="{{ route('cerberus.regions.edit', $region) }}">
                                {{ $region->name }}

                            </a>
                        </div>
                    </td>

                    <td class="p-2">
                        {{ $region->continent->name }}
                    </td>
                    <td class="p-2">
                        {{ $region->subregion->name }}
                    </td>
                    <td class="p-2">
                        {{ $region->country->name }}
                    </td>
                    <td class="p-2">
                        {{ $region->cities->count() }}
                    </td>

                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.regions.edit', $region) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $regions->links() }}
</div>
