<div>

    <form wire:submit.prevent="addAlias" class="w-4/5 mx-auto">

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="aliasable_type">
                Alias Type
            </x-cerberus::form.label>

            <select wire:model.live="aliasable_type" id="aliasable_type" class="w-48">
                <option value="">Select Type</option>
                @foreach ($aliasable_types as $key => $type)
                    <option value="{{ $key }}">
                        {{ $type }}
                    </option>
                @endforeach
            </select>
            <x-cerberus::form.error value="aliasable_type" />
        </x-cerberus::form.field-block>

        @if ($aliasable_type == 'App\Models\Continent')

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="aliasble_id">
                    Select Continent
                </x-cerberus::form.label>
                <select wire:model.live="aliasable_id" id="aliasable_id" class="w-48">
                    <option value="">Select Continent</option>
                    @foreach ($continents as $continent)
                        <option value="{{ $continent->id }}">
                            {{ $continent->name }}
                        </option>
                    @endforeach
                </select>
                <x-cerberus::form.error value="aliasable_id" />
            </x-cerberus::form.field-block>
        @elseif ($aliasable_type == 'App\Models\Subregion')
            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="aliasble_id">
                    Select Subregion
                </x-cerberus::form.label>
                <select wire:model.live="aliasable_id" id="aliasable_id" class="w-48">
                    <option value="">Select Subregion</option>
                    @foreach ($subregions as $subregion)
                        <option value="{{ $subregion->id }}">
                            {{ $subregion->name }}
                        </option>
                    @endforeach
                </select>
                <x-cerberus::form.error value="aliasable_id" />
            </x-cerberus::form.field-block>
        @elseif ($aliasable_type == 'App\Models\Country')
            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="aliasble_id">
                    Select Country
                </x-cerberus::form.label>
                <select wire:model.live="aliasable_id" id="aliasable_id" class="w-48">
                    <option value="">Select Country</option>
                    @foreach ($countries as $country)
                        <option value="{{ $country->id }}">
                            {{ $country->name }}
                        </option>
                    @endforeach
                </select>
                <x-cerberus::form.error value="aliasable_id" />
            </x-cerberus::form.field-block>
        @elseif ($aliasable_type == 'App\Models\Region')
            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="aliasble_id">
                    Select Region
                </x-cerberus::form.label>
                <select wire:model.live="aliasable_id" id="aliasable_id" class="w-48">
                    <option value="">Select Region</option>
                    @foreach ($regions as $region)
                        <option value="{{ $region->id }}">
                            {{ $region->name }}
                        </option>
                    @endforeach
                </select>
                <x-cerberus::form.error value="aliasable_id" />
            </x-cerberus::form.field-block>
        @elseif ($aliasable_type == 'App\Models\City')
            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="aliasble_id">
                    Select City
                </x-cerberus::form.label>
                <select wire:model.live="aliasable_id" id="aliasable_id" class="w-48">
                    <option value="">Select City</option>
                    @foreach ($cities as $city)
                        <option value="{{ $city->id }}">
                            {{ $city->name }}
                        </option>
                    @endforeach
                </select>
                <x-cerberus::form.error value="aliasable_id" />
            </x-cerberus::form.field-block>
        @endif

        @if ($aliasable_id)
            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="name">
                    Alias
                </x-cerberus::form.label>
                <x-cerberus::form.input id="name" wire:model.live="name" />
                <x-cerberus::form.error value="name" />
            </x-cerberus::form.field-block>
        @endif

        <div class="flex items-center justify-between px-4">
            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.aliases.index') }}" />

        </div>

    </form>

</div>
