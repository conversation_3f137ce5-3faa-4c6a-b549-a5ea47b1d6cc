<div class="w-full px-4 block mx-auto bg-white mt-4">
    <div class="mb-4 p-4 flex flex-wrap gap-3 justify-between">
        <div>
            <div class="flex gap-2 items-center">
                <x-cerberus::form.input wire:model.live="searchTerm" class="w-48" placeholder="Αναζήτηση Live" />
                <x-cerberus::form.helper>Αναζήτηση σε όνομα οντότητας</x-cerberus::form.helper>
            </div>
        </div>
        <div>
            <select wire:model.live='type' id="" class="w-64">
                <option value="">Select Type</option>
                @foreach ($aliasable_types as $key => $type)
                    <option value="{{ $key }}">
                        {{ $type }}
                    </option>
                @endforeach
            </select>
        </div>
        <div>
            <x-cerberus::form.link href="{{ route('cerberus.aliases.create') }}"
                class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
                Προσθήκη
            </x-cerberus::form.link>
        </div>
    </div>

    <table class="w-full table-auto divide-solid divide-black divide-y-4 mb-8">
        <thead class="bg-gray-300 text-left">
            <th scope="col" class="p-2">
                ID
            </th>
            <th class="p-2" wire:click="sortBy('name')">                
                ALIAS
            </th>
            <th class="p-2" wire:click="sortBy('iso_code')">                
                TYPE                
            </th>
            <th class="p-2" wire:click="sortBy('iso_code')">                
                NAME                
            </th>
           
            <th class="p-2"></th>
        </thead>
        <tbody class="divide-y-2">
            @foreach ($aliases as $alias)
                <tr>
                    <td class="p-2 w-12">{{ $alias->id }}</td>

                    <td class="p-2">
                        <div class="pb-2">
                            <a class="font-semibold hover:underline"
                                href="{{ route('cerberus.aliases.edit', $alias) }}">
                                {{ $alias->name }}

                            </a>
                        </div>
                    </td>

                    <td class="p-2">
                        {{ Str::of($alias->aliasable_type)->explode('\\')->last() }}
                    </td>
                    <td class="p-2">
                        {{ $alias->aliasable?->name }}
                    </td>
             
    

                    <td>
                        <div class="flex">
                            <x-cerberus::form.link href="{{ route('cerberus.aliases.edit', $alias) }}"
                                class="focus:ring-transparent">
                                <x-cerberus::icons.edit-icon />
                            </x-cerberus::form.link>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    {{ $aliases->links() }}

</div>
