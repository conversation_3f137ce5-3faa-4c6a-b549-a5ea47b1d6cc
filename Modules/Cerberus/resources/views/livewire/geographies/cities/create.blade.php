<div>
    <form wire:submit="createCity" class="w-4/5 mx-auto">
        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="country_id">
                Country
            </x-cerberus::form.label>
            <select wire:model.live="country_id" name="country_id" id="country_id">
                <option selected>Select Country</option>
                @foreach ($countries as $country)
                    <option value="{{ $country->id }}" @selected(old('country_id') == $country->id)>
                        {{ $country->name }}
                    </option>
                @endforeach
            </select>
            <x-cerberus::form.error value="country_id" />
        </x-cerberus::form.field-block>
        @if ($country_id)
            @if ($regions->count())
                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="region_id">
                        Region
                    </x-cerberus::form.label>
                    <select wire:model.live="region_id" id="region_id">
                        <option selected>Select Region</option>
                        @foreach ($regions as $region)
                            <option value="{{ $region->id }}" @selected(old('region_id') == $region->id)>
                                {{ $region->name }}
                            </option>
                        @endforeach
                    </select>
                    <x-cerberus::form.error value="region_id" />
                </x-cerberus::form.field-block>
            @else
                <div class="text-red-500 my-2">Δεν υπάρχουν regions στη χώρα. Θα πρέπει <a href="{{ route('cerberus.regions.create') }}" target="_blank" class="underline">να δημιουργήσετε</a> τουλάχιστο ένα. </div>
            @endif

        @endif
        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="name">
                Name
            </x-cerberus::form.label>
            <x-cerberus::form.input id="name" wire:model.live="name" value="{{ old('name') }}" />
            <x-cerberus::form.error value="name" />
        </x-cerberus::form.field-block>
        <div class="flex items-center justify-between px-4">
            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.cities.index') }}" />

        </div>
    </form>
    

    @if ($selected_region)
        <div class="p-2">
            <h2>REGION CITIES</h2>
            <div class="flex flex-wrap justify-center gap-6 mt-4">
                @foreach ($selected_region->cities as $city)
                    <div>
                        {{ $city->name }}
                    </div>
                @endforeach
            </div>
        </div>
    @endif

</div>
