<div>
    <div wire:sortable='updateOrder' wire:sortable.options="{ animation: 100 }" class="flex flex-wrap gap-4 p-4">
        @if ($images)
            @foreach ($images as $image)
                <div wire:sortable.item="{{ $image->id }}" wire:key="image-{{ $image->id }}"
                    class="w-[24%] border-2 p-2">
                    <img src="{{ asset('storage/galleries/' . $gallery->id . '/' . $image->filename) }}" alt="">

                    <div class="flex justify-between mt-2">
                        <span>{{ $image->order != 0 ? $image->order : $loop->iteration }}</span>
                        <button wire:click.prevent="deleteImage({{ $image->id }})"
                            wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;" title="Delete Image">
                            <x-cerberus::icons.delete-icon class="text-red-500" />
                        </button>
                    </div>
                </div>
            @endforeach
        @endif

    </div>

    <div class="flex justify-between items-center m-2 ">
        <span><x-cerberus::form.info>Drag 'n Drop to change order</x-cerberus::form.info></span>
        <button wire:click.prevent="deleteGallery({{ $images }})"
            wire:confirm="Θέλετε σίγουρα να προχωρήσετε σε διαγραφή;"
            class="group flex gap-2 items-center  hover:bg-red-800 hover:text-white px-2 py-1">
            Delete Gallery
            <x-cerberus::icons.delete-icon class="text-red-800 group-hover:text-white" />
        </button>
    </div>
</div>
