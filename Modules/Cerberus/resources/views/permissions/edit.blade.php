<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΕΠΕΞΕΡΓΑΣΙΑ PERMISSION: {{ $permission->name }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div>
        <form action="{{ route('cerberus.permissions.update', $permission) }}" method="POST" class="w-1/4">
            @csrf
            @method('PATCH')


            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="name">
                    NAME
                </x-cerberus::form.label>
                <x-cerberus::form.input id="name" name="name" value="{{ old('name', $permission->name) }}" />
                <x-cerberus::form.error value="{{ 'name' }}" />
            </x-cerberus::form.field-block>


            <x-cerberus::form.field-block>
                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.permissions.index') }}"
                    deleteRoute="{{ route('cerberus.permissions.destroy', $permission) }}" class="sticky top-0" />
            </x-cerberus::form.field-block>
        </form>
    </div>
</x-cerberus::cerberus>
