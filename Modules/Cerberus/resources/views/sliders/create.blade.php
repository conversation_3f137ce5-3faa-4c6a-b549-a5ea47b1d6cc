<x-cerberus::cerberus>
    <x-slot:header>
        <h1>Προσθήκη Slider</h1>
    </x-slot:header>


    <div class="m-2">
        <form action="{{ route('cerberus.sliders.store') }}" method="POST" enctype="multipart/form-data">
            @csrf

            <input type="hidden" name="type" value="slider">

            <x-cerberus::form.field-block class="w-1/3">
                <x-cerberus::form.label for="image">
                    Εικόνα
                    <x-cerberus::form.info>Μέγιστο μέγεθος 500kb - Πλάτος 1920px - Αναλογία 3/2</x-cerberus::form.info>
                </x-cerberus::form.label>
                <x-cerberus::form.input id="image" name="image" type="file" value="{{ old('image') }}" />
                <x-cerberus::form.error value="image" />
            </x-cerberus::form.field-block>

            <div class="flex gap-3">
                @foreach (LocaleConfig::getLocales() as $locale)
                    <x-cerberus::form.field-block class="w-[48%]">
                        <x-cerberus::form.label for="alt[{{ $locale }}]">
                            ALT {{ __('base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                            type="alt" value="{{ old('alt.' . $locale) }}" />
                        <x-cerberus::form.error value="alt.{{ $locale }}" />
                    </x-cerberus::form.field-block>
                @endforeach
            </div>

            <div class="flex gap-3">
                @foreach (LocaleConfig::getLocales() as $locale)
                    <x-cerberus::form.field-block class="w-[48%]">
                        <x-cerberus::form.label for="title[{{ $locale }}]">
                            TITLE {{ __('base.' . $locale) }}
                            <x-cerberus::form.info>Ο τίτλος του slider - προαιρετικό</x-cerberus::form.info>
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="title[{{ $locale }}]" name="title[{{ $locale }}]"
                            type="title" value="{{ old('title.' . $locale) }}" />
                        <x-cerberus::form.error value="title.{{ $locale }}" />
                    </x-cerberus::form.field-block>
                @endforeach
            </div>

            <div class="flex gap-3">
                @foreach (LocaleConfig::getLocales() as $locale)
                    <x-cerberus::form.field-block class="w-[48%]">
                        <x-cerberus::form.label for="content[{{ $locale }}]">
                            CONTENT {{ __('base.' . $locale) }}
                            <x-cerberus::form.info>Ο τίτλος του slider - προαιρετικό</x-cerberus::form.info>
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="content[{{ $locale }}]" name="content[{{ $locale }}]" class="editor"
                            type="content" value="{{ old('content.' . $locale) }}" >
                            {{ old('content.' . $locale) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="content.{{ $locale }}" />
                    </x-cerberus::form.field-block>
                @endforeach
            </div>

            <div class="flex gap-3">
                @foreach (LocaleConfig::getLocales() as $locale)
                    <x-cerberus::form.field-block class="w-[48%]">
                        <x-cerberus::form.label for="url[{{ $locale }}]">
                            URL {{ __('base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="url[{{ $locale }}]" name="url[{{ $locale }}]"
                            type="url" value="{{ old('url.' . $locale) }}" />
                        <x-cerberus::form.error value="url.{{ $locale }}" />
                    </x-cerberus::form.field-block>
                @endforeach
            </div>

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="description">
                    Περιγραφή
                    <x-cerberus::form.info>
                        Σημειώσεις για admins
                    </x-cerberus::form.info>
                </x-cerberus::form.label>
                <x-cerberus::form.textarea id="description" name="description">
                    {{ old('description') }}
                </x-cerberus::form.textarea>
                <x-cerberus::form.error value="description" />
            </x-cerberus::form.field-block>

            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.homepage.index') }}" />
        </form>
    </div>

</x-cerberus::cerberus>
