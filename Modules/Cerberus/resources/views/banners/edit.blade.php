<x-cerberus::cerberus>
    <x-slot:header>
        <h1>Επεγεργασία Banner</h1>
    </x-slot:header>

    <div class="m-2">
        <form action="{{ route('cerberus.banners.update', $banner) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PATCH')

            <input type="hidden" name="type" value="banner">

            <x-cerberus::form.field-block class="w-1/3">
                <x-cerberus::form.label for="image">
                    Εικόνα
                    <img src="{{ asset('storage/banners/'. $banner->bannerImage()->filename) }}" alt="">
                </x-cerberus::form.label>
                <x-cerberus::form.input id="image" name="image" type="file" value="{{ old('image') }}" />
                <x-cerberus::form.error value="image" />
            </x-cerberus::form.field-block>

            <div class="flex gap-3">
                @foreach (LocaleConfig::getLocales() as $locale)
                    <x-cerberus::form.field-block class="w-[48%]">
                        <x-cerberus::form.label for="alt[{{ $locale }}]">
                            ALT {{ __('base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]" type="alt"
                            value="{{ old('alt.' . $locale, $banner->bannerImage()->getTranslation('alt', $locale)) }}" />
                        <x-cerberus::form.error value="alt.{{ $locale }}" />
                    </x-cerberus::form.field-block>
                @endforeach
            </div>
 
            <div class="flex gap-3">
                @foreach (LocaleConfig::getLocales() as $locale)
                    <x-cerberus::form.field-block class="w-[48%]">
                        <x-cerberus::form.label for="url[{{ $locale }}]">
                            URL {{ __('base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="url[{{ $locale }}]" name="url[{{ $locale }}]" type="url"
                            value="{{ old('url.' . $locale, $banner->getTranslation('url', $locale)) }}" />
                        <x-cerberus::form.error value="url.{{ $locale }}" />
                    </x-cerberus::form.field-block>
                @endforeach
            </div>

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="description">
                    Περιγραφή
                    <x-cerberus::form.info>
                        Σημειώσεις για admins
                    </x-cerberus::form.info>
                </x-cerberus::form.label>
                <x-cerberus::form.textarea id="description" name="description">
                    {{ $banner->description }}
                </x-cerberus::form.textarea>
                <x-cerberus::form.error value="description" />
            </x-cerberus::form.field-block>

            <livewire:cerberus::elements.publish-button :model="$banner" />
            

            <div class="flex items-center justify-between px-4">
            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.homepage.index') }}" />
        </form>

        <x-cerberus::form.delete deleteRoute="{{ route('cerberus.banners.destroy', $banner) }}" />
    </div>
    </div>

</x-cerberus::cerberus>
