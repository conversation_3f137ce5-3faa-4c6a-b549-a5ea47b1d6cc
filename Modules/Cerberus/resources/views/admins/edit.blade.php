<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΕΠΕΞΕΡΓΑΣΙΑ ADMIN: {{ $user->name }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />

    <div>
        <form action="{{ route('cerberus.admins.update', $user) }}" method="POST" class="w-1/2">
            @csrf
            @method('PATCH')

            <input type="hidden" name="type" value="admin">

            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="name">
                    NAME
                </x-cerberus::form.label>
                <x-cerberus::form.input id="name" name="name" value="{{ old('name', $user->name) }}" />
                <x-cerberus::form.error value="{{ 'name' }}" />
            </x-cerberus::form.field-block>



            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="email">
                    EMAIL
                </x-cerberus::form.label>
                <x-cerberus::form.input id="email" name="email" value="{{ old('email', $user->email) }}" />
                <x-cerberus::form.error value="{{ 'email' }}" />
            </x-cerberus::form.field-block>



{{--            <x-cerberus::form.field-block>--}}
{{--                <x-cerberus::form.label for="type">--}}
{{--                    TYPE--}}
{{--                </x-cerberus::form.label>--}}
{{--                <select name="type" id="type">--}}
{{--                    <option @selected($user->type == 'admin') value="admin">admin</option>--}}
{{--                    <option @selected($user->type == 'dev') value="dev">dev</option>--}}
{{--                </select>--}}
{{--                <x-cerberus::form.error value="{{ 'type' }}" />--}}
{{--            </x-cerberus::form.field-block>--}}

            <x-cerberus::form.field-block>
                @foreach ($roles as $role)
                    <div class="flex gap-2 items-center">

                        <input id="roles[{{ $role->id }}]" name="roles[]" value="{{ $role->id }}"
                            type="checkbox" @checked($user->roles->contains($role->id)) />

                        <x-cerberus::form.label for="roles[{{ $role->id }}]">
                            {{ $role->name }}
                        </x-cerberus::form.label>
                    </div>
                @endforeach
            </x-cerberus::form.field-block>


            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="enabled">
                    ENABLED
                </x-cerberus::form.label>
                <select name="enabled" id="enabled" class="w-24">
                    <option @selected($user->enabled == true) value="1">YES</option>
                    <option @selected($user->enabled == false) value="0">NO</option>
                </select>
                <x-cerberus::form.error value="{{ 'enabled' }}" />
            </x-cerberus::form.field-block>


            <x-cerberus::form.field-block>
                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.admins.index') }}"
                    deleteRoute="{{ route('cerberus.users.destroy', $user) }}" />
            </x-cerberus::form.field-block>
        </form>
    </div>
</x-cerberus::cerberus>
