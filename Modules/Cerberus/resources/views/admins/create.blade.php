<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΠΡΟΣΘΗΚΗ ADMIN</h1>
    </x-slot:header>

    <form action="{{ route('cerberus.admins.store') }}" method="POST" class="w-2/4 mt-2">
        @csrf

        <input type="hidden" name="type" value="admin">
        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="name">
                Name
            </x-cerberus::form.label>
            <x-cerberus::form.input id="name" name="name" value="{{ old('name') }}" />
            <x-cerberus::form.error value="name" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="email">
                Email
            </x-cerberus::form.label>
            <x-cerberus::form.input id="email" name="email" value="{{ old('email') }}" />
            <x-cerberus::form.error value="email" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="password">
                Password
            </x-cerberus::form.label>
            <x-cerberus::form.input id="password" type="password" name="password" value="{{ old('password') }}" />
            <x-cerberus::form.error value="password" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.field-block>
            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.admins.index') }}" />
        </x-cerberus::form.field-block>

    </form>
</x-cerberus::cerberus>
