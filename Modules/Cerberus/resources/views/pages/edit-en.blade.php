<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΜΕΤΑΦΡΑΣΗ ΣΕΛΙΔΑΣ {{ $page->title }}</h1>

    </x-slot:header>

    <form action="{{ route('cerberus.pages.update', $page) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PATCH')

        <x-cerberus::alerts.flash type="success" timeout=10 />
        <x-cerberus::alerts.errors />

        <div class="px-4 sticky top-0 z-50 mb-4 bg-white flex flex-wrap justify-center md:justify-between items-center">
            <x-cerberus::form.buttons returnRoute="{{ route('cerberus.pages.edit', $page) }}"
                cancelRoute="{{ route('cerberus.pages.index') }}" />

            <div class="pb-2 grow flex flex-wrap justify-end gap-2 md:gap-6">

                <x-cerberus::form.link id="preview" href="{{ route('en.pages.show', $page->getTranslation('slug', 'en')) }}"
                class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white" target="_blank">
                    ΠΡΟΕΠΙΣΚΟΠΗΣΗ ΣΕΛΙΔΑΣ
                </x-cerberus::form.link>
            </div>


        </div>

        <div>
            <div class="lg:grid lg:grid-cols-12 lg:gap-4">
                <div class="col-span-8">
                    <x-cerberus::form.title-block>
                        <h3 class="text-white">MAIN CONTENT</h3>
                    </x-cerberus::form.title-block>

                    <input type="hidden" name="locale" value="{{ $locale }}">

                    <livewire:cerberus::titles.edit-title :model="$page" :locale="$locale" :showGreek="true" />


                    <x-cerberus::form.field-block x-data>
                        <x-cerberus::form.label for="content[{{ $locale }}]" class="flex justify-between items-center">
                            CONTENT {{ __('cerberus::base.' . $locale) }}

                            <div>
                                <livewire:cerberus::elements.suggest-translation :content="$page->getTranslation('content', 'el')"
                                    :id="'content'" />
                               
                                <span
                                    x-on:translate-content.window="tinyMCE.get('content[en]').setContent($event.detail.translation)"></span>
                            </div>
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="content[{{ $locale }}]" class="editor"
                            name="content[{{ $locale }}]">
                            {{ old('content.' . $locale, $page->getTranslation('content', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="{{ 'content.' . $locale }}" />

                        <div class="px-3 py-1 mt-1 bg-gray-200 h-[200px] overflow-scroll">Ελληνικά: {!! $page->getTranslation('content', 'el') !!}</div>
                    </x-cerberus::form.field-block>

                    {{-- SEO ATTRIBUTES --}}
                    <x-cerberus::seo.seo-attributes :model="$page" :locale="$locale" />

                </div>
                <div class="col-span-4">
                    <x-cerberus::form.title-block class="p-3">
                        <h3 class="text-white">MAIN IMAGE ATTRIBUTES</h3>
                    </x-cerberus::form.title-block>
                    <x-cerberus::form.field-block class="p-3">
                        <x-cerberus::form.label for="image">
                            ΚΕΝΤΡΙΚΗ ΕΙΚΟΝΑ
                            @if ($page->mainImage())
                                <img src="{{ asset('storage/' . $page->imageSm()) }}" alt="">

                                <label class="block mb-3 mt-2" for="deleteImage">
                                    Επιλέξτε για διαγραφή της εικόνας
                                    <input type="checkbox" name="deleteImage" id="deleteImage">
                                </label>
                            @endif
                        </x-cerberus::form.label>
                     
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block x-data>
                        <x-cerberus::form.label for="alt[{{ $locale }}]" class="flex justify-between items-center">
                            ALT TAG {{ __('cerberus::base.' . $locale) }}

                            <div>
                                <livewire:cerberus::elements.suggest-translation :content="$page->mainImage()?->getTranslation('alt', 'el')"
                                    :id="'alt'" />
                               
                                <span
                                    x-on:translate-alt.window="document.getElementById('alt[en]').value = $event.detail.translation"></span>
                            </div>

                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                            value="{{ old('alt.' . $locale, $page?->mainImage()?->getTranslation('alt', $locale, false)) }}" />
                        <x-cerberus::form.error value="alt.{{ $locale }}" />

                        <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά:
                            {{ $page->mainImage()?->getTranslation('alt', 'el') }}
                        </div>
                    </x-cerberus::form.field-block>

                   
                </div>

            </div>

            <div class="flex items-center flex-wrap justify-center gap-2 md:justify-between px-4 pb-8">

                <x-cerberus::form.buttons 
                    cancelRoute="{{ route('cerberus.pages.index') }}" />

    </form>

    <form action="{{ route('cerberus.pages.clear.translation', $page) }}" method="page">
        @csrf
        <x-cerberus::form.button
            class="bg-slate-400 text-white hover:bg-slate-500"
            onclick="return confirm('Θέλετε σίγουρα να διαγράψετε τη μετάφραση;');"
            >
            Κατάργηση Μετάφρασης
        </x-cerberus::form.button>
    </form>

    </div>

    </div>

</x-cerberus::cerberus>
