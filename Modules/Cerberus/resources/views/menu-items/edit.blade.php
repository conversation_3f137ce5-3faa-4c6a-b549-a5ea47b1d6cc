<x-cerberus::cerberus>
    <x-slot:header>
        <h1 class="">Edit Menu Item: {{ $menuItem->title }}</h1>
        <h2>Menu: {{ $menuItem->menu->title }}</h2>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=15 />
    <x-cerberus::alerts.flash type="error" timeout=15 />

    <form action="{{ route('cerberus.menu-items.update', [$menuItem->menu, $menuItem]) }}" method="POST"
        class="xl:w-4/5 mx-auto my-4">
        @csrf
        @method('PATCH')

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="menu_id">
                Menu
            </x-cerberus::form.label>
            <select name="menu_id" id="menu_id" class="w-auto">
                <option value="" disabled>Select Menu</option>
                @foreach ($menus as $main_menu)
                    <option @selected($menuItem->menu_id == $main_menu->id) value="{{ $main_menu->id }}">{{ $main_menu->title }}</option>
                @endforeach
            </select>
        </x-cerberus::form.field-block>

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="title">
                Menu Title
            </x-cerberus::form.label>
            <x-cerberus::form.input id="title" name="title" value="{{ old('title', $menuItem->title) }}" />
            <x-cerberus::form.error value="title" />
        </x-cerberus::form.field-block>


        <livewire:cerberus::menu-items.menu-type :menuItem="$menuItem" />

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="parent_id">
                Parent Item
            </x-cerberus::form.label>
            <x-cerberus::form.info>
                Επιλέξτε αν αυτό το menu item θα εμφανίζεται ως υπομενού σε κάποιο άλλο item.
            </x-cerberus::form.info>
            <select name="parent_id" id="parent_id" class="w-1/2">
                <option @selected($menuItem->parent_id == 0) value="0">No parent</option>
                @foreach ($menu->menuItems as $item)
                    <option @selected($item->id == $menuItem->parent_id) @disabled($item->id == $menuItem->id) value="{{ $item->id }}">
                        {{ $item->title }}</option>
                @endforeach
            </select>
            <x-cerberus::form.error value="parent_id" />
        </x-cerberus::form.field-block>

        <x-cerberus::form.field-block>
            <x-cerberus::form.label for="page_title">
                Page Title
                <x-cerberus::form.info>
                    Ο κεντρικός τίτλος που θα εμφανίζεται στη σελίδα (προαιρετικό)
                </x-cerberus::form.info>

            </x-cerberus::form.label>
            <x-cerberus::form.input id="page_title" name="page_title"
                value="{{ old('page_title', $menuItem->page_title) }}" />
            <x-cerberus::form.error value="page_title" />
        </x-cerberus::form.field-block>

        <livewire:cerberus::elements.publish-button :model="$menuItem" />

        <div class="flex items-center justify-between px-4">
            <x-cerberus::form.buttons
                cancelRoute="{{ route('cerberus.menu-items.index', [$menuItem->menu, $menuItem]) }}" />


    </form>
    <x-cerberus::form.delete deleteRoute="{{ route('cerberus.menu-items.destroy', [$menuItem->menu, $menuItem]) }}" />
    </div>
</x-cerberus::cerberus>
