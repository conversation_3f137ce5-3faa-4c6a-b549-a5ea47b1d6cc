<x-cerberus::cerberus>

    <x-slot:header>
        <h1>Επεξεργασία Banner</h1>
    </x-slot:header>

    <div class="m-2">
        <form action="{{ route('cerberus.pagebanners.update', $page_banner) }}" method="POST"
            enctype="multipart/form-data">
            @csrf
            @method('PATCH')

            <div
                class="px-4 sticky top-0 z-50 mb-4 bg-white flex flex-wrap justify-center md:justify-between items-center">
                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.pagebanners.index') }}" />
            </div>

            <x-cerberus::form.title-block>
                <h3 class="text-white">BANNER ATTRIBUTES</h3>
            </x-cerberus::form.title-block>

            <x-cerberus::form.field-block class="w-1/2">
                <x-cerberus::form.label for="filename">
                    Banner Image
                    @if ($page_banner->filename)
                        <img src="{{ asset('storage/pagebanners/' . $page_banner->filename) }}" alt="">
                    @endif
                </x-cerberus::form.label>
                <x-cerberus::form.input id="filename" type="file" name="filename" value="{{ old('filename') }}" />
                <x-cerberus::form.error value="filename" />
            </x-cerberus::form.field-block>

            <div class="flex gap-3">
                @foreach (LocaleConfig::getLocales() as $locale)
                    <div class="w-[48%]">
                        <x-cerberus::form.field-block class="">
                            <x-cerberus::form.label for="alt[{{ $locale }}]">
                                ALT {{ __('base.' . $locale) }}
                            </x-cerberus::form.label>
                            <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                                type="alt"
                                value="{{ old('alt.' . $locale, $page_banner->getTranslation('alt', $locale)) }}" />
                            <x-cerberus::form.error value="alt.{{ $locale }}" />
                        </x-cerberus::form.field-block>

                        <x-cerberus::form.field-block class="">
                            <x-cerberus::form.label for="url[{{ $locale }}]">
                                URL {{ __('base.' . $locale) }}
                            </x-cerberus::form.label>
                            <x-cerberus::form.input id="url[{{ $locale }}]" name="url[{{ $locale }}]"
                                type="url"
                                value="{{ old('url.' . $locale, $page_banner->getTranslation('url', $locale)) }}" />
                            <x-cerberus::form.error value="url.{{ $locale }}" />
                        </x-cerberus::form.field-block>
                    </div>
                @endforeach
            </div>



            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="description">
                    Περιγραφή
                    <x-cerberus::form.info>
                        Σημειώσεις για admins
                    </x-cerberus::form.info>
                </x-cerberus::form.label>
                <x-cerberus::form.textarea id="description" name="description">
                    {{ $page_banner->description }}
                </x-cerberus::form.textarea>
                <x-cerberus::form.error value="description" />
            </x-cerberus::form.field-block>


            <x-cerberus::form.title-block>
                <h3 class="text-white">RELATED CATEGORY PAGES</h3>
            </x-cerberus::form.title-block>

            <x-cerberus::form.field-block class="flex flex-wrap items-center gap-4">
                @foreach ($categories as $category)
                    <x-cerberus::form.checkbox name="categories[]" value="{{ $category->id }}" :checked="$page_banner->categories->contains($category->id)">
                        {{ $category->getTranslation('title', 'el') }}
                    </x-cerberus::form.checkbox>
                @endforeach
            </x-cerberus::form.field-block>

            <x-cerberus::form.title-block>
                <h3 class="text-white">RELATED TAG PAGES</h3>
            </x-cerberus::form.title-block>

            <x-cerberus::form.field-block class="flex flex-wrap items-center gap-4">
                @foreach ($tags as $tag)
                    <x-cerberus::form.checkbox name="tags[]" value="{{ $tag->id }}" :checked="$page_banner->tags->contains($tag->id)">
                        {{ $tag->getTranslation('title', 'el') }}
                    </x-cerberus::form.checkbox>
                @endforeach
            </x-cerberus::form.field-block>

            <x-cerberus::form.title-block>
                <h3 class="text-white">PUBLISHING OPTIONS</h3>
            </x-cerberus::form.title-block>

            <livewire:cerberus::elements.publish-button :model="$page_banner" />


            <div class="flex items-center justify-between px-4">

                <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.pagebanners.index') }}" />


        </form>

        <x-cerberus::form.delete deleteRoute="{{ route('cerberus.pagebanners.destroy', $page_banner) }}" />
    </div>
</x-cerberus::cerberus>
