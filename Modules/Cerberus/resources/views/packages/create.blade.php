<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΠΡΟΣΘΗΚΗ TRAVEL PACKAGE</h1>
    </x-slot:header>

    <form action="{{ route('cerberus.packages.store') }}" method="POST">
        @csrf

        <livewire:cerberus::titles.create-title />

        <div>
            <x-cerberus::form.field-block>
                <x-cerberus::form.label for="type">
                    PACKAGE TYPE
                </x-cerberus::form.label>
                <select name="type" id="type" class="w-1/2">
                    <option selected disabled>Επιλέξτε τύπο</option>
                    <option value="travel">Travel</option>
                    <option value="cruise">Cruise</option>
                </select>
                <x-cerberus::form.error value="type" />
        </x-cerberus::form.field-block>
        </div>

        <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.packages.index') }}" class="px-4" />

    </form>
</x-cerberus::cerberus>
