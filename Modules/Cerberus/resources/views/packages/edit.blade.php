<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΕΠΕΞΕΡΓΑΣΙΑ ΠΑΚΕΤΟΥ: {{ $package->title }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" timeout=10 />
    <x-cerberus::alerts.flash type="error" timeout=10 />


    <form action="{{ route('cerberus.packages.update', $package) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PATCH')



        <div class="px-4 sticky top-0 z-50 mb-4 bg-white flex flex-wrap justify-center md:justify-between items-center">
            <x-cerberus::form.buttons translationRoute="{{ route('cerberus.packages.en.edit', $package) }}"
                cancelRoute="{{ route('cerberus.packages.index') }}" />

            <div class="pb-2 grow flex flex-wrap justify-end gap-2 md:gap-6">

                <x-cerberus::form.link id="preview" href="{{ route('el.travel.show', $package) }}"
                    class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white"
                    target="_blank">
                    ΠΡΟΕΠΙΣΚΟΠΗΣΗ ΠΑΚΕΤΟΥ
                </x-cerberus::form.link>
            </div>


        </div>

        <div class="lg:grid lg:grid-cols-12 lg:gap-4">

            <div class="col-span-8">
                <x-cerberus::form.title-block>
                    <h3 class="text-white">MAIN CONTENT</h3>
                </x-cerberus::form.title-block>

                <div>
                    <input type="hidden" name="locale" value="{{ $locale }}">
                </div>

                <livewire:cerberus::titles.edit-title :model="$package" :locale="$locale" />

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="subtitle[{{ $locale }}]">
                        SUBTITLE {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.input id="subtitle[{{ $locale }}]" name="subtitle[{{ $locale }}]"
                        value="{{ old('subtitle.' . $locale, $package->getTranslation('subtitle', $locale, false)) }}" />
                    <x-cerberus::form.error value="subtitle[el]" />
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="content[{{ $locale }}]">
                        CONTENT {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="content[{{ $locale }}]" name="content[{{ $locale }}]"
                        data-height="900" class="editor">
                        {{ old('content.' . $locale, $package->getTranslation('content', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="content.'.{{ $locale }}" />
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="tagline[{{ $locale }}]">
                        TAGLINE {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>

                    <x-cerberus::form.info>
                        Ιδανικό μέγεθος: 30-40 λέξεις
                    </x-cerberus::form.info>

                    <x-cerberus::form.textarea id="tagline[{{ $locale }}]" name="tagline[{{ $locale }}]"
                        class="h-16">
                        {{ old('tagline.' . $locale, $package->getTranslation('tagline', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="tagline.' . {{ $locale }}" />
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="itinerary[{{ $locale }}]">
                        ITINERARY FREE TEXT {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="itinerary[{{ $locale }}]"
                        name="itinerary[{{ $locale }}]" class="editor">
                        {{ old('itinerary.' . $locale, $package->getTranslation('itinerary', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="itinerary.'.{{ $locale }}" />
                </x-cerberus::form.field-block>

                
                <x-cerberus::form.field-block>

                    {{-- Υπάρχοντα Itinerary Steps  --}}

                    <livewire:cerberus::packages.itinerary-steps :package="$package" :locale="$locale" />

                    {{-- Creating new steps  with Alpine Js --}}

                    <div x-data="{
                        items: [],
                        adding: false,
                        count: {{ $package->itinerary_steps->count() }},
                        initEditor() {
                            setTimeout(() => {
                                tinymce.init({
                                    selector: '.editor',
                                    plugins: 'lists link image code',
                                    toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | code',
                                });
                            }, 0);
                        }
                    }" x-init="initEditor()" @items-updated.window="initEditor()">
                        <template x-for="item in items" :key="item">
                            <div class="border-t-2 pb-3">
                                <div class="mx-2 mb-4 bg-slate-200 py-2 px-3 rounded-md">
                                    Itinerary Step <span
                                        x-text="items.indexOf(item)+{{ $package->itinerary_steps->count() + 1 }}"></span>
                                </div>
                                <div>
                                    <x-cerberus::form.label>Title</x-cerberus::form.label>
                                    <x-cerberus::form.input x-bind:name="`steps[${item+count}][title][{{ $locale }}]`"
                                        value="" />
                                </div>
                                <div>
                                    <x-cerberus::form.label>Content</x-cerberus::form.label>
                                    <x-cerberus::form.textarea x-bind:name="`steps[${item+count}][content][{{ $locale }}]`"
                                        value="" class="editor">
                                    </x-cerberus::form.textarea>
                                </div>

                                <button @click.prevent="items.splice(items.indexOf(item), 1).sort()"
                                    class="ml-3 float-right bg-red-500 p-1 text-white rounded-lg">Remove</button>
                            </div>
                        </template>
                        <x-cerberus::form.button @click.prevent="items.push(items.length+1); $dispatch('items-updated')"
                            id="addStep" class="bg-slate-800 text-white mt-4">
                            <x-cerberus::icons.add-icon />
                        </x-cerberus::form.button>
                    </div>
                </x-cerberus::form.field-block>

                <div class="flex">
                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="included[{{ $locale }}]">
                            INCLUDED {{ __('cerberus::base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="included[{{ $locale }}]" data-height="500"
                            name="included[{{ $locale }}]" class="editor">
                            {{ old('included.' . $locale, $package->getTranslation('included', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="included.'.{{ $locale }}" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="not_included[{{ $locale }}]">
                            NOT INCLUDED {{ __('cerberus::base.' . $locale) }}
                        </x-cerberus::form.label>
                        <x-cerberus::form.textarea id="not_included[{{ $locale }}]" data-height="500"
                            name="not_included[{{ $locale }}]" class="editor">
                            {{ old('not_included.' . $locale, $package->getTranslation('not_included', $locale, false)) }}
                        </x-cerberus::form.textarea>
                        <x-cerberus::form.error value="not_included.'.{{ $locale }}" />
                    </x-cerberus::form.field-block>
                </div>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="info[{{ $locale }}]">
                        INFO {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>
                    <x-cerberus::form.textarea id="info[{{ $locale }}]" name="info[{{ $locale }}]"
                        class="editor">
                        {{ old('info.' . $locale, $package->getTranslation('info', $locale, false)) }}
                    </x-cerberus::form.textarea>
                    <x-cerberus::form.error value="info.'.{{ $locale }}" />
                </x-cerberus::form.field-block>

                {{-- SEO ATTRIBUTES --}}
                <x-cerberus::seo.seo-attributes :model="$package" :locale="$locale" />

                {{-- SEO CHECKER --}}
                <x-cerberus::seo.seo-checker :model="$package" />
            </div>
            <div class="col-span-4">
                <x-cerberus::form.title-block class="p-3">
                    <h3 class="text-white">MAIN IMAGE ATTRIBUTES</h3>
                </x-cerberus::form.title-block>

                <x-cerberus::form.field-block class="p-3">
                    <x-cerberus::form.label for="image">
                        ΚΕΝΤΡΙΚΗ ΕΙΚΟΝΑ
                        @if ($package->mainImage())
                            <img src="{{ asset('storage/' . $package->imageSm()) }}" alt="">
                            <label class="block mb-3" for="deleteImage">
                                Επιλέξτε για διαγραφή της εικόνας
                                <input type="checkbox" name="deleteImage" id="deleteImage">
                            </label>
                        @endif
                    </x-cerberus::form.label>

                    <x-cerberus::form.input id="image" name="image" type="file"
                        value="{{ $package->mainImage() }}" />
                    <x-cerberus::form.error value="image" />
                </x-cerberus::form.field-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="alt[{{ $locale }}]">
                        ALT TAG {{ __('cerberus::base.' . $locale) }}
                    </x-cerberus::form.label>

                    <x-cerberus::form.input id="alt[{{ $locale }}]" name="alt[{{ $locale }}]"
                        value="{{ old('alt.' . $locale, $package?->mainImage()?->getTranslation('alt', $locale, false)) }}" />
                    <x-cerberus::form.error value="alt.{{ $locale }}" />

                </x-cerberus::form.field-block>

                <x-cerberus::form.title-block class="p-3">
                    <h3 class="text-white">PRICELIST IMAGE</h3>
                </x-cerberus::form.title-block>

                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="pricelist">
                        ΤΙΜΟΚΑΤΑΛΟΓΟΣ
                        @if ($package->pricelistImage())
                            <img src="{{ asset('storage/' . $package->pricelistImageSm()) }}" alt="">
                            <label class="block mb-3" for="deletePricelist">
                                Επιλέξτε για διαγραφή της εικόνας
                                <input type="checkbox" name="deletePricelist" id="deletePricelist">
                            </label>
                        @endif
                    </x-cerberus::form.label>
                    <x-cerberus::form.input id="pricelist" name="pricelist" value="{{ old('pricelist') }}"
                        type="file" />
                    <x-cerberus::form.error value="pricelist" />
                </x-cerberus::form.field-block>

                <x-cerberus::form.title-block>
                    <h3 class="text-white">CATEGORIES & TAGS</h3>
                </x-cerberus::form.title-block>

                <livewire:cerberus::tags.show :locale="$locale" :model="$package" :category=true />

                <livewire:cerberus::tags.show :locale="$locale" :model="$package" />

                <x-cerberus::form.title-block>
                    <h3 class="text-white">PRODUCT OPTIONS</h3>
                </x-cerberus::form.title-block>

                <div class="flex flex-wrap items-end">
                    <x-cerberus::form.field-block class=lg:w-1/3>
                        <x-cerberus::form.label for="duration">
                            DURATION <span class="text-xs">(DAYS)</span>
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="duration" name="duration" type="number"
                            value="{{ old('duration', $package->duration) }}" />
                        <x-cerberus::form.error value="duration" />
                    </x-cerberus::form.field-block>
                    <x-cerberus::form.field-block class=lg:w-1/3>
                        <x-cerberus::form.label for="sku">
                            SKU
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="sku" name="sku"
                            value="{{ old('sku', $package->sku) }}" />
                        <x-cerberus::form.error value="sku" />
                    </x-cerberus::form.field-block>
                    <x-cerberus::form.field-block class=lg:w-1/3>
                        <x-cerberus::form.label for="price">
                            PRICE
                        </x-cerberus::form.label>
                        <div class="flex items-center gap-1">
                            <x-cerberus::form.input id="price" name="price"
                                value="{{ old('price', $package->price) }}" class="text-right" /> <span
                                class="inline-flex">€</span>
                        </div>
                        <x-cerberus::form.error value="price" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block >
                        <x-cerberus::form.label for="price_fixed">
                            PRICE FIXED
                        </x-cerberus::form.label>
                        <input id="price_fixed" name="price_fixed" type="checkbox" class="rounded-full" @checked($package->price_fixed == 1)
                             />
                        <x-cerberus::form.error value="price_fixed" />
                    </x-cerberus::form.field-block>
                </div>
                <div>
                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="type">
                            PACKAGE TYPE
                        </x-cerberus::form.label>
                        <select name="type" id="type" class="w-1/2">
                            <option @selected($package->type == 'travel') value="travel">Travel</option>
                            <option @selected($package->type == 'cruise') value="cruise">Cruise</option>
                        </select>
                    </x-cerberus::form.field-block>
                </div>

                <x-cerberus::form.title-block>
                    <h3 class="text-white">PUBLISHING OPTIONS</h3>
                </x-cerberus::form.title-block>
                <div class="">
                    <livewire:cerberus::elements.publish-button :model="$package" />
                    <livewire:cerberus::elements.featured-button :model="$package" />

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="created_at">
                            ΗΜΕΡΟΜΗΝΙΑ ΔΗΜΙΟΥΡΓΙΑΣ
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="created_at" name="created_at" disabled class="!bg-slate-300"
                            value="{{ old('published_at', \Carbon\Carbon::parse($package->created_at)->format('d-m-Y H:i')) }}" />
                        <x-cerberus::form.error value="published_at" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block>
                        <x-cerberus::form.label for="created_at">
                            ΤΕΛΕΥΤΑΙΑ ΕΝΗΜΕΡΩΣΗ
                        </x-cerberus::form.label>
                        <x-cerberus::form.input id="created_at" name="created_at" disabled class="!bg-slate-300"
                            value="{{ old('published_at', \Carbon\Carbon::parse($package->updated_at)->format('d-m-Y H:i')) }}" />
                        <x-cerberus::form.error value="published_at" />
                    </x-cerberus::form.field-block>

                    <x-cerberus::form.field-block class="relative">
                        <x-cerberus::form.label for="depart_dates">
                            ΗΜΕΡΟΜΗΝΙΕΣ ΑΝΑΧΩΡΗΣΗΣ
                            <x-cerberus::form.info>Click στο ημερολόγιο για επιλογή / αποεπιλογή</x-cerberus::form.info>
                            <x-cerberus::icons.calendar-icon />

                        </x-cerberus::form.label>

                        <input id="depart_dates" name="depart_dates"
                            class="w-0 bg-transparent border-none focus:ring-0 absolute top-0"
                            value="{{ old('depart_dates', $package->departDates->pluck('date')->implode(', ')) }}" />
                        <x-cerberus::form.error value="depart_dates" />

                        <div class="">
                            @foreach ($package->departDates as $departDate)
                                <div class="border-b-2 w-auto border-slate-300 rounded-lg px-2 py-1 ">
                                    {{ Carbon\Carbon::parse($departDate->date)->translatedFormat('d F') }}</div>
                            @endforeach
                        </div>
                    </x-cerberus::form.field-block>
                </div>
            </div>
        </div>


        <div class="flex items-center justify-between px-4">

            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.packages.index') }}" />

    </form>

    <x-cerberus::form.delete deleteRoute="{{ route('cerberus.packages.destroy', $package) }}" />

    </div>

    @section('footer-scipts')
        <script>
            // Provide default dates dynamically from Blade
            const departDates = @json($package->departDates->pluck('date'));
            window.defaultDepartDates = departDates;
        </script>
    @endsection
</x-cerberus::cerberus>
