<x-cerberus::cerberus>
    <x-slot:header>
        <h1>ΕΠΕΞΕΡΓΑΣΙΑ USER: {{ $user->id }}</h1>
    </x-slot:header>

    <x-cerberus::alerts.flash type="success" />

    <div>
        <form action="{{ route('cerberus.users.update', $user) }}" method="POST">
            @csrf
            @method('PATCH')

            <input type="hidden" name="type" value="admin">
            <div class="flex gap-12">
                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="name">
                        NAME
                    </x-cerberus::form.label>
                    <x-cerberus::form.input id="name" name="name" value="{{ old('name', $user->name) }}" />
                    <x-cerberus::form.error value="{{ 'name' }}" />
                </x-cerberus::form.field-block>
            </div>

            <div class="flex gap-12">
                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="email">
                        EMAIL
                    </x-cerberus::form.label>
                    <x-cerberus::form.input id="email" name="email" value="{{ old('email', $user->email) }}" />
                    <x-cerberus::form.error value="{{ 'email' }}" />
                </x-cerberus::form.field-block>
            </div>

            <div class="flex gap-12">
                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="enabled">
                        ENABLED
                    </x-cerberus::form.label>
                    <select name="enabled" id="enabled" class='w-48'>
                        <option @selected($user->enabled == true) value="1">YES</option>
                        <option @selected($user->enabled == false) value="0">NO</option>
                    </select>
                    <x-cerberus::form.error value="{{ 'enabled' }}" />
                </x-cerberus::form.field-block>
            </div>

            <div class="flex gap-12">
                <x-cerberus::form.field-block>
                    <x-cerberus::form.label for="type">
                        TYPE
                    </x-cerberus::form.label>
                    <x-cerberus::form.input disabled
                                            value="{{ $user->type }}" />
                </x-cerberus::form.field-block>
            </div>

            <x-cerberus::form.buttons cancelRoute="{{ route('cerberus.users.index') }}"
                deleteRoute="{{ route('cerberus.users.destroy', $user) }}" />
        </form>
    </div>
</x-cerberus::cerberus>
