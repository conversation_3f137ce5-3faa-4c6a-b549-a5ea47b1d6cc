@props(['model', 'locale'])

<x-cerberus::form.title-block>
    <h3 class="text-white">SEO ATTRIBUTES</h3>
</x-cerberus::form.title-block>

<x-cerberus::form.field-block x-data="{ count: 0, message: '' }" x-init="count = $refs.countme.value.length">
    <x-cerberus::form.label for="meta_title[{{ $locale }}]" class="flex justify-between">
        META TITLE {{ __('cerberus::base.' . $locale) }}

        @if ($locale == 'en')
            <div>
                <livewire:cerberus::elements.suggest-translation :content="$model->getTranslation('meta_title', 'el')" :id="'metatitle'" />
                <span
                    x-on:translate-metatitle.window="document.getElementById('meta_title[en]').value = $event.detail.translation"></span>
            </div>
        @endif

    </x-cerberus::form.label>

    <x-cerberus::form.input id="meta_title[{{ $locale }}]" name="meta_title[{{ $locale }}]"
        value="{{ old('meta_title.' . $locale, $model->getTranslation('meta_title', $locale)) }}" x-ref="countme"
        x-on:keyup="count = $refs.countme.value.length" />
    <x-cerberus::form.error value="meta_title[{{ $locale }}]" />
    <p class="text-right mt-2" :class="count >= 65 ? 'text-red-500' : 'text-pacific-800'"><span x-text="count"></span>
        <span>/ 65 χαρακτήρες</span>
    </p>

    <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $model->getTranslation('meta_title', 'el') !!}</div>
</x-cerberus::form.field-block>

<x-cerberus::form.field-block x-data="{ count: 0, message: '' }" x-init="count = $refs.countme.value.length">
    <x-cerberus::form.label for="meta_description[{{ $locale }}]" class="flex justify-between">
        META DESCRIPTION {{ __('cerberus::base.' . $locale) }}

        @if ($locale == 'en')
        <div>
            <livewire:cerberus::elements.suggest-translation :content="$model->getTranslation('meta_description', 'el')" :id="'metadescription'" />
            <span
                x-on:translate-metadescription.window="document.getElementById('meta_description[en]').value = $event.detail.translation"></span>
        </div>
    @endif
    </x-cerberus::form.label>
    <x-cerberus::form.textarea id="meta_description[{{ $locale }}]" name="meta_description[{{ $locale }}]"
        class="h-24" x-ref="countme" x-on:keyup="count = $refs.countme.value.length">
        {{ old('meta_description.' . $locale, $model->getTranslation('meta_description', $locale)) }}
    </x-cerberus::form.textarea>
    <div class="px-3 py-1 mt-1 bg-gray-200">Ελληνικά: {!! $model->getTranslation('meta_description', 'el') !!}</div>
    <x-cerberus::form.error value="meta_title[{{ $locale }}]" />

    <p class="text-right mt-2"
        :class="{
            'text-sun-700': count >= 160 && count < 300,
            'text-red-500': count >= 300,
            'text-pacific-800': count < 160
        }">
        <span x-text="count"></span> <span>/ 160 χαρακτήρες</span>
    </p>

</x-cerberus::form.field-block>
