@props(['active'])

@php
$classes = ($active ?? false)
            ? 'items-center px-1 pt-1 border-b-2 border-pacific text-sm font-medium leading-5 text-gray-100 hover:text-gray-400 focus:outline-none focus:border-indigo-700 transition duration-150 ease-in-out'
            : 'items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium leading-5 text-gray-300 hover:text-gray-400 focus:outline-none focus:text-gray-700 focus:border-gray-300 transition duration-150 ease-in-out';
@endphp

<a {{ $attributes->merge(['class' => $classes]) }}>
    {{ $slot }}
</a>
