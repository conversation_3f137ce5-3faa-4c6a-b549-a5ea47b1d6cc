@props([
    'deleteRoute' => NULL,
    'prompt' => 'Θέλετε να προχωρήσετε σε διαγραφή;',
    ])

@if ($deleteRoute)
    <form action="{{$deleteRoute}}" method="POST" class="inline-flex cursor-pointer">
        @csrf
        @method('DELETE')

        <x-cerberus::.form.button type="submit"
            {{ $attributes->merge(['class'=>' border border-red-500 hover:bg-red-500  text-white items-center']) }}
            onclick="return confirm(`{{ $prompt }}`);"
            >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 fill-red-500"  viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            {{-- {{__('Διαγραφή')}} --}}
        </x-cerberus::.form.button>

    </form>
@endif
