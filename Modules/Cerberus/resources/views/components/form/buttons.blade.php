@props(['deleteRoute' => null, 'cancelRoute', 'translationRoute', 'closeWindow', 'returnRoute'])

<div class="flex items-center justify-between grow">
    
    
    <div {{ $attributes(['class' => 'flex justify-start flex-wrap gap-2 md:gap-6 py-4 md:py-8 grow']) }}>
        <x-cerberus::form.button value="save"
            class="text-white bg-pacific-800 hover:bg-pacific-700 focus:ring-pacific-700 focus:ring-offset-pacific-800">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            {{ __('Αποθήκευση') }}
        </x-cerberus::form.button>
    
        @if (isset($cancelRoute))
            <x-cerberus::form.link link="{{ $cancelRoute }}" class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
                </svg>
                {{ __('Επιστροφή') }}
            </x-cerberus::form.link>
        @endif
    
    </div>
    <div>
    
        @if (isset($translationRoute))
            <x-cerberus::form.link link="{{ $translationRoute }}"
                class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white">
                <x-cerberus::icons.flags :lang="'en'" class="mr-2" />
                {{ __('Αγγλικά') }}
            </x-cerberus::form.link>
        @endif
    
        @if (isset($returnRoute))
            <x-cerberus::form.link link="{{ $returnRoute }}"
                class="border border-slate-400  hover:bg-slate-500 text-slate-600 font-semibold hover:text-white">
                <x-cerberus::icons.flags :lang="'el'" class="mr-2" />
                {{ __('Ελληνικά') }}
            </x-cerberus::form.link>
        @endif
    
        @if (isset($closeWindow))
            <x-cerberus::form.link class="bg-slate-400 text-white cursor-pointer hover:bg-slate-500"
                onclick="window.open('', '_self', ''); window.close();">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
                </svg>
                {{ __('Κλείσιμο') }}
            </x-cerberus::form.link>
        @endif
    </div>
</div>
