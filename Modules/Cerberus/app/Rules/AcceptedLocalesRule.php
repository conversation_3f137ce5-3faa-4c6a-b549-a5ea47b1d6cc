<?php

namespace Modules\Cerberus\Rules;

use Closure;
use CodeZero\LocalizedRoutes\Facades\LocaleConfig;
use Illuminate\Contracts\Validation\ValidationRule;

class AcceptedLocalesRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {

        if (! in_array($value, LocaleConfig::getLocales())) {
            $fail(__('Αυτή η γλώσσα δεν είναι έγκυρη'));
        };
    }
}
