<?php

namespace Modules\Cerberus\Exports;

use App\Models\Package;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class PackagesExport implements FromCollection, WithHeadings, WithMapping
{

    public function __construct(protected $packages)
    {
        
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {        
        $packages = Package::whereIn('id', $this->packages)->get();

        return $packages;

    }

    public function map($package): array
    {
        return [
            $package->type,
            $package->getTranslation('title', 'el'), 
            strip_tags($package->getTranslation('content', 'el')),
            $package->categories->map(function($category){
                return $category->getTranslation('title', 'el');
            })->implode(', '),
            $package->duration,
            $package->price,
            asset('storage/'.$package->originalImage()),
        ];
    }

    public function headings(): array
    {
        return [
            'Είδος',
            'Τίτλος',
            'Περιγραφή',
            'Κατηγορίες',
            'Διάρκεια',
            'Τιμή',
            'Εικόνα',
        ];
    }
}
