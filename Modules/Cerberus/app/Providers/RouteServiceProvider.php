<?php

namespace Modules\Cerberus\Providers;

use App\Models\Package;
use App\Models\Tag;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Called before routes are registered.
     *
     * Register any model bindings or pattern based filters.
     */
    public function boot(): void
    {
        parent::boot();

        // Route::bind('category', function ($value) {
        //     return Tag::where('id', $value)->firstOrFail();
        // });

        // Route::bind('food', function ($value) {
        //     return Package::where('id', $value)->firstOrFail();
        // });
    }

    /**
     * Define the routes for the application.
     */
    public function map(): void
    {
        $this->mapApiRoutes();

        $this->mapWebRoutes();
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     */
    protected function mapWebRoutes(): void
    {
        // generic routes for company admins
        Route::middleware(['web', 'auth', 'admin'])
            ->prefix('cerby')
            ->name('cerberus.')
            ->group(module_path('Cerberus', '/routes/web.php'));

//        Route::middleware('web')->group(module_path('Cerberus', '/routes/web.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     */
    protected function mapApiRoutes(): void
    {
        Route::middleware('api')->prefix('api')->name('api.')->group(module_path('Cerberus', '/routes/api.php'));
    }
}
