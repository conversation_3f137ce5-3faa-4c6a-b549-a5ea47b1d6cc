<?php

namespace Modules\Cerberus\Livewire\Faqs;

use App\Models\Post;
use App\Models\Package;
use Livewire\Component;

class MultipleInput extends Component
{
    public $faq;

    public $searchTerm;
    public $packages = [];
    public $posts = [];
    public $selectedPackages = [];

    public function selectedPackagesUpdate($package)
    {
        $this->selectedPackages[] = Package::find($package);
        
        $this->reset('searchTerm');
    }

    public function render()
    {
        
        $this->packages = Package::travel()
        ->where(function ($query) {
            $query->whereRaw('LOWER(title) LIKE ?', ['%' . strtolower($this->searchTerm) . '%']);
        })
        ->get();


        return view('cerberus::livewire.faqs.multiple-input');
    }
}
