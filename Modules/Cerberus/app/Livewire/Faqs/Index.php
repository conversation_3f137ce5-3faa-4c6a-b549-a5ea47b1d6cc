<?php

namespace Modules\Cerberus\Livewire\Faqs;

use App\Models\Faq;
use App\Models\MenuItem;
use Livewire\Component;

class Index extends Component
{
    public $searchTerm;
    public $featured;


    public function togglePublished(Faq $faq)
    {
        $faq->update(['published' => !$faq->published]);
    }

    public function toggleFeatured(Faq $faq)
    {
        $faq->update(['featured' => !$faq->featured]);
    }

    public function updateOrder($items)
    {
        foreach ($items as $item) {
            Faq::find($item['value'])->update(['order' => $item['order']]);
        }
    }

    public function deleteFaq(Faq $faq)
    {
        $faq->delete();

        session()->flash('success', 'Διαγράφηκε με επιτυχία');
    }

    public function render()
    {
        $faqs = Faq::query()
            ->where(function ($query) {
                $query->whereRaw('LOWER(question) LIKE ?', ['%' . strtolower($this->searchTerm) . '%'])
                    ->orWhereRaw('LOWER(answer) LIKE ?', ['%' . strtolower($this->searchTerm) . '%']);
            })
            ->where(function($query){
                $this->featured ? $query->where('featured', 1) : NULL;
            })
            ->orderBy('order')
            ->paginate();

        return view('cerberus::livewire.faqs.index', compact('faqs'));
    }
}
