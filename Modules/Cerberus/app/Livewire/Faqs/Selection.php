<?php

namespace Modules\Cerberus\Livewire\Faqs;

use App\Models\Faq;
use Livewire\Component;

class Selection extends Component
{

    public $model;
    public $searchTerm;

    public function render()
    {
        
        $faqs = Faq::query()
        ->published()
        ->where(function ($query) {
            $query->whereRaw('LOWER(question) LIKE ?', ['%' . strtolower($this->searchTerm) . '%']);
        })
        ->get();

        return view('cerberus::livewire.faqs.selection', compact('faqs'));
    }
}
