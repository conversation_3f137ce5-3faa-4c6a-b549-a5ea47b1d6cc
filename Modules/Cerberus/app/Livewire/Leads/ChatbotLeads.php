<?php

namespace Modules\Cerberus\Livewire\Leads;

use App\Models\ChatLead;
use Livewire\Component;

class ChatbotLeads extends Component
{

    public $searchTerm;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';

    public function sortBy($field)
    {
        if ($this->sortField == $field) {
            $this->sortDirection = $this->sortDirection == 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {

        $leads = ChatLead::query()
        ->where(function($query){    
                $query->where('name', 'LIKE', '%' . $this->searchTerm . '%')                    
                    ->orWhere('email', 'LIKE', '%' . $this->searchTerm . '%');
        })
        ->orderBy($this->sortField, $this->sortDirection)
        ->get();

        return view('cerberus::livewire.leads.chatbot-leads', compact('leads'));
    }
}
