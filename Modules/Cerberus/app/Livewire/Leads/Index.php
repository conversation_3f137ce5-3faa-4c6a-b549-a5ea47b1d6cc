<?php

namespace Modules\Cerberus\Livewire\Leads;

use App\Models\ContactLead;
use Livewire\Component;

class Index extends Component
{

    public $type;
    public $searchTerm;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';

    public function sortBy($field)
    {
        if ($this->sortField == $field) {
            $this->sortDirection = $this->sortDirection == 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {

        $leads = ContactLead::query()
            ->where('message_type', $this->type)
            ->where(function($query){        
                    $query->where('first_name', 'LIKE', '%' . $this->searchTerm . '%')
                        ->orWhere('last_name', 'LIKE', '%' . $this->searchTerm . '%')
                        ->orWhere('email', 'LIKE', '%' . $this->searchTerm . '%');
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->get();

        return view('cerberus::livewire.leads.index', compact('leads'));
    }
}
