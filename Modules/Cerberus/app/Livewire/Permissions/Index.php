<?php

namespace Modules\Cerberus\Livewire\Permissions;

use Spatie\Permission\Models\Permission;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;

class Index extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $searchTerm;


    public function deletePermission(Permission $permission)
    {
        $permission->delete();

        session()->flash('success', 'Διαγράφηκε με επιτυχία');

    }

    public function render()
    {
        $permissions = Permission::query()
            ->whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($this->searchTerm) . '%'])
            ->paginate();

        return view('cerberus::livewire.permissions.index', compact('permissions'));
    }
}
