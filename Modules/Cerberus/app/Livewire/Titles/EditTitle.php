<?php

namespace Modules\Cerberus\Livewire\Titles;

use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use Livewire\Attributes\Session;

class EditTitle extends Component
{
    public $locale;
    public $model;
    public $title;
    public $slug;
    public $showGreek = false;


    #[On('translate-title')]
    public function updatedTitle()
    {
        if (!$this->slug) {
            $this->slug = Str::slug($this->title);
        }
    }

    public function mount()
    {

        if ($this->model == 'foodtour') {
            $this->model = 'package';
        }

        $this->title = old('title.' . $this->locale) ?? $this->model->getTranslation('title', $this->locale, false);

        $this->slug = old('slug.' . $this->locale) ?? $this->model->getTranslation('slug', $this->locale, false);
    }

    public function render()
    {
        return view('cerberus::livewire.titles.edit-title');
    }
}
