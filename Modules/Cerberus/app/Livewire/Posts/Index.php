<?php

namespace Modules\Cerberus\Livewire\Posts;

use App\Models\Tag;
use App\Models\Post;
use Livewire\Component;
use App\Models\MenuItem;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;
use Illuminate\Support\Facades\Storage;

class Index extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $searchTerm;
    public $featured;
    public $categories;
    public $category;
    public $tags;
    public $tag;

    public function togglePublished(Post $post)
    {
        $post->update(['published' => !$post->published]);
    }

    public function toggleFeatured(Post $post)
    {
        $post->update(['featured' => !$post->featured]);
    }

    
    public function deletePost(Post $post)
    {

        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $post->slug) {

                return redirect()->route('cerberus.posts.index')->with('error', 'Η σελίδα δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        $post->delete();

        session()->flash('success', 'Το post διαγράφηκε με επιτυχία');

    }

    public function mount()
    {
        $this->tags = Tag::tag()->get();
        $this->categories = Tag::category()->get();
    }

    public function render()
    {
        $posts = Post::query()
            ->whereLocale('title', app()->getLocale())
            ->where(function ($query) {
                $query->whereRaw('LOWER(title) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%'])
                      ->orWhereRaw('LOWER(content) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%'])
                      ->orWhereRaw('LOWER(tagline) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%']);
            })
            ->where(function($query){
                $this->featured ? $query->where('featured', 1) : NULL;
            })
            ->where(function ($query) {
                $this->category ? $query->whereHas('categories', function($q){
                    $q->where('type', 'category')->where('tags.id', $this->category);
                }) : null;
            })
            ->where(function ($query) {
                $this->tag ? $query->whereHas('tags', function($q){
                    $q->where('type', 'tag')->where('tags.id', $this->tag);
                }) : null;
            })
            ->orderBy('created_at', 'desc')
            ->paginate(50);


        return view('cerberus::livewire.posts.index', compact('posts'));
    }
}
