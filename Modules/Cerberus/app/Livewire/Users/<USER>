<?php

namespace Modules\Cerberus\Livewire\Users;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;

class Index extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $searchTerm;

    public function updateStatus(User $user)
    {
        $user->update([
        	'enabled' => !$user->enabled
        ]);

        session()->flash('success' ,$user->enabled ? 'Ο χρήστης ενεργοποιήθηκε': 'Ο χρήστης απενεργοποιήθηκε');
    }

    public function deleteEnduser(User $user)
    {
        $user->delete();

        session()->flash('success', 'Διαγράφηκε με επιτυχία');

    }

    public function render()
    {
        $users = User::query()
        ->where('type', '=', 'enduser')
        ->where(function ($query) {
            $query->whereRaw('LOWER(email) LIKE ?', ['%' . strtolower($this->searchTerm) . '%'])
                  ->orWhereRaw('LOWER(name) LIKE ?', ['%' . strtolower($this->searchTerm) . '%']);
        })
        ->paginate();
    

        return view('cerberus::livewire.users.index', compact('users'));
    }
}
