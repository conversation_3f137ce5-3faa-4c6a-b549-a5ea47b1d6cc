<?php

namespace Modules\Cerberus\Livewire\Admins;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;

class Index extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $searchTerm;


    public function updateEnabled(User $user)
    {
        $user->update(['enabled'=> !$user->enabled ]);

        session()->flash('success' ,$user->enabled ? 'Ο χρήστης ενεργοποιήθηκε': 'Ο χρήστης απενεργοποιήθηκε');
    }

    public function deleteAdmin(User $user)
    {
        $user->delete();

        session()->flash('success', 'Διαγράφηκε με επιτυχία');

    }

    public function render()
    {
        $users = User::query()
            ->whereIn('type', ['admin', 'dev'])
            ->where(function ($query) {
                $query->whereRaw('LOWER(email) LIKE ?', ['%' . strtolower($this->searchTerm) . '%'])
                      ->orWhereRaw('LOWER(name) LIKE ?', ['%' . strtolower($this->searchTerm) . '%']);
            })
            ->paginate();

        return view('cerberus::livewire.admins.index', compact('users'));
    }
}
