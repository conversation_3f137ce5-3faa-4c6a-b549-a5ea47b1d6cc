<?php

namespace Modules\Cerberus\Livewire\Foodtours;

use App\Exports\FoodToursExport;
use App\Models\Package;
use Livewire\Component;
use App\Models\MenuItem;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Cerberus\Exports\PackagesExport;

class Index extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $searchTerm;
    public $featured;
    public $published;
    public array $selectedFoodtours;
    public $selectAll = false;

    public function togglePublished(Package $package)
    {
        $package->update(['published' => !$package->published]);
    }

    public function toggleFeatured(Package $package)
    {
        $package->update(['featured' => !$package->featured]);
    }


    public function deletePackage(Package $package)
    {

        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $package->slug) {

                return redirect()->route('cerberus.foodtours.index')->with('error', 'Το πακέτο δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        $package->delete();

        session()->flash('success', 'Το package διαγράφηκε με επιτυχία');
    }

    public function export()
    {
        return Excel::download(new FoodToursExport($this->selectedFoodtours), 'packages.csv', \Maatwebsite\Excel\Excel::CSV);
    }

    public function updatedSelectAll($value)
    {
        if($value) {
            $this->selectedFoodtours = $this->getFoodTours()->pluck('id')->toArray();
        } else {
            $this->selectedFoodtours = [];
        }
    }

    public function updatedSelectedFoodTours()
    {
        $this->selectAll = false;
    }

    public function render()
    {
        $foodtours = $this->getFoodTours()
            ->paginate();

        return view('cerberus::livewire.foodtours.index', compact('foodtours'));
    }

    public function getFoodTours()
    {
        return Package::food()
        ->whereLocale('title', app()->getLocale())
        ->where(function ($query) {
            $query->whereRaw('LOWER(title) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%'])
                ->orWhereRaw('LOWER(content) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%'])
                ->orWhereRaw('LOWER(tagline) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%']);
        })
        ->where(function ($query) {
            $this->featured ? $query->where('featured', 1) : NULL;
        })
        ->where(function ($query) {
            $this->published ? $query->where('published', 1) : NULL;
        });
    }
}
