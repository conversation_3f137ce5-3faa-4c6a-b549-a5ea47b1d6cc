<?php

namespace Modules\Cerberus\Livewire\Categories;

use App\Models\Tag;
use Livewire\Component;

class Homepage extends Component
{

    public function updateOrder($items)
    {
        foreach ($items as $item) {
            Tag::find($item['value'])->update(['order' => $item['order']]);
        }
    }

    public function render()
    {
        $categories = Tag::featured()
            ->orderBy('order')
            ->get();

        return view('cerberus::livewire.categories.homepage', compact('categories'));
    }
}
