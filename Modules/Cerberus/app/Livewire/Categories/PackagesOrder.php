<?php

namespace Modules\Cerberus\Livewire\Categories;

use Livewire\Component;
use App\Models\Taggable;

class PackagesOrder extends Component
{
    public $tag;
    public $packageType = 'travel';

    public function updateOrder($packages)
    {
        foreach ($packages as $package) {
            
            $this->tag->packages->where('id', $package['value'])->first()->pivot->update(['order' => $package['order']]);
        }

        session()->flash('success', 'Η σειρά ενημερώθηκε');
    }

    public function toggleType()
    {
        if($this->packageType == 'travel'){
            return $this->packageType = 'food';
        } elseif($this->packageType == 'food'){
            return $this->packageType = 'travel';
        }
    }

    public function render()
    {
        $packages = $this->tag->packages()
            ->where('type',  $this->packageType)
            ->orderByDesc('featured')
            ->orderBy('order')
            ->get();
 
        return view('cerberus::livewire.categories.packages-order', compact('packages'));
    }
}
