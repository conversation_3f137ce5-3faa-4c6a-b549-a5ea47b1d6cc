<?php

namespace Modules\Cerberus\Livewire\Categories;

use App\Models\Tag;
use Livewire\Component;
use App\Models\MenuItem;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;

class Index extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $searchTerm;

    public function deleteCategory(Tag $tag)
    {
        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $tag->slug) {

                return redirect()->route('cerberus.categories.index')->with('error', 'Η κατηγορία δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');

            }
        }
        
        $tag->delete();

        session()->flash('success', 'Το Category διαγράφηκε');
    }

    public function render()
    {
        $categories = Tag::category()
        ->whereLocale('title', app()->getLocale())
        ->whereRaw('LOWER(title) LIKE ?', ['%' . strtolower($this->searchTerm) . '%'])
        ->withCount(['packages', 'posts'])
        ->paginate(30);

        return view('cerberus::livewire.categories.index', compact('categories'));
    }
}
