<?php

namespace Modules\Cerberus\Livewire\Categories;

use Livewire\Component;

class BannersOrder extends Component
{
    public $tag;

    public function updateOrder($banners)
    {
        foreach ($banners as $banner) {
            
            $this->tag->banners->where('id', $banner['value'])->first()->pivot->update(['order' => $banner['order']]);
        }

        session()->flash('success', 'Η σειρά ενημερώθηκε');
    }

    public function render()
    {
        $banners = $this->tag->banners()->orderBy('order')->get();

        return view('cerberus::livewire.categories.banners-order', compact('banners'));
    }
}
