<?php

namespace Modules\Cerberus\Livewire\Pagebanners;

use Livewire\Component;
use App\Models\PageBanner;
use Illuminate\Support\Facades\Storage;

class Index extends Component
{

    public function togglePublished(PageBanner $banner)
    {
        $banner->update(['published' => !$banner->published]);
    }

    public function deleteBanner(PageBanner $banner)
    {
        
        if($banner->filename && Storage::exists('pagebanners/'.$banner->image) ) {
            Storage::delete('pagebanners/'.$banner->filename);
        }

        $banner->delete();

        session()->flash('success', 'Διαγράφηκε με επιτυχία');
    }

    public function render()
    {
        $pagebanners = PageBanner::paginate(30);

        return view('cerberus::livewire.pagebanners.index', compact('pagebanners'));
    }
}
