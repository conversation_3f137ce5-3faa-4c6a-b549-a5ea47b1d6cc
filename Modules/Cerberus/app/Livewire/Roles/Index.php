<?php

namespace Modules\Cerberus\Livewire\Roles;

use Spatie\Permission\Models\Role;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;

class Index extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $searchTerm;


    public function deleteRole(Role $role)
    {
        $role->delete();

        session()->flash('success', 'Διαγράφηκε με επιτυχία');

    }

    public function render()
    {
        $roles = Role::query()
            ->whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($this->searchTerm) . '%'])
            ->paginate();

        return view('cerberus::livewire.roles.index', compact('roles'));
    }
}
