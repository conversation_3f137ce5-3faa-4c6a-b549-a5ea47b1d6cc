<?php

namespace Modules\Cerberus\Livewire\Ships;

use App\Models\Image;
use Livewire\Component;
use App\Models\ShipFeature;
use Livewire\WithFileUploads;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\Storage;

class ShipFeatures extends Component
{

    public $ship;
    public $locale;

    public function updateFeaturesOrder($features)
    {
        foreach ($features as $feature) {
            ShipFeature::find($feature['value'])->update(['order' => $feature['order']]);
        }
    }

    public function deleteFeature(ShipFeature $feature)
    {
        if($feature->bannerImage() && Storage::exists('ships/features/'.$feature->bannerImage()->filename) )
        {
             Storage::delete('ships/features/'.$feature->bannerImage()->filename);

             $feature->bannerImage()->delete();
        }

        $feature->delete();

        session()->flash('success', 'Διαγράφηκε με επιτυχία');
    }

    public function render()
    {

        $features = $this->ship->features->sortBy('order');

        return view('cerberus::livewire.ships.ship-features', compact('features'));
    }
}
