<?php

namespace Modules\Cerberus\Livewire\Ships;

use App\Models\Ship;
use Livewire\Component;
use Illuminate\Support\Facades\Storage;

class Index extends Component
{

    
    public function togglePublished(Ship $ship)
    {
        $ship->update(['published' => !$ship->published]);
    }

    public function toggleFeatured(Ship $ship)
    {
        $ship->update(['featured' => !$ship->featured]);
    }

    public function deleteShip(Ship $ship)
    {
          // if the ship has main image, delete it and the files
          if ($ship->mainImage()) {

            // Delete file from server
            $ship->deleteMainImage();

            $ship->mainImage()->delete();
        }


        // if the ship has features with image
        if ($ship->features->count()) {
            foreach ($ship->features as $feature) {
                if ($feature->bannerImage()) {
                    Storage::delete('ships/features/' . $feature->bannerImage()->filename);
                }
            }
        }

        // Delete the ship has galleries
        if($ship->galleries->count()) {
            foreach ($ship->galleries as $gallery) {
                $gallery->images->each(function ($image) {
                    $image->delete();
                });

                Storage::deleteDirectory('galleries/' . $gallery->id);

                $gallery->delete();
            }
        }

        $ship->delete();

        session()->flash('success', 'Το πλοίο διαγράφηκε με επιτυχία'); 
    }

    public function render()
    {
        $ships = Ship::all();

        return view('cerberus::livewire.ships.index', compact('ships'));
    }
}
