<?php

namespace Modules\Cerberus\Livewire\Ships;

use App\Models\Image;
use Livewire\Component;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Storage;

class Gallery extends Component
{
    public $ship;
    public $gallery;

    public function updateOrder($items)
    {
        foreach ($items as $item) {
            Image::find($item['value'])->update(['order' => $item['order']]);
        }

        session()->flash('success', 'Order updated successfully');

    }

    public function deleteImage(Image $image){

        Storage::delete('galleries/'.$this->gallery->id.'/'.$image->filename);
        $image->delete();
        
        session()->flash('success', 'Image deleted successfully');
    }

    public function render()
    {
        
        $images = Image::where('imageable_type', 'App\Models\ImageGallery')
            ->where('imageable_id', $this->gallery->id)
            ->orderBy('order')
            ->get();

        return view('cerberus::livewire.ships.gallery', compact('images'));
    }
}
