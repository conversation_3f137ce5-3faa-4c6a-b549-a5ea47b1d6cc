<?php

namespace Modules\Cerberus\Livewire\Ships;

use App\Models\ShipFeature;
use Livewire\Component;
use Livewire\Attributes\Validate;
use Livewire\WithFileUploads;

class ShipFeatureItem extends Component
{

    use WithFileUploads;

    public $feature;
    public $locale;

    #[Validate('required', message: 'Ο τίτλος είναι απαραίτητος')]
    public $title;
    #[Validate('')]
    public $content;

    #[Validate('image')]
    public $featureImage;

    public $featureImageName;

    public function updatedFeatureImage()
{
    // Optionally process or validate the uploaded file
    $this->featureImageName = $this->featureImage->getClientOriginalName();
}

    public function updateFeature(ShipFeature $step)
    {
        $this->validate();

        $step->update([
            'title' => [
                $this->locale => $this->title
            ],
            'content' => [
                $this->locale => $this->content
            ]
        ]);



        session()->flash('success', 'Το βήμα ενημερώθηκε');
    }

    public function mount()
    {
        $this->title = $this->feature->getTranslation('title', $this->locale, false);
        $this->content = $this->feature->getTranslation('content', $this->locale, false);

    }

    public function render()
    {
        return view('cerberus::livewire.ships.ship-feature-item');
    }
}
