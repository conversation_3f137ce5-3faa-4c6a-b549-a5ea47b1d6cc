<?php

namespace Modules\Cerberus\Livewire\Ships;

use Livewire\Component;
use App\Models\ImageGallery;
use Illuminate\Support\Facades\Storage;

class Galleries extends Component
{
    public $ship;

    public function updateOrder($items)
    {
        foreach ($items as $item) {
            ImageGallery::find($item['value'])->update(['order' => $item['order']]);
        }

        session()->flash('success', 'Order updated successfully');
    }

    public function deleteGallery(ImageGallery $gallery)
    {
        $gallery->images->each(function ($image) {
            $image->delete();
        });

        Storage::deleteDirectory('galleries/' . $gallery->id);

        $gallery->delete();

        session()->flash('success', 'Gallery deleted successfully');
    }

    public function render()
    {

        $galleries = $this->ship->galleries()->orderBy('order')->get();

        return view('cerberus::livewire.ships.galleries', compact('galleries'));
    }
}
