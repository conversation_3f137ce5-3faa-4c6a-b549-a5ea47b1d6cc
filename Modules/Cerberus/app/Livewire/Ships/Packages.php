<?php

namespace Modules\Cerberus\Livewire\Ships;

use App\Models\Package;
use Livewire\Component;

class Packages extends Component
{
    public $searchTerm;
    public $ship;

    public function render()
    {
        $packages = Package::cruise()
        ->where(function ($query) {
            $query->whereRaw('LOWER(title) LIKE ?', ['%' . mb_strtolower($this->searchTerm) . '%']);
        })
        ->get();


        return view('cerberus::livewire.ships.packages', compact('packages'));
    }
}
