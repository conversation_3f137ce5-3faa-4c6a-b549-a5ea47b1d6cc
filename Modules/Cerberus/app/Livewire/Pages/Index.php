<?php

namespace Modules\Cerberus\Livewire\Pages;

use App\Models\Page;
use Livewire\Component;
use App\Models\MenuItem;

class Index extends Component
{

    public $searchTerm;

    public function deletePage(Page $page)
    {

        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $page->slug) {

                return redirect()->route('cerberus.pages.index')->with('error', 'Η σελίδα δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        $page->delete();

        session()->flash('success', 'Η σελίδα διαγράφηκε με επιτυχία');
    }

    public function render()
    {
        $pages = Page::query()
            ->whereLocale('title', app()->getLocale())
            ->where(function ($query) {
                $query->whereRaw('LOWER(title) LIKE ?', ['%' . strtolower($this->searchTerm) . '%'])
                    ->orWhereRaw('LOWER(content) LIKE ?', ['%' . strtolower($this->searchTerm) . '%']);
            })
            ->get();

        return view('cerberus::livewire.pages.index', compact('pages'));
    }
}
