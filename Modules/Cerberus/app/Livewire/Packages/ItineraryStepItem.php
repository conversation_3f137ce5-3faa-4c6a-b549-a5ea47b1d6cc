<?php

namespace Modules\Cerberus\Livewire\Packages;

use App\Models\ItineraryStep;
use Livewire\Attributes\Validate;
use Livewire\Component;

class ItineraryStepItem extends Component
{
    public $step;
    public $locale;
    
    #[Validate('required', message: 'Ο τίτλος είναι απαραίτητος')]
    public $title;
    #[Validate('')]
    public $content;

    public function updateStep(ItineraryStep $step)
    {
        $this->validate();

        $step->update([
            'title' => [
                $this->locale => $this->title
            ],
            'content' => [
                $this->locale => $this->content
            ]
        ]);

        session()->flash('success', 'Το βήμα ενημερώθηκε');

        return redirect(request()->header('Referer'));
    }

    public function mount()
    {
        $this->title = $this->step->getTranslation('title', $this->locale, false);
        $this->content = $this->step->getTranslation('content', $this->locale, false);
    }

    public function render()
    {
        return view('cerberus::livewire.packages.itinerary-step-item');
    }
}
