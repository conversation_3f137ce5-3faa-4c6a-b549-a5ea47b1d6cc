<?php

namespace Modules\Cerberus\Livewire\Packages;

use App\Models\ItineraryStep;
use Livewire\Attributes\Validate;
use Livewire\Component;

class ItinerarySteps extends Component
{
    public $package;
    public $locale;
    public $isFoodTour = false;

   

    public function updateStepsOrder($steps)
    {
        foreach ($steps as $step) {

            ItineraryStep::find($step['value'])->update(['order' => $step['order']]);
        }
    }

    public function deleteStep(ItineraryStep $step) 
    {
        $step->delete();

        session()->flash('success', 'Διαγράφηκε με επιτυχία');
    }

    public function render()
    {

        $steps = ItineraryStep::where('package_id', $this->package->id)->orderBy('order')->get();
        

        return view('cerberus::livewire.packages.itinerary-steps', compact('steps'));
    }
}
