<?php

namespace Modules\Cerberus\Livewire\Tags;

use App\Models\Tag;
use Livewire\Component;
use Livewire\Attributes\On;

class Show extends Component
{

    public $model;
    public $locale;
    public $category = false;

    public function updateTag(Tag $tag)
    {
        // Sync Tags
        if($tag->type == 'tag') {
            if ($this->model->tags->contains($tag)){
                $this->model->tags()->detach($tag);
            } else {
                $this->model->tags()->attach($tag);
            }
        } else {
            if ($this->model->categories->contains($tag)){
                $this->model->categories()->detach($tag);
            } else {
                $this->model->categories()->attach($tag);
            }
        }
       


        
        $this->dispatch('tags-updated');
    }

    #[On('tags-updated')]
    public function render()
    {
        $tags = $this->category ? Tag::category()->get() : Tag::tag()->get();

        return view('cerberus::livewire.tags.show', compact('tags'));
    }
    
}
