<?php

namespace Modules\Cerberus\Livewire\Tags;

use App\Models\Tag;
use Livewire\Component;
use App\Models\MenuItem;
use Livewire\WithPagination;
use Livewire\WithoutUrlPagination;

class Index extends Component
{
    use WithPagination, WithoutUrlPagination;

    public $searchTerm;

    public function deleteTag(Tag $tag)
    {

        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $tag->slug) {

                return redirect()->route('cerberus.tags.index')->with('error', 'Το tag δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        $tag->delete();

        session()->flash('success', 'Το Tag διαγράφηκε με επιτυχία');
    }
    
    public function render()
    {
        $tags = Tag::tag()
        ->whereLocale('title', app()->getLocale())
        ->whereRaw('LOWER(title) LIKE ?', ['%' . strtolower($this->searchTerm) . '%'])
        ->withCount(['posts', 'packages'])
        ->paginate(30);

        return view('cerberus::livewire.tags.index', compact('tags'));
    }
}
