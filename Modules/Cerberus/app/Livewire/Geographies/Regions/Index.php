<?php
namespace Modules\Cerberus\Livewire\Geographies\Regions;

use App\Models\Region;
use Livewire\Component;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Subregion;

class Index extends Component
{
    public $continent_id;
    public $subregion_id;
    public $country_id;
    public $searchTerm;

    public function render()
    {
        $continents = Continent::orderBy('name')->get();

        $subregions = Subregion::query()
            ->when($this->continent_id, function ($query) {
                $query->where('continent_id', $this->continent_id);
            })
            ->orderBy('name')
            ->get();

        $countries = Country::query()
        ->when($this->continent_id, function($query){
            $query->whereHas('continent', function($query){
                $query->where('continent_id', $this->continent_id);
            });
        })
        ->when($this->subregion_id, function($query){
            $query->where('subregion_id', $this->subregion_id);
        })
        ->orderBy('name')
        ->get();

        $regions = Region::query()
            ->where('regions.name', 'LIKE', '%'.$this->searchTerm.'%' )
            ->when($this->continent_id, function ($query) {
                $query->whereHas('continent', function ($query) {
                    $query->where('continent_id', $this->continent_id);
                });
            })
            ->when($this->subregion_id, function($query){
                $query->whereHas('subregion', function($query){
                    $query->where('subregion_id', $this->subregion_id);
                });
            })
            ->when($this->country_id, function($query){
                $query->whereHas('country', function($query){
                    $query->where('country_id', $this->country_id);
                });
            })
            ->with(['continent', 'subregion', 'country'])
            ->join('countries', 'regions.country_id', '=', 'countries.id')
            ->join('subregions', 'countries.subregion_id', '=', 'subregions.id')
            ->join('continents', 'subregions.continent_id', '=', 'continents.id')
            ->orderBy('continents.name')
            ->orderBy('subregions.name')
            ->orderBy('countries.name')
            ->orderBy('regions.name')
            ->select('regions.*')
            ->paginate(100);

        return view('cerberus::livewire.geographies.regions.index', compact('continents', 'subregions', 'regions', 'countries'));
    }
}
