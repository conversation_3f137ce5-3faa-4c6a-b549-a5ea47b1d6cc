<?php
namespace Modules\Cerberus\Livewire\Geographies\Aliases;

use App\Models\Alias;
use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Region;
use App\Models\Subregion;
use Livewire\Component;

class Index extends Component
{

    public $type;
    public $aliasable_types = [];
    public $searchTerm;

    public function mount()
    {
        $this->aliasable_types = [
            'App\Models\Continent' => 'Continent',
            'App\Models\Subregion' => 'Subregion',
            'App\Models\Country' => 'Country',
            'App\Models\Region' => 'Region',
            'App\Models\City' => 'City',
        ];
    }

    public function render()
    {
        $continents = Continent::orderBy('name')->get();
      
        $aliases = Alias::query()
            ->when($this->type, function ($query)
            {
                $query->where('aliasable_type', $this->type);
            })
            ->whereHasMorph('aliasable', 
                [Continent::class, Subregion::class, Country::class, Region::class, City::class],
                function ($query)
            {
                $query->where('name', 'LIKE', '%'.$this->searchTerm.'%');
            })
            ->paginate(100);

        return view('cerberus::livewire.geographies.aliases.index', compact('aliases'));
    }

   
}
