<?php

namespace Modules\Cerberus\Livewire\Geographies\Cities;

use App\Models\City;
use App\Models\Region;
use App\Models\Country;
use Livewire\Component;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Validate;

class Create extends Component
{
    #[Validate('required')]
    public $country_id;

    #[Validate('required')]
    public $region_id;

    public $name;

    public $selected_region;

    public function rules()
    {
        return [
            'name' => [
                'required',
                Rule::unique('cities', 'name')->where('region_id', $this->region_id)

            ]
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Το όνομα είναι απαραίτητο',
            'name.unique' => 'Η πόλη αυτή υπάρχει ήδη'
        ];
    }

    public function createCity()
    {
        $this->validate();

        $city = City::create([
            'name' => $this->name,
            'region_id' => $this->region_id
        ]);

        return redirect()->route('cerberus.cities.edit', $city);
    }

    public function render()
    {
        $countries = Country::orderBy('name')->get();

        $regions = Region::with('cities')
            ->where('country_id', $this->country_id)
            ->orderBy('name')
            ->get();

        if($this->region_id) {
            $this->selected_region = Region::find($this->region_id);
        }

        $selected_region = $this->selected_region;

        return view('cerberus::livewire.geographies.cities.create', compact('countries', 'regions', 'selected_region'));
    }
}
