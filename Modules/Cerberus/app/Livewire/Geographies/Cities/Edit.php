<?php
namespace Modules\Cerberus\Livewire\Geographies\Cities;

use App\Models\Country;
use App\Models\Region;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Validate;
use Livewire\Component;

class Edit extends Component
{
    public $city;

    public $country_id;

    public $region_id;

    public $name;

    public $selected_region;

    protected function rules()
    {
        return [
            'region_id'  => 'required',
            'country_id' => 'required',
            'name'       => [
                'required',
                Rule::unique('cities', 'name')
                    ->where('region_id', $this->region_id)
                    ->ignore($this->city->id),
            ],

        ];
    }

    protected function messages()
    {
        return [
            'name.required' => 'Το όνομα είναι απαραίτητο',
            'name.unique'   => 'Η πόλη αυτή υπάρχει ήδη',
            'country_id.required' => 'Πρέπει να επιλέξετε χώρα',
            'region_id.required' => 'Πρέπει να επιλέξετε region',
        ];
    }

    public function updateCity()
    {
        $this->validate();

        $this->city->update([
            'region_id' =>$this->region_id,
            'name' => $this->name,
        ]);

        return redirect()->route('cerberus.cities.edit', $this->city)->with('success', 'Ενημερώθηκε με επιτυχία');
    }

    public function mount()
    {
        $this->country_id = $this->city->country->id;

        $this->region_id = $this->city->region->id;

        $this->name = $this->city->name;
    }

    public function render()
    {
        $countries = Country::orderBy('name')->get();

        $regions = Region::with('cities')
            ->where('country_id', $this->country_id)
            ->orderBy('name')
            ->get();

        if ($this->region_id) {
            $this->selected_region = Region::find($this->region_id);
        }

        $selected_region = $this->selected_region;

        return view('cerberus::livewire.geographies.cities.edit', compact('countries', 'regions', 'selected_region'));
    }
}
