<?php
namespace Modules\Cerberus\Livewire\Geographies\Cities;

use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Region;
use App\Models\Subregion;
use Livewire\Component;

class Index extends Component
{
    public $continent_id;
    public $subregion_id;
    public $country_id;
    public $region_id;
    public $searchTerm;

    public function render()
    {
        $continents = Continent::orderBy('name')->get();

        $subregions = $this->getSubregions();

        $countries = $this->getCountries();

        $regions = $this->getRegions();

        $cities = $this->getCities();

        return view('cerberus::livewire.geographies.cities.index', compact('continents', 'subregions', 'countries', 'regions', 'cities'));
    }

    public function getSubregions()
    {
        return Subregion::query()
            ->when($this->continent_id, function ($query) {
                $query->where('continent_id', $this->continent_id);
            })
            ->orderBy('name')
            ->get();
    }

    public function getCountries()
    {
        return Country::query()
            ->when($this->continent_id, function ($query) {
                $query->whereHas('continent', function ($query) {
                    $query->where('continent_id', $this->continent_id);
                });
            })
            ->when($this->subregion_id, function ($query) {
                $query->where('subregion_id', $this->subregion_id);
            })
            ->orderBy('name')
            ->get();
    }

    public function getRegions()
    {
        return Region::query()
            ->where('regions.name', 'LIKE', '%' . $this->searchTerm . '%')
            ->when($this->continent_id, function ($query) {
                $query->whereHas('continent', function ($query) {
                    $query->where('continent_id', $this->continent_id);
                });
            })
            ->when($this->subregion_id, function ($query) {
                $query->whereHas('subregion', function ($query) {
                    $query->where('subregion_id', $this->subregion_id);
                });
            })
            ->when($this->country_id, function ($query) {
                $query->whereHas('country', function ($query) {
                    $query->where('country_id', $this->country_id);
                });
            })
            ->orderBy('name')
            ->get();
    }
    

    public function getCities()
    {
        return City::query()
            ->where('cities.name', 'LIKE', '%'.$this->searchTerm.'%' )
            ->when($this->continent_id, function ($query) {
                $query->whereHas('continent', function ($query) {
                    $query->where('continent_id', $this->continent_id);
                });
            })
            ->when($this->subregion_id, function ($query) {
                $query->whereHas('subregion', function ($query) {
                    $query->where('subregion_id', $this->subregion_id);
                });
            })
            ->when($this->country_id, function ($query)
            {
                $query->whereHas('country', function ($query)
                {
                    $query->where('country_id', $this->country_id);
                });
            })
            ->when($this->region_id, function ($query)
            {
                $query->whereHas('region', function ($query)
                {
                    $query->where('region_id', $this->region_id);
                });
            })
            ->with(['continent', 'subregion', 'country', 'region'])
            ->join('regions', 'cities.region_id', '=', 'regions.id')
            ->join('countries', 'regions.country_id', '=', 'countries.id')
            ->join('subregions', 'countries.subregion_id', '=', 'subregions.id')
            ->join('continents', 'subregions.continent_id', '=', 'continents.id')
            ->orderBy('continents.name')
            ->orderBy('subregions.name')
            ->orderBy('countries.name')
            ->orderBy('regions.name')
            ->orderBy('cities.name')
            ->select('cities.*')
            ->paginate(100);
    }

}
