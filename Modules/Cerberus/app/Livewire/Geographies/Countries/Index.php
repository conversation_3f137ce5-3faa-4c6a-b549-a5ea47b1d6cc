<?php
namespace Modules\Cerberus\Livewire\Geographies\Countries;

use App\Models\Continent;
use App\Models\Country;
use App\Models\Subregion;
use Livewire\Component;

class Index extends Component
{
    public $continent_id;
    public $subregion_id;
    public $searchTerm;
    public $sortField     = 'name';
    public $sortDirection = 'asc';

    public function sortBy($field)
    {
        if ($this->sortField == $field) {
            $this->sortDirection = $this->sortDirection == 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        $continents = Continent::orderBy('name')->get();

        $subregions = Subregion::query()
            ->when($this->continent_id, function ($query) {
                $query->where('continent_id', $this->continent_id);
            })
            ->orderBy('name')
            ->get();

        $countries = Country::query()
            ->where(function ($query) {
                $query->where('name', 'LIKE', '%' . $this->searchTerm . '%')
                    ->orWhere('iso_code', 'LIKE', '%' . $this->searchTerm . '%');
            })
            ->when($this->continent_id, function ($query) {
                $query->whereHas('subregion', function ($query) {
                    $query->where('continent_id', $this->continent_id);
                });
            })
            ->when($this->subregion_id, function ($query) {
                $query->whereHas('subregion', function ($query) {
                    $query->where('id', $this->subregion_id);
                });
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(100);

        return view('cerberus::livewire.geographies.countries.index', compact('continents', 'subregions', 'countries'));
    }
}
