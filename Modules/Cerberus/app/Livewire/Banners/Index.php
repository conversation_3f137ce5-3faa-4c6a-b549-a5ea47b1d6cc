<?php

namespace Modules\Cerberus\Livewire\Banners;

use App\Models\Image;
use App\Models\Banner;
use Livewire\Component;

class Index extends Component
{

    public $type;

    public function updateOrder($items)
    {
        foreach ($items as $item) {
            Image::find($item['value'])->update(['order' => $item['order']]);
        }
    }


    public function render()
    {
        $bannerImages = Image::where('imageable_type', 'App\Models\Banner')
            ->whereHasMorph('imageable', [Banner::class], function($query){
                $query->where('type', $this->type);
            } )
            ->orderBy('order')
            ->get();


        return view('cerberus::livewire.banners.index', compact('bannerImages'));
    }
}
