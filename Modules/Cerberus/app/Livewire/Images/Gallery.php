<?php

namespace Modules\Cerberus\Livewire\Images;

use App\Models\Image;
use Livewire\Component;
use Illuminate\Support\Facades\Storage;

class Gallery extends Component
{

    public $foodtour;
    public $gallery;

    public function updateOrder($images)
    {
        foreach ($images as $image) {
            Image::find($image['value'])->update(['order' => $image['order']]);
        }
    }

    public function deleteImage($image)
    {
        $image = Image::find($image);
        
        Storage::delete(['galleries/'.$this->gallery->id.'/'.$image->filename]);

        $image->delete();

        session()->flash('success','Η εικόνα διαγράφηκε');
    }

    public function deleteGallery()
    {
        $this->gallery->images->each(function ($image) {
            $image->delete();
        });

        Storage::deleteDirectory('galleries/' . $this->gallery->id);        

        session()->flash('success','Όλες οι εικόνες διαγράφηκαν');
    }

    public function mount()
    {
        $this->gallery = $this->foodtour->latestGallery;
    }

    public function render()
    {

        $images = $this->foodtour->latestGallery?->images->sortBy('order');

        return view('cerberus::livewire.images.gallery', compact('images'));
    }
}
