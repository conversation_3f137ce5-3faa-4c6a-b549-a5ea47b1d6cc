<?php
namespace Modules\Cerberus\Livewire\Seo;

use App\Models\Seoterm;
use App\Seo\AnalysisService;
use Illuminate\Support\Str;
use Livewire\Component;

class Checker extends Component
{

    protected AnalysisService $analysis;

    // #[Validate('required', message:'Δεν έχετε επιλέξει λέξη ή φράση κλειδί')]
    public ?string $keyword = '';

    public $model;

    public int $keyLength;
    public int $keywordOccurences;
    public $keywordModels;
    public bool $keywordInTitle;
    public bool $titleStartsWithKeyword;
    public int $titleLength;
    public int $metaDescriptionLength;
    public bool $keywordInMetaDescription;
    public bool $keywordInHeadings;
    public int $wordCount;
    public bool $keywordInFirstParagraph;
    public float $density;
    public int $keywordCount;
    public bool $keywordInSlug;
    public bool $imageExists;
    public bool $imageAltExists = false;
    public bool $imageAltContainsKeyword;
    public int $inboundLinks;
    public int $outboundLinks;

    public function checkSeo()
    {

        // Create or update the keyword related to the model
        $this->model->seoTerm()->updateOrCreate([], [
            'term' => [
                'el' => $this->keyword,
            ],
        ]);

        $this->analysis = new AnalysisService;
        $this->runChecks();

    }

    public function mount()
    {
        $this->analysis = new AnalysisService;

        if ($this->model->seoTerm) {
            $this->keyword = $this->model->seoTerm->term;

        }

        $this->runChecks();

    }

    public function render()
    {
        return view('cerberus::livewire.seo.checker');
    }

    public function runChecks()
    {
        // 1. Check keyphrase length
        $this->checkLength();
        // 2. Check if keyphrase has been used again
        $this->checkIfUsed();
        // 3. Check if keyphraseis contained in title
        $this->existsInTitle();
        // 4. Check Title length
        $this->checkTitleLength();
        // 5. CHeck meta description length
        $this->checkMetadescriptionLength();
        // 6. CHeck if metadescrption contains keyword
        $this->existsInMetaDescription();
        // 7. Check if headings contain keyword
        $this->existsInHeadings();
        // 8. Check if content has 300 words and more
        $this->checkWordCount();
        // 9. Check if 1st paragraph contains keyword
        $this->existsInFirstParagraph();
        // 10. Check keyword density
        $this->checkKeywordDensity();
        // 11. Check if slug contains keyword
        $this->existsInSlug();
        // 12. Check if image exists
        $this->checkForImage();
        // 13. Check image alt
        $this->checkForAlt();
        // 14. Check for links
        $this->checkLinks();

    }

    /**
     * Check keyword/kwyphrase length in words
     */
    public function checkLength()
    {
        $this->keyLength = $this->analysis->keyphraseLength($this->keyword);
    }

    /**
     * Check if keyword has been used again
     * Ignores capitalization
     * Does not ignore accents (tonous)
     */
    public function checkIfUsed()
    {
        if ($this->keyword) {
            $results = Seoterm::whereLocale('term', 'el')
                ->whereRaw('LOWER(term) LIKE ?', ['%' . Str::lower($this->model->seoTerm->term) . '%'])
                ->get();

            $this->keywordOccurences = $results->count();

            $this->keywordModels = $results;
        }
    }

    /**
     * 1. Checks if title contains the keyword
     * 2. Checks if the title begins with keyword
     */
    public function existsInTitle()
    {
        $title = $this->model->meta_title;

        $this->keywordInTitle = $this->analysis->checkTitleForKeyword($title, $this->keyword);

        $this->titleStartsWithKeyword = $this->analysis->checkTitleStartsWithKeyword($title, $this->keyword);
    }

    /**
     * Check that metatitle is not above 65 characters
     */
    public function checkTitleLength()
    {
        $this->titleLength = Str::length($this->model->meta_title);
    }

    /**
     * Check meta description length
     */
    public function checkMetadescriptionLength()
    {
        $this->metaDescriptionLength = Str::length($this->model->meta_description);
    }

    /**
     * Check if metadescription contains keyphrase
     */
    public function existsInMetaDescription()
    {
        $meta_desciprion = $this->model->meta_description;

        $this->keywordInMetaDescription = $this->analysis->checkMetaDescriptionForKeyword($meta_desciprion, $this->keyword);

    }

    /**
     * Check if headings contain keyword
     */
    public function existsInHeadings()
    {
        $content = $this->model->content;

        $this->keywordInHeadings = $this->analysis->checkHeadingsForKeyword($content, $this->keyword);
    }

    /**
     * Check if content is 300 words and above
     */
    public function checkWordCount()
    {
        $this->wordCount = $this->analysis->checkContentLength($this->model->content);
    }

    /**
     * Check if 1st paragraph contains keyword
     */
    public function existsInFirstParagraph()
    {
        $this->keywordInFirstParagraph = $this->analysis->checkFirstParagraphForKeyword($this->model->content, $this->keyword);
    }

    /**
     * Check keyword Density
     */
    public function checkKeywordDensity()
    {
        if($this->keyword) {
            $this->density      = $this->analysis->checkKeywordDensity($this->model->content, $this->keyword)['density'];
            $this->keywordCount = $this->analysis->checkKeywordDensity($this->model->content, $this->keyword)['keywordCount'];
        }
       

    }

    /**
     * Check slug
     */
    public function existsInSlug()
    {
        $this->keywordInSlug = $this->analysis->checkSlugForKeyword($this->model->slug, $this->keyword);
    }

    /**
     * Check for image
     */
    public function checkForImage()
    {
        $this->imageExists = $this->model->mainImage() !== null ? true : false;
    }

    /**
     * Check if image has alt and if alt contains keyword
     */
    public function checkForAlt()
    {

        if ($this->model->mainImage()?->alt) {
            $this->imageAltExists = true;

            $this->imageAltContainsKeyword = $this->analysis->checkImageAltForKeyword($this->model->mainImage()->alt, $this->keyword);

        }
    }

    /**
     * Check for inbound and outbound links
     */
    public function checkLinks()
    {
        $this->inboundLinks = $this->analysis->checkLinks($this->model->content)['internal'];

        // To check: if tag has packages then there are internal links by deafault!!!
        $this->outboundLinks = $this->analysis->checkLinks($this->model->content)['external'];
    }
}
