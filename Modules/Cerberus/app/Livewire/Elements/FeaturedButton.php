<?php

namespace Modules\Cerberus\Livewire\Elements;

use Livewire\Component;
use Livewire\Attributes\On;

class FeaturedButton extends Component
{
    public $model;

    public function featuredOn()
    {

        $this->model->update([
            'featured' => 1
        ]);
        
        $this->dispatch('featured-updated');
    }

    public function featuredOff()
    {

        $this->model->update([
            'featured' => 0
        ]);
        
        $this->dispatch('featured-updated');
    }

    #[On('featured-updated')]
    public function render()
    {
        return view('cerberus::livewire.elements.featured-button');
    }
}
