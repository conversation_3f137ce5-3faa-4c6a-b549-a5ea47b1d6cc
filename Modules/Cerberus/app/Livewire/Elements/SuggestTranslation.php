<?php

namespace Modules\Cerberus\Livewire\Elements;

use Livewire\Component;
use App\Services\TranslationService;

class SuggestTranslation extends Component
{

    public $content;
    public $id;

    public function translate()
    {

        $translatedContent = (new TranslationService)->translate($this->content);
 
        $this->dispatch('translate-'.$this->id, translation: html_entity_decode($translatedContent));
    }



    public function render()
    {
        return view('cerberus::livewire.elements.suggest-translation');
    }
}
