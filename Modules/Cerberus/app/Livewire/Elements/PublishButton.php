<?php

namespace Modules\Cerberus\Livewire\Elements;

use Livewire\Component;
use Livewire\Attributes\On;

class PublishButton extends Component
{
    public $model;


    public function publish()
    {

        $this->model->update([
            'published' => 1
        ]);
        
        $this->dispatch('status-updated');
    }

    public function unpublish()
    {

        $this->model->update([
            'published' => 0
        ]);
        
        $this->dispatch('status-updated');
    }

    #[On('status-updated')]
    public function render()
    {
        return view('cerberus::livewire.elements.publish-button');
    }
}
