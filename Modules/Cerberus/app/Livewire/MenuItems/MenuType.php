<?php

namespace Modules\Cerberus\Livewire\MenuItems;

use App\Models\Tag;
use App\Models\Page;
use App\Models\Post;
use App\Models\Package;
use Livewire\Component;

class MenuType extends Component
{

    public $menuItem;

    public $lang;

    public $type = '';
    public $link;

    public $postsShow = false;
    public $tagsShow = false;
    public $travelShow = false;
    public $travelCategoryShow = false;
    public $travelPackageShow = false;
    public $foodToursShow = false;
    public $staticPageShow = false;


    public $posts;
    public $post = null;

    public $tags;
    public $tag = null;

    public $travelPackages;
    public $travelPackage = null;

    public $travelCategories;
    public $travelCategory = null;

    public $foodTours;
    public $foodTour = null;

    public $staticPages;
    public $staticPage = null;


    public function updatedType()
    {

        match ($this->type) {
            'posts_index' => $this->typePostsIndexSelected(),
            'post' => $this->typePostSelected(),
            'tag' => $this->typeTagSelected(),
            // 'cruise_index' => $this->typeCruisesIndexSelected(),
            'travel_category_index' => $this->typeTravelCategoryIndexSelected(),
            'travel_package' => $this->typeTravelPackageSelected(),
            'food_index' => $this->typeFoodToursIndexSelected(),
            'food_show' => $this->typeFoodToursShowSelected(),
            'static_page' => $this->typeStaticPageSelected(),
            'custom' => $this->typeCustomSelected(),
        };
    }

    public function updatedPost()
    {
        $post = Post::find($this->post);

        $this->link = parse_url(route($this->lang . '.posts.show', $post->getTranslation('slug', $this->lang)))['path'];
    }

    public function updatedTag()
    {

        $tag = Tag::find($this->tag);

        $this->link = parse_url(route($this->lang . '.tag.show', $tag->getTranslation('slug', $this->lang)))['path'];
    }

    public function updatedTravelPackage()
    {
        $travelPackage = Package::find($this->travelPackage);

        $this->link = parse_url(route($this->lang . '.travel.show', ['package' => $travelPackage->slug]))['path'];
    }

    public function updatedTravelCategory()
    {
        $travelCategory = Tag::find($this->travelCategory);

        $this->link = parse_url((route($this->lang . '.category.show', $travelCategory->getTranslation('slug', $this->lang))))['path'];
    }

    public function updatedFoodTour()
    {
        $foodTour = Package::find($this->foodTour);

        $this->link = parse_url(route($this->lang.'.foodtours.show', $foodTour->getTranslation('slug', $this->lang)))['path'];
    }

    public function updatedStaticPage()
    {

        $staticPage = Page::find($this->staticPage);

        $this->link = parse_url(route($this->lang . '.pages.show', ['page' => $staticPage->getTranslation('slug', $this->lang)]))['path'];
    }

    public function mount()
    {
        $this->type = $this->menuItem->type ?? '';

        $this->link = $this->menuItem->link ?? '';


        $this->lang = $this->menuItem->menu->locale;
    }

    public function render()
    {
        return view('cerberus::livewire.menu-items.menu-type');
    }


    /**
     * What happens when user selects type Posts Index
     */
    public function typePostsIndexSelected()
    {
        $this->postsShow = false;
        $this->tagsShow = false;
        $this->travelShow = false;
        $this->travelCategoryShow = false;
        $this->travelPackageShow = false;
        $this->staticPageShow = false;

        $this->link = null;

        $this->link = parse_url(route($this->lang . '.posts.index'))['path'];
    }

    /**
     * What happens when user selects type Blog Post
     */
    public function typePostSelected()
    {
        $this->postsShow = true;
        $this->tagsShow = false;
        $this->travelShow = false;
        $this->travelCategoryShow = false;
        $this->travelPackageShow = false;
        $this->staticPageShow = false;

        $this->link = null;

        $this->posts = Post::published()
            ->whereLocale('title', $this->lang)
            ->get();
    }

    /**
     * What happens when user selects type Tags Index
     */
    public function typeTagSelected()
    {
        $this->postsShow = false;
        $this->tagsShow = true;
        $this->travelShow = false;
        $this->travelCategoryShow = false;
        $this->travelPackageShow = false;
        $this->staticPageShow = false;

        $this->link = null;

        $this->tags = Tag::tag()
            ->whereLocale('title', $this->lang)
            ->get();
    }

    /**
     * What happens when user selects type Travel Index
     */

    // public function typeCruisesIndexSelected()
    // {
    //     $this->travelShow = true;
    //     $this->postsShow = false;
    //     $this->tagsShow = false;
    //     $this->travelCategoryShow = false;
    //     $this->travelPackageShow = false;
    //     $this->staticPageShow = false;

    //     $this->link = null;

    //     $this->link = parse_url(route($this->lang . '.cruises.index'))['path'];
    // }

    /**
     * What happens when user selects type Travel Category Index
     */

    public function typeTravelCategoryIndexSelected()
    {
        $this->travelShow = false;
        $this->postsShow = false;
        $this->tagsShow = false;
        $this->travelPackageShow = false;
        $this->travelCategoryShow = true;
        $this->staticPageShow = false;

        $this->link = null;

        $this->travelCategories = Tag::category()
            ->whereLocale('title', $this->lang)
            ->get();
    }

    /**
     * What happens when user selects type Travel Package
     */

    public function typeTravelPackageSelected()
    {
        $this->postsShow = false;
        $this->tagsShow = false;
        $this->travelCategoryShow = false;
        $this->travelPackageShow = true;
        $this->staticPageShow = false;

        $this->link = null;

        $this->travelPackages = Package::travel()
            ->published()
            ->whereLocale('title', $this->lang)
            ->get();
    }

    /**
     * What happens when user selects type Food Tours Index
     */
    public function typeFoodToursIndexSelected(){
        $this->postsShow = false;
        $this->tagsShow = false;
        $this->travelCategoryShow = false;
        $this->travelPackageShow = false;
        $this->staticPageShow = false;

        $this->link = null;

        $this->link = parse_url(route($this->lang.'.food-tours.index'))['path'];

    }

    /**
     * What happens when user selects type Food Tours Show
     */
    public function typeFoodToursShowSelected(){
        $this->postsShow = false;
        $this->tagsShow = false;
        $this->travelCategoryShow = false;
        $this->travelPackageShow = false;
        $this->foodToursShow = true;
        $this->staticPageShow = false;

        $this->link = null;

        $this->foodTours = Package::food()
        ->published()
        ->whereLocale('title', $this->lang)
        ->get();

    }


    /**
     * What happens when user selects type Static Page
     */

    public function typeStaticPageSelected()
    {
        $this->postsShow = false;
        $this->tagsShow = false;
        $this->travelCategoryShow = false;
        $this->travelPackageShow = false;
        $this->staticPageShow = true;

        $this->link = null;

        $this->staticPages = Page::published()
            ->whereLocale('title', $this->lang)
            ->get();
    }


    /**
     * What happens when user selects type Custom
     */

    public function typeCustomSelected()
    {
        $this->postsShow = false;
        $this->tagsShow = false;
        $this->travelCategoryShow = false;
        $this->travelPackageShow = false;

        $this->link = '';
    }
}
