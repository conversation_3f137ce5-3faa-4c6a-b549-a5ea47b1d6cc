<?php

namespace Modules\Cerberus\Livewire\MenuItems;

use App\Models\MenuItem;
use Livewire\Component;

class Index extends Component
{

    public $menu;

    public function updateOrder($items)
    {
        foreach ($items as $item) {
            MenuItem::find($item['value'])->update(['order' => $item['order']]);
        }
    }

    public function render()
    {
        $menuItems = $this->menu->menuItems->sortBy('order');

        return view('cerberus::livewire.menu-items.index', compact('menuItems'));
    }
}
