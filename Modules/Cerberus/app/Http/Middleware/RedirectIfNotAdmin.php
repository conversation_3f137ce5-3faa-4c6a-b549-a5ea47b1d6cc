<?php

namespace Modules\Cerberus\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectIfNotAdmin
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // we also allow 'dev' user types apart from 'admin'
        if (! in_array(Auth::user()->type, ['admin', 'dev']))
        {
            return redirect()->route('dashboard');
        }

        return $next($request);
    }
}
