<?php

namespace Modules\Cerberus\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectIfNotDev
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // we also allow 'dev' user types apart from 'admin'
        if ( Auth::user()->type != 'dev' )
        {
            return redirect()->route('cerberus.dashboard');
        }

        return $next($request);
    }
}
