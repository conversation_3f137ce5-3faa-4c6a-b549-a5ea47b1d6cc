<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Menu;
use App\Models\MenuItem;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Modules\Cerberus\Http\Requests\MenuItemStoreRequest;
use Modules\Cerberus\Http\Requests\MenuItemUpdateRequest;

class MenuItemsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Menu $menu)
    {
        return view('cerberus::menu-items.index', compact('menu'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Menu $menu)
    {
        return view('cerberus::menu-items.create', compact('menu'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(MenuItemStoreRequest $request): RedirectResponse
    {
        $attributes = $request->validated();

        $menuItem = MenuItem::create($attributes);

        return redirect()->route('cerberus.menu-items.edit', [$menuItem->menu, $menuItem])->with('success', 'Το Menu Item δημιουργήθηκε');
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Menu $menu, MenuItem $menuItem)
    {
        $menus = Menu::all();

        return view('cerberus::menu-items.edit', compact('menu','menuItem', 'menus'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(MenuItemUpdateRequest $request, Menu $menu, MenuItem $menuItem): RedirectResponse
    {

        $attributes =  $request->validated();

        $menuItem->update($attributes);

        return redirect()->route('cerberus.menu-items.edit',[$menu, $menuItem])->with('success', 'Το Menu Item ενημερώθηκε');

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Menu $menu, MenuItem $menuItem)
    {
        $menuItem->delete();

        return redirect()->route('cerberus.menu-items.index', [$menu, $menuItem])->with('success', 'Το Menu Item διαγράφηκε');
    }
}
