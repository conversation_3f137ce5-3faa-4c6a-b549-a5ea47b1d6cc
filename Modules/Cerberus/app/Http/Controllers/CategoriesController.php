<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Faq;
use App\Models\Tag;
use App\Models\Image;
use App\Models\MenuItem;
use App\Models\Taggable;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Http\Requests\TagStoreRequest;
use Modules\Cerberus\Http\Requests\TagUpdateRequest;

class CategoriesController extends CerberusController
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::categories.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TagStoreRequest $request): RedirectResponse
    {
        $attributes = $request->validated();
        $attributes['type'] = 'category';

        $category = Tag::create($attributes);

        return redirect()->route('cerberus.categories.edit', $category)->with('success', 'Προστέθηκε νέα Κατηγορία');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Tag $category)
    {

        $faqs = Faq::orderBy('order')->get();

        return view('cerberus::categories.edit', compact('category', 'faqs'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TagUpdateRequest $request, Tag $category): RedirectResponse
    {
        $attributes = $request->validated();


        $category->update($attributes);

        if (isset($attributes['faqs'])) {

            $category->faqs()->syncWithPivotValues($attributes['faqs'], ['faqable_type' => 'App\Models\Tag']);
        } else {
            $category->faqs()->detach();
        }

        if (isset($attributes['image'])) {

            if ($category->mainImage()) {

                $category->deleteMainImage();
            }

            Image::updateOrCreate(['imageable_type' => 'App\Models\Tag', 'imageable_id' => $category->id], [
                'filename' => $attributes['image'],
                'alt' => $attributes['alt'],
                'imageable_type' => 'App\Models\Tag',
                'imageable_id' => $category->id,
                'main' => 1
            ]);
        } elseif ($category->mainImage()) {
            // If delete image is checked
            if ($request->deleteImage) {

                // Delete file from server
                $category->deleteMainImage();

                $category->mainImage()->delete();
            } else {
                // if there is an image, then update these fields

                $category->mainImage()->update([
                    'alt' => $attributes['alt'],
                    // 'caption' => $attributes['caption']
                ]);
            }
        }

        return redirect()->back()->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Tag $category)
    {

        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $category->slug) {
                return redirect()->back()->with('error', 'Η κατηγορία δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        if ($category->mainImage()) {
            $category->deleteMainImage();
        }

        $category->delete();

        return redirect()->route('cerberus.categories.index')->with('success', 'Η κατηγορία διαγράφηκε με επιτυχία');
    }
}
