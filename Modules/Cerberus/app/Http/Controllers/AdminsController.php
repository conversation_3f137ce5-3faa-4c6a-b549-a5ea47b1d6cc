<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Http\RedirectResponse;
use Modules\Cerberus\Http\Requests\AdminStoreRequest;
use Modules\Cerberus\Http\Requests\AdminUpdateRequest;
use Modules\Cerberus\Http\Traits\RedirectAfterSaveTrait;

class AdminsController extends CerberusController
{
    use RedirectAfterSaveTrait;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::admins.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::all();

        return view('cerberus::admins.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AdminStoreRequest $request): RedirectResponse
    {
        $attributes = $request->validated();

        // the user created is an admin
        // we cannot create endusers here
        $user = User::create($attributes);

        return $this->afterSave($request, 'cerberus.admins', $user)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $roles = Role::all();

        return view('cerberus::admins.edit', [
            'roles' => $roles,
            'user' => $user,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AdminUpdateRequest $request, User $user): RedirectResponse
    {
        $attributes = $request->validated();

        $user->update($attributes);

        // if the roles is not present, then the user has deselected all roles
        if($request->filled('roles'))
        {
            $user->roles()->sync($attributes['roles']);
        }
        else
        {
            $user->roles()->sync(array());
        }

        return $this->afterSave($request, 'cerberus.admins', $user)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user): RedirectResponse
    {
        $user->delete();

        return redirect()->route('cerberus.admins.index')->with('success', 'Επιτυχής διαγραφή');
    }
}
