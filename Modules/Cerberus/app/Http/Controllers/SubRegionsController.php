<?php
namespace Modules\Cerberus\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Continent;
use App\Models\Subregion;
use Modules\Cerberus\Http\Requests\SubregionStoreRequestt;
use Modules\Cerberus\Http\Requests\SubregionUpdateRequestt;

class SubRegionsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::geographies.subregions.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $continents = Continent::orderBy('name')->get();

        return view('cerberus::geographies.subregions.create', compact('continents'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SubregionStoreRequestt $request)
    {
        $attributes = $request->validated();

        $subregion = Subregion::create($attributes);

        return redirect()->route('cerberus.subregions.edit', $subregion);

    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Subregion $subregion)
    {
        $continents = Continent::orderBy('name')->get();

        return view('cerberus::geographies.subregions.edit', compact('continents', 'subregion'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(SubregionUpdateRequestt $request, Subregion $subregion)
    {
        $attributes = $request->validated();

        $subregion->update($attributes);

        return redirect()->route('cerberus.subregions.edit', $subregion)->with('success', 'Ενημερώθηκε με επιτυχία');

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Subregion $subregion)
    {
        if ($subregion->countries->count()) {
            return redirect()->back()->with('error', 'Δεν μπορείτε να διαγράψετε αυτό το Subregion. Έχει συνδεδεμένες χώρες');
        }

        $subregion->delete();
        return redirect()->route('cerberus.subregions.index')->with('success', 'Διαγράφηκε με επιτυχία');

    }
}
