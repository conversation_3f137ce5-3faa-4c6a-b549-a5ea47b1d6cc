<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Page;
use App\Models\Banner;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class HomepageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        $popupBannersCount = Banner::where('type', 'popup')->count();

        return view('cerberus::homepage.index', compact('popupBannersCount'));
    }

  
}
