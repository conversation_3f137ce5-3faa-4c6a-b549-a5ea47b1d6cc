<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Image;
use App\Models\Package;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class PricelistMigrationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function __invoke()
    {

        $packages = Package::travel()->get();

        foreach ($packages as $package) {
            
            if( $package->created_at->isBefore('2024-12-07') && $package->pricelistImage()) {
                $package->deletePricelistImage();
                $package->pricelistImage()->delete();
            }

        }

        $page = 1;
        $allPackages = collect();

        do {

            $response = Http::get('https://worldoftravel.gr/wp-json/wc/v3/products', [
                'per_page' => 100,
                'page' => $page,
                'status' => 'publish',
                // 'before'   => '2023-12-31T23:59:59',  // Posts published before December 31, 2023
                // 'after'    => '2024-09-22T00:00:00',  // Posts published after January 1, 2023
                'consumer_key' => 'ck_c7f12566b7ea69c81a9cc48a57ace162327198c8',
                'consumer_secret' => 'cs_209e7732da89f8c2560984f9cebf4c77d10ec8cf'
            ]);

            if ($response->successful()) {

                $packages = collect($response->json())->sortBy('date_created');
                $allPackages = $allPackages->merge($packages);

                $page++;  // Increment page for next request
            } else {
                Log::error('Invalid JSON received: ' . json_last_error_msg());
            }
        } while ($page < 4);

          /**
         * Step 4
         * 2nd loop. Store pricelists
         */
        foreach ($allPackages as $key => $old_package) {


            /**
             * Get the description and store the information needed in variables
             */
            $old_description = $old_package['description'];

            $sku = null;
            $pricelist = null;
            $duration = null;
            $subtitle = null;


            // 4. Extract link for "ΕΔΩ" (case-insensitive). This is the pricelist image
            if (preg_match('/<a[^>]*href=["\']([^"\']*)["\'][^>]*>\s*ΕΔΩ\s*<\/a>/ui', $old_description, $matches)) {
                $pricelist = $matches[1];
            }

            $package = Package::where('slug->el', $old_package['slug'])->first();

            /** 
             * UPLOAD PRICELIST
             * Get the filename to store. Takes the initial filename and removes the first sections
             * i.e. if filename is 2024/10/diimeri-ekdromi-konta-stin-athina-mainalo-oreini-korinthia-nayplio.jpg, remove 2024/10/
             **/

            if ($package) {


                if ($pricelist != '') {

                    $pricelist_filename = Arr::last(explode('/', $pricelist));

                    // Download and save image in a temp folder 'old_uploads'
                    $contents = file_get_contents($pricelist);

                    // Check if this is an image file
                    if (getimagesize($pricelist)) {

                        Storage::put('old_uploads/' . $pricelist_filename, $contents);

                        // Get the file from the temp folder and upload it
                        $uploaded_pricelist = new UploadedFile('storage/old_uploads/' . $pricelist_filename, $pricelist_filename);

                        // Store the file
                        $new_pricelist = Image::updateOrCreate(['imageable_id' => $package->id, 'imageable_type' => 'App\Models\Package', 'pricelist' => 1], [
                            'filename' => $uploaded_pricelist,
                            'alt' => 'Pricelist',
                            'imageable_id' => $package->id,
                            'imageable_type' => 'App\Models\Package',
                            'pricelist' => 1,
                        ]);
                    }
                }
            }
        }


        Storage::deleteDirectory('old_uploads');

        return response()->json(['message' => 'Pricelists migrated successfully'], 200);
    }

  
}
