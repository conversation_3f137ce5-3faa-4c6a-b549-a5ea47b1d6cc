<?php

namespace Modules\Cerberus\Http\Controllers;

use <PERSON>tie\Permission\Models\Role;
use Illuminate\Http\RedirectResponse;
use Spatie\Permission\Models\Permission;
use Modules\Cerberus\Http\Requests\RoleStoreRequest;
use Modules\Cerberus\Http\Requests\RoleUpdateRequest;
use Modules\Cerberus\Http\Traits\RedirectAfterSaveTrait;

class RolesController extends CerberusController
{
    use RedirectAfterSaveTrait;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::roles.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $permissions = Permission::all();

        return view('cerberus::roles.create', compact('permissions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(RoleStoreRequest $request): RedirectResponse
    {
        $attributes = $request->validated();

        $role = Role::create($attributes);

    return $this->afterSave($request, 'cerberus.roles', $role)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Role $role)
    {
        $permissions = Permission::all();

        return view('cerberus::roles.edit', compact('role', 'permissions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(RoleUpdateRequest $request, Role $role): RedirectResponse
    {
        $attributes = $request->validated();

        $role->update($attributes);

        // if the permissions is not present, then the user has deselected all permissions
        if($request->filled('permissions'))
        {
            $role->permissions()->sync($attributes['permissions']);
        }
        else
        {
            $role->permissions()->sync(array());
        }

        return $this->afterSave($request, 'cerberus.roles', $role)->with('success', 'Ενημερώθηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Role $role): RedirectResponse
    {
        $role->delete();

        return redirect()->route('cerberus.roles.index')->with('success', 'Επιτυχής διαγραφή');
    }
}
