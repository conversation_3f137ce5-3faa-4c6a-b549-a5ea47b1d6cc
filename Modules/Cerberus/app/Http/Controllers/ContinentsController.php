<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Continent;
use Illuminate\Http\Request;
use Modules\Cerberus\Http\Requests\ContinentStoreRequest;

class ContinentsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $continents = Continent::all();

        return view('cerberus::geographies.continents.index', compact('continents'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::geographies.continents.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ContinentStoreRequest $request)
    {
        $attributes = $request->validated();

        $continent = Continent::create($attributes);

        return redirect()->route('cerberus.continents.edit', $continent)->with('success', 'Προστέθηκε με επιτυχία');
    }

    /**
     * Show the specified resource.
     */
    public function show(Continent $continent)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Continent $continent)
    {
        return view('cerberus::geographies.continents.edit', compact('continent'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ContinentStoreRequest $request, Continent $continent)
    {
        $attributes = $request->validated();

        $continent->update($attributes);

        return redirect()->route('cerberus.continents.edit', $continent)->with('success', 'Ενημερώθηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Continent $continent)
    {
        if($continent->subregions->count()) {
           return back()->with('error', 'Δεν μπορεί να διαγραφεί. Περιέχει subregions');
        }
        $continent->delete();

        return redirect()->route('cerberus.continents.index')->with('success', 'Διαγράφηκε με επιτυχία');
    }
}
