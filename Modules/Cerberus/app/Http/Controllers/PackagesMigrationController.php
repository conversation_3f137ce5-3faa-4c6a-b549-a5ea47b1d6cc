<?php

namespace Modules\Cerberus\Http\Controllers;

use Carbon\Carbon;
use App\Models\Image;
use App\Models\Package;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Models\DepartureDate;
use App\Models\ItineraryStep;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class PackagesMigrationController extends Controller
{
    public function __invoke()
    {
        /**
         * Step 1: Get all posts
         */

        $page = 1;
        $allPackages = collect();

        do {

            $response = Http::get('https://worldoftravel.gr/wp-json/wc/v3/products', [
                'per_page' => 100,
                'page' => $page,
                'status' => 'publish',
                // 'before'   => '2023-12-31T23:59:59',  // Posts published before December 31, 2023
                // 'after'    => '2024-09-22T00:00:00',  // Posts published after January 1, 2023
                'consumer_key' => 'ck_c7f12566b7ea69c81a9cc48a57ace162327198c8',
                'consumer_secret' => 'cs_209e7732da89f8c2560984f9cebf4c77d10ec8cf'
            ]);

            if ($response->successful()) {

                $packages = collect($response->json())->sortBy('date_created');
                $allPackages = $allPackages->merge($packages);

                $page++;  // Increment page for next request
            } else {
                Log::error('Invalid JSON received: ' . json_last_error_msg());
            }
        } while ($page < 4);


        /**
         * Step 2
         * 1st loop store packages data
         */
        foreach ($allPackages as $key => $old_package) {


            // Check if there is a soft deleted package with the same slug and permanently delete it
            if(Package::onlyTrashed()->where('slug->el', $old_package['slug'])->first()){
                Package::onlyTrashed()->where('slug->el', $old_package['slug'])->first()->forceDelete();
            }

            /**
             * Get the description and store the information needed in variables
             */
            $old_description = $old_package['description'];

            $pattern = '#<h3>Δείτε τον τιμοκατάλογο.*?</h3>#s';


            // Get the package description
            $package_contnet = preg_replace($pattern, '', $old_description);


            $sku = null;
            $pricelist = null;
            $duration = null;
            $subtitle = null;

            // 1. Extract "Κωδικός ταξιδιού" code (case-insensitive)
            if (preg_match('/Κωδικός\s+ταξιδιού:\s*([\d-]+)/ui', $old_description, $matches)) {
                $sku = $matches[1];
            }

            // 2. Extract "ΗΜΕΡΕΣ" duration (case-insensitive)
            if (preg_match('/(\d+)\s*ΗΜΕΡΕΣ/ui', $old_description, $matches)) {
                $duration = $matches[1];
            }

            // 3. Extract subtitle (case-insensitive)
            if (preg_match('/<h3>([^<]+?)-<br \/>/ui', $old_description, $matches)) {
                $subtitle = trim($matches[1]);
            }

            // 4. Extract link for "ΕΔΩ" (case-insensitive). This is the pricelist image
            if (preg_match('/<a[^>]*href=["\']([^"\']*)["\'][^>]*>\s*ΕΔΩ\s*<\/a>/ui', $old_description, $matches)) {
                $pricelist = $matches[1];
            }


            /**
             * Get the included and not included details and store it in respective variables
             */
            $info_text = $old_package['meta_data'][39]['value'];


            // Initialize variables
            $included = null;
            $not_included = null;

            // Extract content under "Περιλαμβάνονται"
            if (preg_match('/\[su_spoiler title="Περιλαμβάνονται"[^\]]*](.*?)\[\/su_spoiler]/uis', $info_text, $matches)) {
                $included = trim($matches[1]);
                $info_text = str_replace($included, '', $info_text);
            }

            // Extract content under "Δεν περιλαμβάνονται"
            if (preg_match('/\[su_spoiler title="Δεν περιλαμβάνονται"[^\]]*](.*?)\[\/su_spoiler]/uis', $info_text, $matches)) {
                $not_included = trim($matches[1]);
                $info_text = str_replace($not_included, '', $info_text);
            }

            // Remove empty su_spoiler tags
            $info_text = preg_replace('/\[su_spoiler.*?]\s*\[\/su_spoiler]/is', '', $info_text);

            // Remove su_accordion if it becomes empty after removing su_spoiler tags
            $info_text = preg_replace('/\[su_accordion\s*]\s*\[\/su_accordion]/is', '', $info_text);

            // Trim the final text to clean up extra whitespace
            $info_text = trim($info_text);

            // Meta tags
            $meta_title = isset($old_package['yoast_head_json']['title']) ? $old_package['yoast_head_json']['title'] : null ;
            $meta_description = isset($old_package['yoast_head_json']['og_description']) ? $old_package['yoast_head_json']['og_description'] : null ;

            // Store the package
            $package = Package::updateOrCreate(['slug->el' => $old_package['slug']], [
                'type' => 'travel',
                'published' => 1,
                'title' => [
                    'el' => $old_package['name'],
                ],
                'slug' => [
                    'el' => $old_package['slug'],
                ],
                'subtitle' => [
                    'el' => $subtitle,
                ],
                'content' => $package_contnet,
                'sku' => $sku,
                'duration' => $duration,
                'price' => $old_package['price'],
                'included' => [
                    'el' => $included,
                ],
                'not_included' => [
                    'el' => $not_included,
                ],
                'info' => [
                    'el' => $info_text,
                ],
                'meta_title' => [
                    'el' => $meta_title
                ],
                'meta_description' => [
                    'el' => $meta_description
                ],
                'created_at' => $old_package['date_created'],
            ]);


            /**
             * CREATE ITINERARY STEPS
             * Get the itinerary steps. They are stored in $package -> meta_data -> value
             * We take them, strip the needed text and store them as Itnerary Steps
             */


            if ($old_package['meta_data'][37]['value']) {
                $text = $old_package['meta_data'][37]['value'];

                // Regular expression to match titles and content within each [su_spoiler] tag
                preg_match_all('/\[su_spoiler title="(.+?)".*?\](.*?)\[\/su_spoiler\]/s', $text, $matches);


                foreach ($matches[1] as $index => $title) {
                    $content = trim($matches[2][$index]);
                    $result[] = [
                        'title' => $title,
                        'content' => $content
                    ];

                    ItineraryStep::updateOrCreate([
                        'package_id' => $package->id,
                        'title->el' => $title
                    ], [
                        'package_id' => $package->id,
                        'title' => [
                            'el' => $title
                        ],
                        'content' => [
                            'el' => $content
                        ],
                    ]);
                }
            }
        }

        

        /**
         * Step 3
         * 2nd loop. Store main images
         */
        foreach ($allPackages as $key => $old_package) {


            /**
             * Get the description and store the information needed in variables
             */
            // $old_description = $old_package['description'];

            $sku = null;
            $pricelist = null;
            $duration = null;
            $subtitle = null;


            // 4. Extract link for "ΕΔΩ" (case-insensitive). This is the pricelist image
            if (preg_match('/<a[^>]*href=["\']([^"\']*)["\'][^>]*>\s*ΕΔΩ\s*<\/a>/ui', $old_description, $matches)) {
                $pricelist = $matches[1];
            }



            $package = Package::where('slug->el', $old_package['slug'])->first();



            if (!$package->mainImage()) {

                /**
                 * UPLOAD MAIN IMAGE
                 */

                // $image = Http::get('https://worldoftravel.gr/wp-json/wp/v2/media/' . $old_package['images'][0]['id'])->json();

                $image_url =  $old_package['images'][0]['src'];

                /** 
                 * Get the filename to store. Takes the initial filename and removes the first sections
                 * i.e. if filename is 2024/10/diimeri-ekdromi-konta-stin-athina-mainalo-oreini-korinthia-nayplio.jpg, remove 2024/10/
                 **/

                $filename = Arr::last(explode('/', $image_url));


                // Download and save image in a temp folder 'old_uploads'
                $contents = file_get_contents($image_url);

                Storage::put('old_uploads/' . $filename, $contents);

                // Get the alt text
                $alt_text = $old_package['images'][0]['alt'];

                // Get the file from the temp folder and upload it
                $uploaded_file = new UploadedFile('storage/old_uploads/' . $filename, $filename);

                // Store the file
                $new_image = Image::updateOrCreate(['imageable_id' => $package->id, 'imageable_type' => 'App\Models\Package', 'main' => 1], [
                    'filename' => $uploaded_file,
                    'alt' => $alt_text,
                    'imageable_id' => $package->id,
                    'imageable_type' => 'App\Models\Package',
                    'main' => 1,
                ]);
            }
        }

        /**
         * Step 4
         * 2nd loop. Store pricelists
         */
        foreach ($allPackages as $key => $old_package) {


            /**
             * Get the description and store the information needed in variables
             */
            // $old_description = $old_package['description'];

            $sku = null;
            $pricelist = null;
            $duration = null;
            $subtitle = null;


            // 4. Extract link for "ΕΔΩ" (case-insensitive). This is the pricelist image
            if (preg_match('/<a[^>]*href=["\']([^"\']*)["\'][^>]*>\s*ΕΔΩ\s*<\/a>/ui', $old_description, $matches)) {
                $pricelist = $matches[1];
            }

            $package = Package::where('slug->el', $old_package['slug'])->first();

            /** 
             * UPLOAD PRICELIST
             * Get the filename to store. Takes the initial filename and removes the first sections
             * i.e. if filename is 2024/10/diimeri-ekdromi-konta-stin-athina-mainalo-oreini-korinthia-nayplio.jpg, remove 2024/10/
             **/

            if (!$package->pricelistImage()) {


                if ($pricelist != '') {

                    $pricelist_filename = Arr::last(explode('/', $pricelist));

                    // Download and save image in a temp folder 'old_uploads'
                    $contents = file_get_contents($pricelist);

                    // Check if this is an image file
                    if (getimagesize($pricelist)) {

                        Storage::put('old_uploads/' . $pricelist_filename, $contents);

                        // Get the file from the temp folder and upload it
                        $uploaded_pricelist = new UploadedFile('storage/old_uploads/' . $pricelist_filename, $pricelist_filename);

                        // Store the file
                        $new_pricelist = Image::updateOrCreate(['imageable_id' => $package->id, 'imageable_type' => 'App\Models\Package', 'pricelist' => 1], [
                            'filename' => $uploaded_pricelist,
                            'alt' => 'Pricelist',
                            'imageable_id' => $package->id,
                            'imageable_type' => 'App\Models\Package',
                            'pricelist' => 1,
                        ]);
                    }
                }
            }
        }


        Storage::deleteDirectory('old_uploads');

        return response()->json(['message' => 'Packages migrated successfully'], 200);
    }
}
