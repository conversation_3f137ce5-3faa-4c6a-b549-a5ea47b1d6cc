<?php

namespace Modules\Cerberus\Http\Controllers;

use Illuminate\View\View;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\RedirectResponse;
use Modules\Cerberus\Http\Requests\PermissionStoreRequest;
use Modules\Cerberus\Http\Requests\PermissionUpdateRequest;
use Modules\Cerberus\Http\Traits\RedirectAfterSaveTrait;

class PermissionsController extends CerberusController
{
    use RedirectAfterSaveTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        return view('cerberus::permissions.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('cerberus::permissions.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PermissionStoreRequest $request): RedirectResponse
    {
        $permission = Permission::create([
            'name' => $request['name'],
            'module' => 'Cerberus',
        ]);

    return $this->afterSave($request, 'cerberus.permissions', $permission)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Permission $permission): View
    {
        return view('cerberus::permissions.edit', compact('permission'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PermissionUpdateRequest $request, Permission $permission): RedirectResponse
    {
        $attributes = $request->validated();

        $permission->update($attributes);

        return $this->afterSave($request, 'cerberus.permissions', $permission)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Permission $permission): RedirectResponse
    {
        $permission->delete();

        return redirect()->route('cerberus.posts.index')->with('success', 'Επιτυχής διαγραφή');
    }
}
