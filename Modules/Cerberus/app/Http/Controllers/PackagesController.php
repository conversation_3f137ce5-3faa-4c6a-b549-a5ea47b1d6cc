<?php
namespace Modules\Cerberus\Http\Controllers;

use App\Models\Image;
use App\Models\Package;
use App\Models\MenuItem;
use Illuminate\View\View;
use App\Models\DepartureDate;
use App\Models\ItineraryStep;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Exports\PackagesExport;
use Modules\Cerberus\Http\Requests\PackageStoreRequest;
use Modules\Cerberus\Http\Requests\PackageUpdateRequest;
use Modules\Cerberus\Http\Traits\RedirectAfterSaveTrait;

class PackagesController extends CerberusController
{
    use RedirectAfterSaveTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        return view('cerberus::packages.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('cerberus::packages.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PackageStoreRequest $request): RedirectResponse
    {
        $attributes = $request->validated();

        $package = Package::create($attributes);

        return redirect()->route('cerberus.packages.edit', $package)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Package $package): View
    {
        $locale = 'el';

        return view('cerberus::packages.edit', compact('package', 'locale'));
    }

    /**
     * Show the form for editing the post in English language.
     */
    public function editEn(Package $package): View
    {
        $locale = 'en';

        return view('cerberus::packages.edit-en', compact('package', 'locale'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PackageUpdateRequest $request, Package $package): RedirectResponse
    {
        $attributes = $request->validated();

        if (isset($attributes['steps'])) {
            foreach ($attributes['steps'] as $step) {

                if (isset($step['id'])) {

                    ItineraryStep::find($step['id'])->update([
                        'title'   => $step['title'],
                        'content' => $step['content'],
                    ]);
                } else {
                    ItineraryStep::create([
                        'package_id' => $package->id,
                        'title'      => $step['title'],
                        'content'    => $step['content'],
                        'order'      => ItineraryStep::where('package_id', $package->id)->max('order') + 1,
                    ]);
                }

            }

        }

        if (isset($attributes['depart_dates']) && $attributes['depart_dates'] !== '') {

            // Delete all package dates before adding updated ones
            foreach ($package->departDates as $departDate) {
                $departDate->delete();
            }

            foreach ($attributes['depart_dates'] as $depart_date) {

                if ($depart_date !== "") {
                    DepartureDate::create([
                        'package_id' => $package->id,
                        'date'       => $depart_date,
                    ]);
                }
            }
        }

        // Check if the request contains image
        if (isset($attributes['image'])) {

            //If there is already a main image delete old main image files
            if ($package->mainImage()) {

                // Delete main imaga files
                $package->deleteMainImage();
            }

            // store image to Image model
            $image = Image::updateOrCreate(['imageable_id' => $package->id, 'imageable_type' => 'App\Models\Package', 'main' => 1], [
                'filename'       => $attributes['image'],
                'alt'            => $attributes['alt'],
                // 'caption' => $attributes['caption'],
                'imageable_id'   => $package->id,
                'imageable_type' => 'App\Models\Package',
                'main'           => 1,
            ]);
        } elseif ($package->mainImage()) {

            // If delete image is checked
            if ($request->deleteImage) {

                // Delete main imaga files
                $package->deleteMainImage();

                $package->mainImage()->delete();
            } else {
                // if there is an image, then update these fields

                $mainImage             = $package->mainImage();
                $mainImage->timestamps = false;
                $mainImage->alt        = $attributes['alt'];
                $mainImage->save();
            }
        }

        // Check if the request contains pricelist
        if (isset($attributes['pricelist'])) {

            //If there is already a pricelist image delete old main image files
            if ($package->pricelistImage()) {

                // Delete pricelist files from disk
                $package->deletePricelistImage();
            }

            // store image to Image model
            $pricelist = Image::updateOrCreate(['imageable_id' => $package->id, 'imageable_type' => 'App\Models\Package', 'pricelist' => 1], [
                'filename'       => $attributes['pricelist'],
                'alt'            => null,
                'imageable_id'   => $package->id,
                'imageable_type' => 'App\Models\Package',
                'pricelist'      => 1,
            ]);
        } elseif ($package->pricelistImage()) {

            // If delete image is checked
            if ($request->deletePricelist) {

                // Delete pricelist files from disk
                $package->deletePricelistImage();

                $package->pricelistImage()->delete();
            }
        }

        $package->update($attributes);

        if ($request->locale == 'en') {
            return redirect()->route('cerberus.packages.en.edit', $package)->with('success', 'Αποθηκεύτηκε με επιτυχία');
        }

        return redirect()->route('cerberus.packages.edit', $package)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Package $package): RedirectResponse
    {

        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $package->slug) {
                return redirect()->back()->with('error', 'Το πακέτο δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        if ($package->mainImage()) {
            // Delete main imaga files
            $package->deleteMainImage();
        }

        if ($package->pricelistImage()) {
            // Delete pricelist files from disk
            $package->deletePricelistImage();
        }

        $package->delete();

        return redirect()->route('cerberus.packages.index')->with('success', 'Επιτυχής διαγραφή');
    }

    public function clearTranslation(Package $package): RedirectResponse
    {
        $attributes = [
            'title',
            'subtitle',
            'slug',
            'tagline',
            'content',
            'itinerary',
            'info',
            'included',
            'not_included',
            'meta_title',
            'meta_description',
        ];

        foreach ($attributes as $attribute) {
            $package->forgetTranslation($attribute, 'en');
        }

        $package->update($attributes);

        if ($package->mainImage()) {
            $image = $package->mainImage();

            $image->forgetAllTranslations('en');
            $image->save();
        }

        if ($package->itinerary_steps) {

            foreach ($package->itinerary_steps as $step) {

                $step->forgetAllTranslations('en');
                $step->save();
            }
        }

        return redirect()->back()->with('success', 'Η μετάφραση καταργήθηκε');
    }

    // public function export()
    // {
    //     return Excel::download(new PackagesExport, 'packages.csv', \Maatwebsite\Excel\Excel::CSV);
    // }
}
