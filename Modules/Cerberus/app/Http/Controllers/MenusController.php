<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Menu;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Modules\Cerberus\Http\Requests\MenuStoreRequest;
use Modules\Cerberus\Http\Requests\MenuUpdateRequest;

class MenusController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $menus = Menu::all();        

        return view('cerberus::menus.index', compact('menus'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::menus.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(MenuStoreRequest $request): RedirectResponse
    {
        $attributes = $request->validated();

        $menu = Menu::create($attributes);

        return redirect()->route('cerberus.menus.edit', $menu)->with('success', 'Το menu δημιουργήθηκε');
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Menu $menu)
    {
        return view('cerberus::menus.edit', compact('menu'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(MenuUpdateRequest $request, Menu $menu): RedirectResponse
    {
        $attributes = $request->validated();

        $menu->update($attributes);

        return redirect()->route('cerberus.menus.edit', $menu)->with('success', 'Το menu ενημερώθηκε');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Menu $menu)
    {
        if ($menu->menuItems->count()) {
            return redirect()->back()->with('error', 'Αυτό το μενού έχει Menu Items και δεν μπορεί να διαγραφεί');
        }
        $menu->delete();

        return redirect()->route('cerberus.menus.index');
    }
}
