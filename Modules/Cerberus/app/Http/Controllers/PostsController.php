<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Post;
use App\Models\Image;
use App\Models\MenuItem;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Http\Requests\PostStoreRequest;
use Modules\Cerberus\Http\Requests\PostUpdateRequest;
use Modules\Cerberus\Http\Traits\RedirectAfterSaveTrait;

class PostsController extends CerberusController
{
    use RedirectAfterSaveTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        return view('cerberus::posts.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('cerberus::posts.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PostStoreRequest $request): RedirectResponse
    {

        $attributes = $request->validated();

        $post = Post::create($attributes);

        return $this->afterSave($request, 'cerberus.posts', $post)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Show the form for editing the post in Greek language.
     */
    public function edit(Post $post): View
    {
        $locale = 'el';

        return view('cerberus::posts.edit', compact('post', 'locale'));
    }


    /**
     * Show the form for editing the post in English language.
     */
    public function editEn(Post $post): View
    {
        $locale = 'en';

        return view('cerberus::posts.edit-en', compact('post', 'locale'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PostUpdateRequest $request, Post $post): RedirectResponse
    {
        $attributes = $request->validated();

        // Check if the request contains image
        if (isset($attributes['image'])) {


            //If there is already a main image delete old main image files
            if ($post->mainImage()) {   
                // Delete from disk main imaga files             
                $post->deleteMainImage();               
            }

            // store image to Image model        
            $image = Image::updateOrCreate(['imageable_id' => $post->id, 'imageable_type' => 'App\Models\Post',], [
                'filename' => $attributes['image'],
                'alt' => $attributes['alt'],
                // 'caption' => $attributes['caption'],
                'imageable_id' => $post->id,
                'imageable_type' => 'App\Models\Post',
                'main' => 1,
            ]);
        } elseif ($post->mainImage()) {

            // If delete image is checked
            if ($request->deleteImage) {

                // Delete from disk main imaga files
                $post->deleteMainImage();

                // Delete from db
                $post->mainImage()->delete();
            } else {
                // if there is an image, then update these fields

                $mainImage = $post->mainImage();
                $mainImage->timestamps = false;
                $mainImage->alt = $attributes['alt'];
                $mainImage->save();
            }
        }

        $post->update($attributes);

        if ($request->locale == 'en') {
            return redirect()->route('cerberus.posts.en.edit', $post)->with('success', 'Αποθηκεύτηκε με επιτυχία');
        }

        return $this->afterSave($request, 'cerberus.posts', $post)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Post $post): RedirectResponse
    {

        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $post->slug) {
                return redirect()->back()->with('error', 'Το post δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        if($post->mainImage()) {
            $post->deleteMainImage();
        }

        $post->delete();

        return redirect()->route('cerberus.posts.index')->with('success', 'Επιτυχής διαγραφή');
    }

    public function clearTranslation(Post $post): RedirectResponse
    {
        $attributes = [
            'title',
            'slug',
            'content',
            'tagline',
            'meta_title',
            'meta_description',
        ];

        foreach ($attributes as $attribute) {
            $post->forgetTranslation($attribute, 'en');
        }

        $post->update($attributes);

        if ($post->mainImage()) {
            $image = $post->mainImage();

            $image->forgetAllTranslations('en');
            $image->save();
        }

        return redirect()->back()->with('success', 'Η μετάφραση καταργήθηκε');
    }

    /**
     * Totally Remove the specified resource from storage.
     */
    // public function forceDelete($postId)
    // {

    //     $post = Post::find($postId);

    //     if ($post->mainImage()) {
    //         foreach ($post->mainImage()->filename as $file) {
    //             Storage::delete('storage/', $file);;
    //         }
    //     }
    // }
}
