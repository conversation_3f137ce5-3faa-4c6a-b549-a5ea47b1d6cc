<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Country;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Region;
use Modules\Cerberus\Http\Requests\RegionStoreRequest;
use Modules\Cerberus\Http\Requests\RegionUpdateRequest;

class RegionsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::geographies.regions.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $countries = Country::orderBy('name')->get();

        return view('cerberus::geographies.regions.create', compact('countries'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(RegionStoreRequest $request)
    {
        $attributes = $request->validated();
        
        $region = Region::create($attributes);

        return redirect()->route('cerberus.regions.edit', $region)->with('success', 'Δημιουργήθηκε με επιτυχία');

    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Region $region)
    {

        $countries = Country::orderBy('name')->get();

        return view('cerberus::geographies.regions.edit', compact('region', 'countries'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(RegionUpdateRequest $request, Region $region)
    {
        $attributes = $request->validated();

        $region->update($attributes);

        return redirect()->route('cerberus.regions.edit', $region)->with('success', 'Ενημερώθηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Region $region)
    {
        $region->delete();

        return redirect()->route('cerberus.regions.index')->with('success', 'Διαγράφηκε με επιτυχία');
    }
}
