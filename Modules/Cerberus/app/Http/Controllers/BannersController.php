<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Image;
use App\Models\Banner;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Http\Requests\BannerStoreRequest;
use Modules\Cerberus\Http\Requests\BannerUdpateRequest;

class BannersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::banners.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::banners.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BannerStoreRequest $request)
    {
        $attributes = $request->validated();

        $banner = Banner::create([
            'url' => $attributes['url'],
            'description' => $attributes['description']
        ]);


        Image::updateOrCreate(['imageable_id' => $banner->id, 'imageable_type' => 'App\Models\Banner'], [
            'filename' => $attributes['image'],
            'alt' => $attributes['alt'],
            'imageable_id' => $banner->id,
            'imageable_type' => 'App\Models\Banner',
        ]);

        return redirect()->route('cerberus.banners.edit', $banner);
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Banner $banner)
    {

        if($banner->type !== 'banner') {
            abort(404);
        }

        return view('cerberus::banners.edit', compact('banner'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(BannerUdpateRequest $request, Banner $banner)
    {
        $attributes = $request->validated();

        $banner->update($attributes);

        if (isset($attributes['image'])) {

            if(Storage::exists('banners/'.$banner->bannerImage()->filename)) {
                Storage::delete('banners/'.$banner->bannerImage()->filename);
            }

            $banner->bannerImage()->update([
                'filename' => $attributes['image']
            ]);
        }

        $banner->bannerImage()->update([
            'alt' => $attributes['alt']
        ]);


        return redirect()->route('cerberus.banners.edit', $banner);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Banner $banner)
    {
        Storage::delete('banners/'.$banner->bannerImage()->filename);

        $banner->bannerImage()->delete();

        $banner->delete();

        return redirect()->route('cerberus.homepage.index');
    }
}
