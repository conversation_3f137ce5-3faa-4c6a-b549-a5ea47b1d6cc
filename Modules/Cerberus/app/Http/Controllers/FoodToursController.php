<?php
namespace Modules\Cerberus\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Image;
use App\Models\ImageGallery;
use App\Models\ItineraryStep;
use App\Models\MenuItem;
use App\Models\Package;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Modules\Cerberus\Http\Requests\FoodtourStoreRequest;
use Modules\Cerberus\Http\Requests\FoodToursUpdateRequest;
use Modules\Cerberus\Http\Requests\GalleryStoreRequest;
use Modules\Cerberus\Http\Traits\RedirectAfterSaveTrait;

class FoodToursController extends Controller
{
    use RedirectAfterSaveTrait;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::foodtours.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::foodtours.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FoodtourStoreRequest $request): RedirectResponse
    {
        $attributes         = $request->validated();
        $attributes['type'] = 'food';

        $foodtour = Package::create($attributes);

        return $this->afterSave($request, 'cerberus.foodtours', $foodtour)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Package $foodtour): View
    {
        $locale = 'el';

        return view('cerberus::foodtours.edit', compact('foodtour', 'locale'));
    }

    /**
     * Show the form for editing the post in English language.
     */
    public function editEn(Package $foodtour): View
    {
        $locale = 'en';

        return view('cerberus::foodtours.edit-en', compact('foodtour', 'locale'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FoodToursUpdateRequest $request, Package $foodtour): RedirectResponse
    {
        $attributes = $request->validated();

        if (isset($attributes['steps'])) {

            foreach ($attributes['steps'] as $id => $step) {

                ItineraryStep::updateOrCreate([
                    'id'         => $id,
                    'package_id' => $foodtour->id,
                ], [
                    'title'   => $step['title'],
                    'content' => $step['content'],
                ]);
            }

        }

        if (isset($attributes['tour_languages'])) {
            $attributes['tour_languages'] = implode(', ', $attributes['tour_languages']);
        }

        // Check if the request contains image
        if (isset($attributes['image'])) {

            //If there is already a main image delete old main image files
            if ($foodtour->mainImage()) {

                //Delete main image from disk
                $foodtour->deleteMainImage();
            }

            // store image to Image model
            $image = Image::updateOrCreate(['imageable_id' => $foodtour->id, 'imageable_type' => 'App\Models\Package', 'main' => 1], [
                'filename'       => $attributes['image'],
                'alt'            => $attributes['alt'],
                // 'caption' => $attributes['caption'],
                'imageable_id'   => $foodtour->id,
                'imageable_type' => 'App\Models\Package',
                'main'           => 1,
            ]);
        } elseif ($foodtour->mainImage()) {

            // If delete image is checked
            if ($request->deleteImage) {

                //Delete main image from disk
                $foodtour->deleteMainImage();

                $foodtour->mainImage()->delete();
            } else {
                // if there is an image, then update these fields

                $mainImage             = $foodtour->mainImage();
                $mainImage->timestamps = false;
                $mainImage->alt        = $attributes['alt'];
                $mainImage->save();
            }
        }

        $foodtour->update($attributes);

        if ($request->locale == 'en') {
            return redirect()->route('cerberus.foodtours.en.edit', $foodtour)->with('success', 'Αποθηκεύτηκε με επιτυχία');
        }

        return $this->afterSave($request, 'cerberus.foodtours', $foodtour)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Package $foodtour): RedirectResponse
    {

        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $foodtour->slug) {
                return redirect()->back()->with('error', 'Το πακέτο δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        if ($foodtour->mainImage()) {

            //Delete main image from disk
            $foodtour->deleteMainImage();
        }

        if ($foodtour->galleryImages()->count()) {
            foreach ($foodtour->galleryImages as $image) {
                Storage::delete($image->originalImage());
            }
        }

        $foodtour->delete();

        return redirect()->route('cerberus.foodtours.index')->with('success', 'Επιτυχής διαγραφή');
    }

    public function clearTranslation(Package $foodtour): RedirectResponse
    {
        $attributes = [
            'title',
            'subtitle',
            'slug',
            'tagline',
            'content',
            'itinerary',
            'info',
            'included',
            'not_included',
            'meta_title',
            'meta_description',
        ];

        foreach ($attributes as $attribute) {
            $foodtour->forgetTranslation($attribute, 'en');
        }

        $foodtour->update($attributes);

        if ($foodtour->mainImage()) {
            $image = $foodtour->mainImage();

            $image->forgetAllTranslations('en');
            $image->save();
        }

        if ($foodtour->itinerary_steps) {

            foreach ($foodtour->itinerary_steps as $step) {

                $step->forgetAllTranslations('en');
                $step->save();
            }
        }

        return redirect()->back()->with('success', 'Η μετάφραση καταργήθηκε');
    }

    public function gallery(Package $foodtour)
    {
        return view('cerberus::foodtours.gallery', compact('foodtour'));
    }

    public function storeGallery(GalleryStoreRequest $request, Package $foodtour)
    {
        $attributes = $request->validated();

        $imageGallery = ImageGallery::updateOrCreate([
            'galleriable_type' => 'App\Models\Package',
            'galleriable_id'   => $foodtour->id,
        ], [
            'title'            => [
                'el' => $foodtour->getTranslation('title', 'el'),
                'en' => $foodtour->getTranslation('title', 'en'),
            ],
            'galleriable_type' => 'App\Models\Package',
            'galleriable_id'   => $foodtour->id,
            'published'        => 1,
        ]);

        $order = 1;

        foreach ($attributes['images'] as $image) {

            Image::create([
                'imageable_type' => 'App\Models\ImageGallery',
                'imageable_id'   => $imageGallery->id,
                'filename'       => $image,
                'alt'            => '',
                'order'          => $order++,
            ]);
        }

        return redirect()->back()->with('success', 'Οι φωτογραφίες ανέβηκαν');
    }
}
