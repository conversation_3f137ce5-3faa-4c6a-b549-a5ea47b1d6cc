<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Faq;
use App\Models\Tag;
use App\Models\Image;
use App\Models\MenuItem;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Http\Requests\TagStoreRequest;
use Modules\Cerberus\Http\Requests\TagUpdateRequest;

class TagsController extends CerberusController
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::tags.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::tags.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(TagStoreRequest $request): RedirectResponse
    {
        $attributes = $request->validated();
        $attributes['type'] = 'tag';

        $tag = Tag::create($attributes);

        return redirect()->route('cerberus.tags.edit', $tag)->with('success', 'Προστέθηκε νέο Tag');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Tag $tag)
    {
        $faqs = Faq::orderBy('order')->get();

        return view('cerberus::tags.edit', compact('tag', 'faqs'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TagUpdateRequest $request, Tag $tag): RedirectResponse
    {

        $attributes = $request->validated();

        $tag->update($attributes);

        if(isset($attributes['faqs'])){
            $tag->faqs()->syncWithPivotValues($attributes['faqs'], ['faqable_type' => 'App\Models\Tag']);
        } else {
            $tag->faqs()->detach();
        }

        if (isset($attributes['image'])) {

            if ($tag->mainImage()) {
                $tag->deleteMainImage();
            }

            Image::updateOrCreate(['imageable_type' => 'App\Models\Tag', 'imageable_id' => $tag->id], [
                'filename' => $attributes['image'],
                'alt' => $attributes['alt'],
                'imageable_type' => 'App\Models\Tag',
                'imageable_id' => $tag->id,
                'main' => 1
            ]);
        } elseif ($tag->mainImage()) {
            // If delete image is checked
            if ($request->deleteImage) {

                $tag->deleteMainImage();

                $tag->mainImage()->delete();
            } else {
                // if there is an image, then update these fields

                $tag->mainImage()->update([
                    'alt' => $attributes['alt'],
                    // 'caption' => $attributes['caption']
                ]);
            }
        }

        return redirect()->back()->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Tag $tag)
    {
        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $tag->slug) {
                return redirect()->back()->with('error', 'Το tag δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        if($tag->mainImage()){
            $tag->deleteMainImage();
        }

        $tag->delete();

        return redirect()->route('cerberus.tags.index')->with('success', 'Το Tag διαγράφηκε με επιτυχία');
    }
}
