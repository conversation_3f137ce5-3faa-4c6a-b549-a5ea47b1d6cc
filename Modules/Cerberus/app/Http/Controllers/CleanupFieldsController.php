<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Post;
use App\Models\Package;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CleanupFieldsController extends Controller
{
    public function cleanUpInfo()
    {

        // Step 1: get all packages
        $packages = Package::whereLocale('info', 'el')->get();


        foreach ($packages as $package) {

            $content = $package->info;

            // Define the patterns to remove [su_accordion] and [su_spoiler] tags
            $patterns = [
                '/\[su_accordion\]/i', // Match opening [su_accordion]
                '/\[\/su_accordion\]/i', // Match closing [/su_accordion]
                '/\[su_spoiler.*?\]/i', // Match opening [su_spoiler] with attributes
                '/\[\/su_spoiler\]/i'  // Match closing [/su_spoiler]
            ];

            // Perform the replacement (remove the tags)
            $cleanedContent = preg_replace($patterns, '', $content);

            // Optionally, trim excess spaces or lines
            $cleanedContent = preg_replace("/\n+|\r+|\s{2,}/", "\n", $cleanedContent);

            // Trim excess newlines but keep those after text
            $cleanedContent = preg_replace("/([^\n])\n+|\r+|\s{2,}/", "$1\n", $cleanedContent);

            $package->update(['info' => $cleanedContent]);
        }

        return response()->json(['message' => 'Fields cleaned up'], 200);
    }


    public function cleanCruiseContent()
    {
        $cruises = Package::whereLocale('content', 'el')->get();


        foreach ($cruises as $cruise) {

            $content = $cruise->getTranslation('content', 'el');


            $pattern1 = '/<h3>.*?Δείτε αναλυτικά πληροφορίες, βίντεο και φωτογραφίες για το πλοίο.*?<\/h3>/is';
            $pattern2 = '/<h3>.*?Δείτε όλο το πρόγραμμα.*?<\/h3>/is';

            $cleanedContent = preg_replace($pattern1, '', $content);
            $cleanedContent = preg_replace($pattern2, '', $content);

            $cruise->update(['content' => ['el' => $cleanedContent]]);
        }

        return response()->json(['message' => 'Fields cleaned up'], 200);
    }

    public function cleanPostEmptyTags()
    {
        $posts = Post::whereLocale('content', 'el')->get();

        foreach ($posts as $post) {


            $content = $post->getTranslation('content', 'el');

            // Remove empty <p> tags and tags with only &nbsp;
            $content = preg_replace('/<p>(\s|&nbsp;)*<\/p>/', '', $content);

            // Remove all <div> tags but keep their inner content
            $content = preg_replace('/<div[^>]*>|<\/div>/', '', $content);

            // Normalize whitespace: remove multiple \r\n or \n, and replace with a single newline
            $content = preg_replace('/[\r\n]+/', "\n", $content);



            $post->update(['content' => ['el' => $content]]);
        }


        return response()->json(['message' => 'Fields cleaned up'], 200);
    }
}
