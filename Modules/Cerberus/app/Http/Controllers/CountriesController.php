<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Country;
use App\Models\Subregion;
use Illuminate\Http\Request;
use Modules\Cerberus\Http\Requests\CountryStoreRequest;
use Modules\Cerberus\Http\Requests\CountryUpdateRequest;

class CountriesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::geographies.countries.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $subregions = Subregion::orderBy('name')->get();

        return view('cerberus::geographies.countries.create', compact('subregions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CountryStoreRequest $request)
    {
        $attributes = $request->validated();

        $country = Country::create($attributes);

        return redirect()->route('cerberus.countries.edit', $country)->with('success', '΄Προστέθηκε με επιτυχία');
    }

    /**
     * Show the specified resource.
     */
    public function show(Country $country)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Country $country)
    {
        $subregions = Subregion::orderBy('name')->get();

        return view('cerberus::geographies.countries.edit', compact('country', 'subregions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CountryUpdateRequest $request, Country $country)
    {
        $attributes = $request->validated();

        $country->update($attributes);

        return redirect()->route('cerberus.countries.edit', $country);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Country $country)
    {
        if($country->regions->count()) {
            return redirect()->back()->with('error', 'Δεν μπορείτε να διαγράψετε αυτή τη χώρα. Έχει Regions');
        }

        $country->delete();

        return redirect()->route('cerberus.countries.index')->with('success', 'Διαγράφηκε με επιτυχία');
    }
}
