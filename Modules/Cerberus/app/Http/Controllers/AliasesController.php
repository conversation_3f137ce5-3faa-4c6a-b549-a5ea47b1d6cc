<?php
namespace Modules\Cerberus\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Alias;
use Illuminate\Http\Request;
use Modules\Cerberus\Http\Requests\AliasUpdateRequest;

class AliasesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::geographies.aliases.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::geographies.aliases.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Alias $alias)
    {
        $similar_aliases_count = Alias::where('aliasable_type', $alias->aliasable_type)->where('aliasable_id', $alias->aliasable_id)->count();

        return view('cerberus::geographies.aliases.edit', compact('alias', 'similar_aliases_count'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AliasUpdateRequest $request, Alias $alias)
    {
        $attributes = $request->validated();

        $alias->update($attributes);

        return redirect()->route('cerberus.aliases.edit', $alias)->with('success', 'Ενημερώθηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Alias $alias)
    {

        $alias->delete();

        return redirect()->route('cerberus.aliases.index')->with('success', 'Διαγράφηκε με επιτυχία');
    }
}
