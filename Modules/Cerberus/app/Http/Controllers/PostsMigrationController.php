<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Post;
use App\Models\Image;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class PostsMigrationController extends Controller
{
    public function __invoke()
    {

        /**
         * Step 1: Get all posts
         */

        $page = 1;
        $allPosts = collect();

        do {
            $response = Http::get('https://worldoftravel.gr/wp-json/wp/v2/posts', [
                'per_page' => 100,
                'page' => $page,
                'status' => 'publish',
                // 'before'   => '2023-12-31T23:59:59',  // Posts published before December 31, 2023
                // 'after'    => '2024-01-01T00:00:00',  // Posts published after January 1, 2023
            ]);

            if ($response->ok()) {
                $posts = collect($response->json())->sortBy('date');

                $allPosts = $allPosts->merge($posts);
            }
            $page++;
        } while ($page <= 3);


        // Step 2: store posts

        foreach ($allPosts as $post) {

            $new_post = Post::updateOrCreate(
                ['slug->el' => $post['slug']],
                [
                    'title' => [
                        'el' => $post['title']['rendered']
                    ],
                    'slug' => [
                        'el' => $post['slug']
                    ],
                    'content' => [
                        'el' => $post['content']['rendered']
                    ],
                    'meta_title' => [
                        'el' => $post['yoast_head_json']['title']
                    ],
                    'meta_description' => [
                        'el' => $post['yoast_head_json']['og_description']
                    ],
                    'published_at' => $post['yoast_head_json']['article_published_time'],
                    'created_at' => $post['date'],
                    'updated_at' => $post['modified'],
                    'published' => 1
                ]
            );

        }

        
        // Step 2: store posts main image

        foreach ($allPosts as $post) {

 
            $new_post = Post::where('slug->el', $post['slug'])->first();
            

            if ($post['featured_media'] && $new_post->mainImage() == null ) {
                // Get the featured image
                $image = Http::get('https://worldoftravel.gr/wp-json/wp/v2/media/' . $post['featured_media'])->json();

                // Get the image link to download
                $image_url = $image['link'];

                /** 
                 * Get the filename to store. Takes the initial filename and removes the first sections
                 * i.e. if filename is 2024/10/diimeri-ekdromi-konta-stin-athina-mainalo-oreini-korinthia-nayplio.jpg, remove 2024/10/
                 **/

                $filename = Arr::last(explode('/', $image['media_details']['file']));

                // Download and save image in a temp folder 'old_uploads'
                $contents = file_get_contents($image_url);

                Storage::put('old_uploads/' . $filename, $contents);

                // Get the alt text
                $alt_text = $image['alt_text'];

                // Get the file from the temp folder and upload it
                $uploaded_file = new UploadedFile('storage/old_uploads/' . $filename, $filename);

                // Store the file
                $new_image = Image::updateOrCreate(['imageable_id' => $new_post->id, 'imageable_type' => 'App\Models\Post'], [
                    'filename' => $uploaded_file,
                    'alt' => $alt_text,
                    'imageable_id' => $new_post->id,
                    'imageable_type' => 'App\Models\Post',
                    'main' => 1,
                ]);
            }
        }

        Storage::deleteDirectory('old_uploads');

        return 'Posts migrated';
    }
}
