<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Ship;
use App\Models\Image;
use App\Models\Package;
use Illuminate\View\View;
use App\Models\ShipFeature;
use App\Models\ImageGallery;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Http\Requests\ShipStoreRequest;
use Modules\Cerberus\Http\Requests\ShipUpdateRequest;
use Modules\Cerberus\Http\Requests\ImageGalleryStoreRequest;
use Modules\Cerberus\Http\Requests\ImageGalleryUpdateRequest;

class ShipsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::ships.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::ships.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(ShipStoreRequest $request)
    {
        $attributes = $request->validated();

        $ship = Ship::create($attributes);

        return redirect()->route('cerberus.ships.edit', $ship);
    }

    /**
     * Show the specified resource.
     */
    public function show(Ship $ship): View
    {
        return view('cerberus::show', compact('ship'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Ship $ship)
    {
        $locale = 'el';

        return view('cerberus::ships.edit', compact('ship', 'locale'));
    }

    /**
     * Show the form for editing in Engslish the specified resource.
     */
    public function editEn(Ship $ship)
    {
        $locale = 'en';

        return view('cerberus::ships.edit-en', compact('ship', 'locale'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ShipUpdateRequest $request, Ship $ship)
    {
        $attributes = $request->validated();

        // Check if the request contains image
        if (isset($attributes['image'])) {

            //If there is already a main image delete old main image files
            if ($ship->mainImage()) {

                $ship->deleteMainImage();
            }

            // store image to Image model
            $image = Image::updateOrCreate(['imageable_id' => $ship->id, 'imageable_type' => 'App\Models\Ship', 'main' => 1], [
                'filename' => $attributes['image'],
                'alt' => $attributes['alt'],
                // 'caption' => $attributes['caption'],
                'imageable_id' => $ship->id,
                'imageable_type' => 'App\Models\Ship',
                'main' => 1,
            ]);
        } elseif ($ship->mainImage()) {

            // If delete image is checked
            if ($request->deleteImage) {

                $ship->deleteMainImage();

                $ship->mainImage()->delete();
            } else {
                // if there is no image, then update these fields

                $mainImage = $ship->mainImage();
                $mainImage->timestamps = false;
                $mainImage->alt = $attributes['alt'];
                $mainImage->save();
            }
        }


        $ship->update($attributes);

        if (isset($attributes['feature'])) {
            // Create or update Ship Features
            foreach ($attributes['feature'] as $key => $feature) {

                if (isset($feature['feature_id'])) {
                    $shipFeature = ShipFeature::find($feature['feature_id']);

                    $shipFeature->update([
                        'title' => $feature['title'],
                        'content' =>  $feature['content'],
                    ]);
                } else {
                    $shipFeature = ShipFeature::create([
                        'ship_id' => $ship->id,
                        'title' => $feature['title'],
                        'content' => $feature['content'],
                        'order' => $ship->features->max('order') + 1
                    ]);
                }

                // If the request has image
                if (isset($feature['image'])) {

                    // If the feature has already an image, delete it
                    if ($shipFeature->bannerImage()) {
                        Storage::delete('ships/features/' . $shipFeature->bannerImage()->filename);
                    }

                    Image::updateOrCreate(
                        ['imageable_type' => 'App\Models\ShipFeature', 'imageable_id' => $shipFeature->id],
                        [
                            'filename' => $feature['image'],
                            'imageable_id' => $shipFeature->id,
                            'imageable_type' => 'App\Models\ShipFeature',
                            'alt' => [
                                'el' => $feature['alt']
                            ]

                        ]
                    );
                }
            }
        }

        if ($request->locale == 'en') {
            return redirect()->route('cerberus.ships.en.edit', $ship)->with('success', 'Αποθηκεύτηκε με επιτυχία');
        }

        return redirect()->route('cerberus.ships.edit', $ship)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Ship $ship)
    {

        // if the ship has main image, delete it and the files
        if ($ship->mainImage()) {

            // Delete file from server
            $ship->deleteMainImage();

            $ship->mainImage()->delete();
        }


        // if the ship has features with image
        if ($ship->features->count()) {
            foreach ($ship->features as $feature) {
                if ($feature->bannerImage()) {
                    Storage::delete('ships/features/' . $feature->bannerImage()->filename);
                }
            }
        }

        // Delete the ship has galleries
        if($ship->galleries->count()) {
            foreach ($ship->galleries as $gallery) {
                $gallery->images->each(function ($image) {
                    $image->delete();
                });

                Storage::deleteDirectory('galleries/' . $gallery->id);

                $gallery->delete();
            }
        }


        $ship->delete();

        return redirect()->route('cerberus.ships.index');
    }

    public function packages(Ship $ship)
    {

        return view('cerberus::ships.packages', compact('ship'));
    }

    public function packagesAssign(Request $request, Ship $ship)
    {
        $ship->packages()->sync($request->packages);

        return redirect()->route('cerberus.ship.assign.packages', $ship)->with('success', 'Η ανάθεση ολοκληρώθηκε');
    }

    public function createGallery(Ship $ship)
    {
        return view('cerberus::ships.galleries.create', compact('ship'));
    }

    public function storeGallery(ImageGalleryStoreRequest $request, Ship $ship)
    {
        $attributes = $request->validated();

        $imageGallery = ImageGallery::create([
            'title' => $attributes['title'],
            'content' => $attributes['content'],
            'galleriable_id' => $ship->id,
            'galleriable_type' => 'App\Models\Ship',
            'order' => $ship->galleries->max('order') + 1,
        ]);

        foreach ($attributes['images'] as $image) {
            Image::create([
                'imageable_type' => 'App\Models\ImageGallery',
                'imageable_id' => $imageGallery->id,
                'filename' => $image,
                'alt' => '',
            ]);
        }

        return redirect()->route('cerberus.ship.edit.gallery', [$ship, $imageGallery])->with('success', 'Η αποθήκευση ολοκληρώθηκε');
    }

    public function editGallery(Ship $ship, ImageGallery $gallery)
    {
        return view('cerberus::ships.galleries.edit', compact('ship', 'gallery'));
    }

    public function updateGallery(ImageGalleryUpdateRequest $request, Ship $ship, ImageGallery $gallery)
    {
        $attributes = $request->validated();

        $gallery->update($attributes);

        if ($attributes['images']) {
            foreach ($attributes['images'] as $image) {

                if (!$image instanceof UploadedFile) {
                    $imageModel = Image::find($image['id']);
                    $imageModel->update([
                        'caption' => $image['caption'],
                        'alt' => $image['alt'],
                    ]);
                } else {
                    Image::create([
                        'imageable_type' => 'App\Models\ImageGallery',
                        'imageable_id' => $gallery->id,
                        'filename' => $image,
                        'alt' => '',
                    ]);
                }
            }
        }

        return redirect()->route('cerberus.ship.edit.gallery', [$ship, $gallery])->with('success', 'Η αποθήκευση ολοκληρώθηκε');
    }

    public function destroyGallery(Ship $ship, ImageGallery $gallery)
    {
        $gallery->images->each(function ($image) {
            $image->delete();
        });

        Storage::deleteDirectory('galleries/' . $gallery->id);

        $gallery->delete();

        return redirect()->route('cerberus.ships.edit', $ship)->with('success', 'Η διαγραφή ολοκληρώθηκε');
    }
}
