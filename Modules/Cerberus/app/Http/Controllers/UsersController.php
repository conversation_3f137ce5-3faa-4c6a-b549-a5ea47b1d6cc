<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Http\RedirectResponse;
use Modules\Cerberus\Http\Requests\UserUpdateRequest;
use Modules\Cerberus\Http\Traits\RedirectAfterSaveTrait;

class UsersController extends CerberusController
{
    use RedirectAfterSaveTrait;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::users.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $roles = Role::all();

        return view('cerberus::users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserUpdateRequest $request, User $user): RedirectResponse
    {
        $attributes = $request->validated();

        $user->update($attributes);

        return $this->afterSave($request, 'cerberus.users', $user)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user): RedirectResponse
    {
        $user->delete();

        return redirect()->route('cerberus.admins.index')->with('success', 'Επιτυχής διαγραφή');
    }
}
