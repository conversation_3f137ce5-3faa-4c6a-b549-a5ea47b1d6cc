<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Image;
use App\Models\Banner;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Http\Requests\PopupStoreRequest;
use Modules\Cerberus\Http\Requests\PopupUpdateRequest;

class PopupsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::popups.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PopupStoreRequest $request)
    {
        $attributes = $request->validated();

        $popup = Banner::create($attributes);

        Image::updateOrCreate(['imageable_id' => $popup->id, 'imageable_type' => 'App\Models\Banner'], [
            'filename' => $attributes['image'],
            'alt' => $attributes['alt'],
            'imageable_id' => $popup->id,
            'imageable_type' => 'App\Models\Banner',
        ]);

        return redirect()->route('cerberus.popups.edit', $popup);
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Banner $popup)
    {
        return view('cerberus::popups.edit', compact('popup'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PopupUpdateRequest $request, Banner $popup)
    {
        $attributes = $request->validated();

        $popup->update($attributes);

        if (isset($attributes['image'])) {
            if (Storage::exists('banners/' . $popup->bannerImage()->filename)) {
                Storage::delete('banners/' . $popup->bannerImage()->filename);
            }

            $popup->bannerImage()->update([
                'filename' => $attributes['image']
            ]);
        }

        $popup->bannerImage()->update([
            'alt' => $attributes['alt']
        ]);

        return redirect()->route('cerberus.popups.edit', $popup);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Banner $popup)
    {
        Storage::delete('banners/'.$popup->bannerImage()->filename);

        $popup->bannerImage()->delete();

        $popup->delete();

        return redirect()->route('cerberus.homepage.index');
    }
}
