<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Tag;
use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;

class OrderingController extends Controller
{
    public function packagesOrder(Tag $tag): View
    {
        return view('cerberus::categories.packages-order', compact('tag'));
    }

    public function bannersOrder(Tag $tag): View
    {
        return view('cerberus::categories.banners-order', compact('tag'));
    }

    
}
