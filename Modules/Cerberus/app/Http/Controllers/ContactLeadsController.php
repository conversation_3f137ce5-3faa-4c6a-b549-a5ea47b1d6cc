<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ChatLead;
use App\Models\ContactLead;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;

class ContactLeadsController extends Controller
{
    public function travel(): View
    {
        $type = 'travel';
        return view('cerberus::leads.index', compact('type'));
    }

    public function cruise(): VIew 
    {
        $type = 'cruise';
        
        return view('cerberus::leads.index', compact('type'));

    }

    public function food(): View
    {
        $type = 'food';
        return view('cerberus::leads.index', compact('type'));
    }

    public function contact(): View
    {
        $type = 'contact';
        return view('cerberus::leads.index', compact('type'));
    }

    public function createPackage(): View
    {
        $type = 'create_package';
        return view('cerberus::leads.index', compact('type'));
    }

    public function show(ContactLead $lead): View
    {
        return view('cerberus::leads.show', compact('lead'));
    }

    public function chatbotIndex(): View 
    {
        return view('cerberus::leads.chatbot-index');
    }

    public function chatbotDetails(ChatLead $lead): View 
    {
        return view('cerberus::leads.chatbot-show', compact('lead'));
    }
}
