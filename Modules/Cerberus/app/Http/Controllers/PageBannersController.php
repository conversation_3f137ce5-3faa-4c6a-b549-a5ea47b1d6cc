<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Tag;
use App\Models\PageBanner;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Http\Requests\PageBannerStoreRequest;
use Modules\Cerberus\Http\Requests\PageBannerUpdateRequest;

class PageBannersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::pagebanners.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::pagebanners.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PageBannerStoreRequest $request)
    {
        $attributes = $request->validated();

        $page_banner = PageBanner::create($attributes);

        return redirect()->route('cerberus.pagebanners.edit', $page_banner);
    }

    /**
     * Show the specified resource.
     */
    public function show(PageBanner $page_banner)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PageBanner $page_banner)
    {
        $categories = Tag::category()->get();

        $tags = Tag::tag()->get();

        return view('cerberus::pagebanners.edit', compact('page_banner', 'categories', 'tags'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PageBannerUpdateRequest $request, PageBanner $page_banner)
    {
        $attributes = $request->validated();

        $page_banner->update($attributes);


        if(!isset($attributes['categories'])) {
            $attributes['categories'] = [];
        }
        if(!isset($attributes['tags'])) {
            $attributes['tags'] = [];
        }

        // Create one array to sync, to avoid substitutions between tags and categories
        $tags_categories = array_merge($attributes['categories'], $attributes['tags']);

        $page_banner->categories()->syncWithPivotValues($tags_categories, ['page_bannerable_type' => 'App\Models\Tag']);

        return redirect()->route('cerberus.pagebanners.edit', $page_banner);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PageBanner $page_banner)
    {

        if ($page_banner->filename && Storage::exists('pagebanners/' . $page_banner->image)) {
            Storage::delete('pagebanners/' . $page_banner->filename);
        }

        $page_banner->delete();

        return redirect()->route('cerberus.pagebanners.index');
    }
}
