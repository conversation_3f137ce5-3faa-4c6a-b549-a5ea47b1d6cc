<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Page;
use App\Models\Image;
use App\Models\MenuItem;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Http\Requests\PageStoreRequest;
use Modules\Cerberus\Http\Requests\PageUpdateRequest;
use Modules\Cerberus\Http\Traits\RedirectAfterSaveTrait;

class PagesController extends Controller
{
    use RedirectAfterSaveTrait;

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::pages/index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::pages.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PageStoreRequest $request): RedirectResponse
    {
        $attributes = $request->validated();

        $page = Page::create($attributes);

        return redirect()->route('cerberus.pages.edit', $page)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Page $page)
    {
        $locale = 'el';
        
        return view('cerberus::pages.edit', compact('page', 'locale'));
    }

    public function editEn(Page $page)
    {
        $locale = 'en';

        return view('cerberus::pages.edit-en', compact('page', 'locale'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PageUpdateRequest $request, Page $page): RedirectResponse
    {
        $attributes = $request->validated();

        if (isset($attributes['image'])) {

            //If there is already a main image delete old main image files
            if ($page->mainImage()) {
                $page->deleteMainImage();
            }

            // store image to Image model        
            $image = Image::updateOrCreate(['imageable_id' => $page->id, 'imageable_type' => 'App\Models\Page'], [
                'filename' => $attributes['image'],
                'alt' => $attributes['alt'],
                'imageable_id' => $page->id,
                'imageable_type' => 'App\Models\Page',
                'main' => 1,
            ]);
        } elseif ($page->mainImage()) {

            // If delete image is checked
            if ($request->deleteImage) {

                $page->deleteMainImage();

                $page->mainImage()->delete();
            } else {
                // if there is an image, then update these fields

                $page->mainImage()->update([
                    'alt' => $attributes['alt'],
                ]);
            }
        }


        $page->update($attributes);

        if ($request->locale == 'en') {
            return redirect()->route('cerberus.pages.en.edit', $page)->with('success', 'Αποθηκεύτηκε με επιτυχία');
        }

        return $this->afterSave($request, 'cerberus.pages', $page)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Page $page)
    {

        $menu_items = MenuItem::all();

        foreach ($menu_items as $menu_item) {
            $link = explode('/', $menu_item->link);
            if (end($link) == $page->slug) {
                return redirect()->back()->with('error', 'Η σελίδα δεν μπορεί να διαγραφεί γιατί χρησιμοποιείται σε μενού. Διαγράψτε πρώτα το αντίστοιχο μενού.');
            }
        }

        if($page->mainImage()) {
            $page->deleteMainImage();
        }

        $page->delete();

        return redirect()->route('cerberus.pages.index')->with('success', 'Διαγράφηκε με επιτυχία');
    }

    public function clearTranslation(Page $page): RedirectResponse
    {
        $attributes = [
            'title',
            'slug',
            'content',
            'meta_title',
            'meta_description',
        ];

        foreach ($attributes as $attribute) {
            $page->forgetTranslation($attribute, 'en');
        }

        $page->update($attributes);


        return redirect()->back()->with('success', 'Η μετάφραση καταργήθηκε');
    }
}
