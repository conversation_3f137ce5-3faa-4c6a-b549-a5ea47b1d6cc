<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Faq;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Models\MenuItem;
use Illuminate\Http\RedirectResponse;
use Modules\Cerberus\Http\Requests\FaqStoreRequest;
use Modules\Cerberus\Http\Requests\FaqUpdateRequest;

class FaqsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::faqs.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::faqs.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FaqStoreRequest $request): RedirectResponse
    {
        $attributes = $request->validated();

        $faq = Faq::create($attributes);

        return redirect()->route('cerberus.faqs.edit', $faq)->with('success', 'Αποθηκεύτηκε με επιτυχία');
    }

    /**
     * Show the specified resource.
     */
    public function show(Faq $faq)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Faq $faq)
    {
        // $categories = Tag::category()->get();
        // $tags = Tag::tag()->get();

        $menu_items = MenuItem::all();

        return view('cerberus::faqs.edit', compact('faq', 'menu_items'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FaqUpdateRequest $request, Faq $faq): RedirectResponse
    {
        $attributes = $request->validated();

        $faq->update($attributes);
       
        return redirect()->route('cerberus.faqs.edit', $faq)->with('success', 'Ενημερώθηκε με επιτυχία');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Faq $faq)
    {
        $faq->delete();

        return redirect()->route('cerberus.faqs.index')->with('success', 'Διαγράφηκε με επιτυχία');
    }
}
