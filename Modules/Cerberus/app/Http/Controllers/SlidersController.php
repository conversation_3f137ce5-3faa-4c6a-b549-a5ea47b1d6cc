<?php

namespace Modules\Cerberus\Http\Controllers;

use App\Models\Image;
use App\Models\Banner;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Modules\Cerberus\Http\Requests\SliderStoreRequest;
use Modules\Cerberus\Http\Requests\SliderUpdateRequest;

class SlidersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('cerberus::index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('cerberus::sliders.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(SliderStoreRequest $request)
    {
        $attributes = $request->validated();

        $slider = Banner::create($attributes);

        Image::updateOrCreate(['imageable_id' => $slider->id, 'imageable_type' => 'App\Models\Banner'], [
            'filename' => $attributes['image'],
            'alt' => $attributes['alt'],
            'imageable_id' => $slider->id,
            'imageable_type' => 'App\Models\Banner',
        ]);

        return redirect()->route('cerberus.sliders.edit', $slider);
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('cerberus::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Banner $slider)
    {
        if($slider->type !== 'slider') {
            abort(404);
        }

        return view('cerberus::sliders.edit', compact('slider'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(SliderUpdateRequest $request, Banner $slider)
    {
        $attributes = $request->validated();

        $slider->update($attributes);


        if(isset($attributes['image'])) {
            if(Storage::exists('banners/'.$slider->bannerImage()->filename)) {
                Storage::delete('banners/'.$slider->bannerImage()->filename);
            }

            $slider->bannerImage()->update([
                'filename' => $attributes['image']
            ]);
        }

        $slider->bannerImage()->update([
            'alt' => $attributes['alt']
        ]);


        return redirect()->route('cerberus.sliders.edit', $slider);

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Banner $slider)
    {
        Storage::delete('banners/'.$slider->bannerImage()->filename);

        $slider->bannerImage()->delete();

        $slider->delete();

        return redirect()->route('cerberus.homepage.index');
    }
}
