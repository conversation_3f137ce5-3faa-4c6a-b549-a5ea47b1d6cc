<?php

namespace Modules\Cerberus\Http\Requests;

use App\Rules\UniqueTranslatedSlug;
use Illuminate\Foundation\Http\FormRequest;

class PostStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title.el' => ['required'],
            'slug.el' => [new UniqueTranslatedSlug('posts', 'slug', 'el', null)]
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
