<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FaqUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'question' => ['array'],
            'question.el' => ['required'],
            'question.en' => ['required_with:answer.en'],
            'answer' => ['array'],
            'answer.el' => ['required_with:question.el'],
            'answer.en' => ['required_with:question.en'],
            'published' => [''],
            'featured' => [''],
        ];
    }

    public function messages()
    {
        return [
            'question.el.required' => 'Η ερώτηση είναι απαραίτητη',
            'question.*.required_with' => 'Η ερώτηση είναι απαραίτητη αν υπάρχει απάντηση',
            'answer.*.required_with' => 'Η απάντηση είναι απαραίτητη αν υπάρχει ερώτηση',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
