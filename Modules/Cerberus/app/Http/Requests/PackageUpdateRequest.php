<?php
namespace Modules\Cerberus\Http\Requests;

use App\Rules\UniqueTranslatedSlug;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

class PackageUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        
        return [
            'title'            => ['array'],
            'subtitle'         => ['array'],
            'tagline'          => ['array'],
            'content'          => ['array'],
            'itinerary'        => ['array'],
            'info'             => ['array'],
            'included'         => ['array'],
            'not_included'     => ['array'],
            'meta_title'       => ['array'],
            'meta_description' => ['array'],
            'sku'              => [],
            'price'            => [Rule::requiredIf($this->locale == 'el'), 'numeric'],
            'price_fixed'      => [],
            'duration'         => ['nullable', 'integer'],
            'steps'            => ['array'],
            'image'            => [
                File::image()
                    ->max(1024)
                    ->dimensions(Rule::dimensions()->maxWidth(1920)->maxHeight(1280)),
            ],
            'alt'              => ['array'],
            'pricelist'        => [
                File::image()
                    ->max(1024)
                    ->dimensions(Rule::dimensions()->maxWidth(1920)->maxHeight(1280)),
            ],
            'caption'          => ['array'],
            'published'        => [''],
            'featured'         => [''],
            'published_at'     => [],

            'type'             => [''],
        ]
             +
            ($this->input('locale') == 'el' ? $this->greek() : $this->english());
    }

    public function greek()
    {
        return [
            'title.el'     => ['required'],
            'slug.el'      => ['required', new UniqueTranslatedSlug('packages', 'slug', 'el', $this->route('package')->id)],
            'depart_dates' => ['array'],
        ];
    }

    public function english()
    {
        return [
            'title.en' => ['required'],
            'slug.en'  => ['required', new UniqueTranslatedSlug('packages', 'slug', 'en', $this->route('package')->id)],
            // 'content.en' => ['required_with:title.en']
        ];
    }

    public function messages()
    {
        return [
            'title.*.required'  => ['Ο τίτλος είναι υποχρεωτικό πεδίο'],
            'image.dimensions'  => 'Η εικόνα πρέπει να έχει πλάτος μέχρι 1920px και ύψος μέχρι 1280px',
            'image.max'         => 'Η εικόνα δεν πρέπει να είναι μεγαλύτερη από 1MB',
            'image.max_digits'  => 'Το όνομα αρχείου δεν πρέπει να ξεπερνάει τους 40 χαρακτήρες',
            'price.required'    => 'Η τιμή είναι απαραίτητη',
            'price.integer'     => 'Η τιμή θα πρέπει να είναι ακέραιος αριθμός',
            'duration.required' => 'Η διάρκεια είναι υποχρεωτικό πεδίο',

        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'depart_dates' => explode(',', $this->depart_dates),
            'price_fixed'  => $this->price_fixed == 'on' ? 1 : 0,
        ]);
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
