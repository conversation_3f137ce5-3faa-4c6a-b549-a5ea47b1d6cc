<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Validation\Rule;
use App\Rules\UniqueTranslatedSlug;
use Illuminate\Validation\Rules\File;
use Illuminate\Foundation\Http\FormRequest;

class PageUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['array'],
            'slug' => ['array'],
            'tagline' => ['array'],
            'meta_title' => ['array'],
            'meta_description' => ['array'],
            'content' => ['array'],
            'content.*' => [],
            'image' => [
                File::image()
                    ->max(1024)
                    ->dimensions(Rule::dimensions()->maxWidth(1920)->maxHeight(1280)),
                    
            ],
            'alt' => ['array'],
        ]
            +
            ($this->input('locale') == 'el' ? $this->greek() : $this->english());
    }

    public function greek()
    {
        return [
            'title.el' => ['required'],
            'slug.el' => ['required', new UniqueTranslatedSlug('posts', 'slug', 'el', $this->route('page')->id)],
        ];
    }

    public function english()
    {
        return [
            'title.en' => ['required'],
            'slug.en' => ['required', new UniqueTranslatedSlug('posts', 'slug', 'en', $this->route('page')->id)],
            // 'content.en' => ['required_with:title.en']
        ];
    }

    public function messages()
    {
        return [
            'content.en.required_with' => 'Το πεδίο Content είναι υποχρεωτικό εφόσον υπάρχει τίτλος.',
            'image.dimensions' => 'Η εικόνα πρέπει να έχει πλάτος μέχρι 1920px και ύψος μέχρι 1280px',
            'image.max' => 'Η εικόνα δεν πρέπει να είναι μεγαλύτερη από 1MB',
            'image.max_digits' => 'Το όνομα αρχείου δεν πρέπει να ξεπερνάει τους 40 χαρακτήρες',
            'title.*.required' => 'Ο τίτλος είναι υποχρεωτικό πεδίο',
            'slug.*.required' => 'Το slug είναι υποχρεωτικό πεδίο',
        ];
    }
    
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
