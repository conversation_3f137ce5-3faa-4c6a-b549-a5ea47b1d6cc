<?php
namespace Modules\Cerberus\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CountryStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name'         => ['required', 'min:2'],
            'iso_code'     => ['required', 'alpha', 'ascii', 'uppercase', 'min:3', 'max:3', Rule::unique('countries', 'iso_code')],
            'subregion_id' => ['required'],
        ];
    }

    public function messages()
    {
        return [
            'name.required'      => 'Το όνομα είναι απαραίτητο',
            'name.min'           => 'Το όνομα πρέπει να έχει τουλάχιστο 2 χαρακτήρες',
            'subregion_id'       => 'Είναι απαραίτητο να επιλέξετε Subregion',
            'iso_code.required'  => 'Είναι απαραίτητο να προσθέσετε ISO Code',
            'iso_code.unique'    => 'Το ISO Code χρησιμοποιείται ήδη',
            'iso_code.min'       => 'Το ISO Code πρέπει να έχει ακριβώς 3 ψηφία',
            'iso_code.max'       => 'Το ISO Code πρέπει να έχει ακριβώς 3 ψηφία',
            'iso_code.alpha'     => 'Το ISO Code πρέπει να έχει μόνο γράμματα',
            'iso_code.ascii'     => 'Το ISO Code πρέπει να έχει μόνο λατινικούς χαρακτήρες',
            'iso_code.uppercase' => 'Το ISO Code πρέπει να έχει μόνο κεφαλαία',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
