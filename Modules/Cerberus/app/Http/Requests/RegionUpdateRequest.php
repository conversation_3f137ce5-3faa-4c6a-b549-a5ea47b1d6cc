<?php
namespace Modules\Cerberus\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegionUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'country_id' => ['required'],
            'name'       => ['required', 'min:2'],
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Το όνομα είναι απαραίτητο',
            'name.min'      => 'Το όνομα πρέπει να έχει τουλάχιστο 2 χαρακτήρες',
            'country_id'    => 'Είναι απαραίτητο να επιλέξετε Χώρα',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
