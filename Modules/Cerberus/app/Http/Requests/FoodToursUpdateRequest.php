<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Validation\Rule;
use App\Rules\MaximumFilenameRule;
use App\Rules\UniqueTranslatedSlug;
use Illuminate\Validation\Rules\File;
use Illuminate\Foundation\Http\FormRequest;

class FoodToursUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['array'],
            'subtitle' => ['array'],
            'tagline' => ['array'],
            'content' => ['array'],
            'itinerary' => ['array'],
            'info' => ['array'],
            'included' => ['array'],
            'not_included' => ['array'],
            'meta_title' => ['array'],
            'meta_description' => ['array'],
            'sku' => [],
            'price' => [Rule::requiredIf($this->locale == 'el'), 'numeric'],
            'price_fixed' => [],
            'steps' => ['array'],
            'image' => [
                File::image()
                    ->max(1024)
                    ->dimensions(Rule::dimensions()->maxWidth(1920)->maxHeight(1280)),
            ],
            'alt' => ['array'],
            'caption' => ['array'],
            'duration' => [],
            'location' => [],
            'tour_type' => [],
            'group_size' => ['nullable'],
            'tour_languages' => ['array'],
            'published' => [''],
            'featured' => [''],
            'published_at' => [],
        ]
            +
            ($this->input('locale') == 'el' ? $this->greek() : $this->english());
    }

    public function greek()
    {
        return [
            'title.el' => ['required'],
            'slug.el' => ['required', new UniqueTranslatedSlug('packages', 'slug', 'el', $this->route('foodtour')->id)],
        ];
    }

    public function english()
    {
        return [
            'title.en' => ['required'],
            'slug.en' => ['required', new UniqueTranslatedSlug('packages', 'slug', 'en', $this->route('foodtour')->id)],
            // 'content.en' => ['required_with:title.en']
        ];
    }

    public function messages()
    {
        return [
            'title.el.required' => ['Ο τίτλος είναι υποχρεωτικό πεδίο'],
            'image.dimensions' => 'Η εικόνα πρέπει να έχει πλάτος μέχρι 1920px και ύψος μέχρι 1280px',
            'image.max' => 'Η εικόνα δεν πρέπει να είναι μεγαλύτερη από 1MB',
            'image.max_digits' => 'Το όνομα αρχείου δεν πρέπει να ξεπερνάει τους 40 χαρακτήρες',
            'price.required' => 'Η τιμή είναι απαραίτητη',
            'price.integer' => 'Η τιμή θα πρέπει να είναι ακέραιος αριθμός',
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'price_fixed' => $this->price_fixed == 'on' ? 1 : 0,
        ]);
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
