<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use CodeZero\LocalizedRoutes\Facades\LocaleConfig;
use Modules\Cerberus\Rules\AcceptedLocalesRule;

class MenuStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['required'],
            'name' => ['required', 'unique:menus,name'],
            'locale' => ['required', new AcceptedLocalesRule()],
            'published' => ['required']
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
