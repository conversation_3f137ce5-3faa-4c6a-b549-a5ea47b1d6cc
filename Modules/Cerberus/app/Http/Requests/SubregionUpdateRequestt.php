<?php
namespace Modules\Cerberus\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SubregionUpdateRequestt extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name'         => ['required', 'min:2'],
            'continent_id' => ['required'],
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Το όνομα είναι απαραίτητο',
            'name.min'      => 'Το όνομα πρέπει να έχει τουλάχιστο 2 χαρακτήρες',
            'continent_id'  => 'Είναι απαραίτητο να επιλέξετε Ήπειρο',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
