<?php

namespace Modules\Cerberus\Http\Requests;

use App\Rules\TagTitleRule;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Illuminate\Foundation\Http\FormRequest;

class TagUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
 
        return [
            'title' => ['array'],
            'title.el' => ['required'],
            'title.en' => [new TagTitleRule],
            'slug.el' => [Rule::requiredIf(isset($this->title['el']))],
            'slug.en' => [Rule::requiredIf(isset($this->title['en']))],
            'content' => ['array'],
            'meta_title' => ['array'],
            'image' => [
                File::image()
                ->max(1024)
                ->dimensions(Rule::dimensions()->maxWidth(1920)->maxHeight(1280)),
            ],
            'alt' => ['array'],
            'meta_description' => ['array'],
            'faqs' => ['array'],
            'template' => []
        ];
    }

    public function messages()
    {
        return [
            'title.el.required' => 'Το πεδίο Τίτλος είναι απαραίτητο',
            'title.en.required_if' => 'Το πεδίο τίτλος είναι απαραίτητο, αν υπάρχουν συμπληρωμένα άλλα πεδία',
            'slug.*.required' => 'Το πεδίο Slug είναι απαραίτητο',
            'image.max' => 'Το μέγεθος του αρχείου δεν πρέπει να είναι πάνω από 1 MB',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
