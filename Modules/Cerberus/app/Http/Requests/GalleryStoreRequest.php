<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GalleryStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'images' => ['array', 'required'],
            'images.*' => ['image', 'max:1024'],
        ];
    }

    public function messages()
    {
        return [
            'images.*.max' => 'Η εικόνα δεν πρέπει να είναι μεγαλύτερη από 1MB',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
