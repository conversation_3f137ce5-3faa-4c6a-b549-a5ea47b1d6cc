<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FoodtourStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title.el' => ['required'],
            'slug.el' => []
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
