<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Illuminate\Foundation\Http\FormRequest;

class PopupStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'type' => ['required',],
            'url' => ['array'],
            'url.el' => ['required'],
            'url.el' => ['url'],
            'alt' => ['array'],
            'image' => [
                'required',
                File::image()
                    ->max(512)
                    ->dimensions(Rule::dimensions()->maxWidth(1000)->maxHeight(1000)),
            ],
            'description' => []
        ] +
            ($this->url['en'] !== null ? $this->englishUrl() : []);
    }

    private function englishUrl()
    {
        return [
            'url.en' => ['url'],
        ];
    }

    public function messages()
    {
        return [
            'image.required' => 'Η εικόνα του banner είναι απαραίτητη',
            'image.max' => 'Το αρχείο πρέπει να είναι μικρότερο από 500kb',
            'url.el.required' => 'Το url είναι απαραίτητο',
            'url.*.url' => 'Δεν έχετε επιλέξει έγκυρο url',
        ];
    }


    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
