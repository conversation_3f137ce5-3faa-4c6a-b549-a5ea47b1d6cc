<?php

namespace Modules\Cerberus\Http\Requests;

use Closure;
use Illuminate\Validation\Rule;
use Illuminate\Http\UploadedFile;
use Illuminate\Validation\Rules\File;
use Illuminate\Foundation\Http\FormRequest;

class ImageGalleryUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'title' => ['array'],
            'content' => ['array'],
            'images' => ['array'],
            'images.*' => [
                function (string $attribute, mixed $value, Closure $fail) {

                    if ($value instanceof UploadedFile) {
                        File::image()
                            ->max(512)
                            ->dimensions(Rule::dimensions()->maxWidth(800)->maxHeight(800));
                    }
                },

            ],
            'images.*.caption' => ['array'],
            'images.*.alt' => ['array'],
        ];
    }

    public function messages(): array
    {
        return [
            'images.*.image' => 'Το αρχείο θα πρέπει να είναι εικόνα.',
            'images.*.max' => 'Η εικόνα δεν πρέπει να είναι μεγαλύτερη από 512 kilobytes.',
            'images.*.dimensions' => 'Η εικόνα δεν πρέπει να ξεπερνά τις διαστάσεις τα 800x800 pixels.',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
