<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Illuminate\Foundation\Http\FormRequest;

class SliderUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'type' => ['required',],
            'url' => ['array'],
            'url.el' => ['nullable','url'],
            'alt' => ['array'],
            'title' => ['array'],
            'content' => ['array'],
            'image' => [
                'sometimes',
                File::image()
                    ->max(512)
                    ->dimensions(Rule::dimensions()->maxWidth(1920)->maxHeight(1280)->ratio(3 / 2)),
            ],
            'description' => []
        ] +
            ($this->url['en'] !== null ? $this->englishUrl() : []);
    }

    private function englishUrl()
    {
        return [
            'url.en' => ['nullable','url'],
        ];
    }

    public function messages()
    {
        return [
            'image.required' => 'Η εικόνα του banner είναι απαραίτητη',
            'image.max' => 'Το αρχείο πρέπει να είναι μικρότερο από 500kb',
            'image.dimensions' => 'Το αρχείο πρέπει να έχει μέγιστο πλάτος 1920px και αναλογίες 3/2',
            'url.el.required' => 'Το url είναι απαραίτητο',
            'url.*.url' => 'Δεν έχετε επιλέξει έγκυρο url',
        ];
    }


    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
