<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;
use Modules\Cerberus\Rules\AcceptedLocalesRule;

class MenuUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['required'],
            'name' => ['required', Rule::unique('menus')->ignore($this->id)],
            'locale' => ['required', new AcceptedLocalesRule()],
            'published' => ['required']
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
