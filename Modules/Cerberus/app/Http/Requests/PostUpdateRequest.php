<?php

namespace Modules\Cerberus\Http\Requests;

use App\Rules\MaximumFilenameRule;
use App\Rules\UniqueTranslatedSlug;
use Carbon\Carbon;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Illuminate\Foundation\Http\FormRequest;

class PostUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     * If the locale is Greek, use also the Greek validation method. If it is English, use the English one.
     */
    public function rules(): array
    {
        return [
            'title' => ['array'],
            'slug' => ['array'],
            'tagline' => ['array'],
            'meta_title' => ['array'],
            'meta_description' => ['array'],
            'content' => ['array'],
            'content.*' => [],
            'image' => [
                File::image()
                    ->max(1024)
                    ->dimensions(Rule::dimensions()->maxWidth(1920)->maxHeight(1280)),
                new MaximumFilenameRule
            ],
            'alt' => ['array'],
            'caption' => ['array'],
            'published_at' => [],
        ]
        +
        ($this->input('locale') == 'el' ? $this->greek() : $this->english() )
        ;
    }

    public function greek()
    {
        return [
            'title.el' => ['required'],
            'slug.el' => ['required', new UniqueTranslatedSlug('posts', 'slug', 'el', $this->route('post')->id)],
        ];
    }

    public function english()
    {
        return [
            'title.en' => ['required'],
            'slug.en' => ['required', new UniqueTranslatedSlug('posts', 'slug', 'en', $this->route('post')->id)],
            'content.en' => ['required_with:title.en']
        ];
    }

    public function messages()
    {
        return [
            'image.dimensions' => 'Η εικόνα πρέπει να έχει πλάτος μέχρι 1920px και ύψος μέχρι 1280px',
            'image.max' => 'Η εικόνα δεν πρέπει να είναι μεγαλύτερη από 1MB',
            'image.max_digits' => 'Το όνομα αρχείου δεν πρέπει να ξεπερνάει τους 40 χαρακτήρες',
            'content.en.required_with' => 'Το πεδίο Content είναι υποχρεωτικό εφόσον υπάρχει τίτλος.',
            'title.*.required' => 'Ο τίτλος είναι υποχρεωτικό πεδίο',
            'slug.*.required' => 'Το slug είναι υποχρεωτικό πεδίο',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'published_at' => Carbon::parse($this->published_at)->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
