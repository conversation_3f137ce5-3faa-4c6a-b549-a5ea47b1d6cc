<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PackageStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title.el' => ['required'],
            'slug.el' => [],
            'type' => ['required']
        ];
    }

    public function messages()
    {
        return [
            'type.required' => 'Είναι απαραίτητο να επιλέξετε τύπο'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
