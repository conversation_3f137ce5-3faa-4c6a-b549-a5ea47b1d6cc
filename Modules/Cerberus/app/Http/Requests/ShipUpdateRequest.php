<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Validation\Rule;
use App\Rules\UniqueTranslatedSlug;
use Illuminate\Validation\Rules\File;
use Illuminate\Foundation\Http\FormRequest;

class ShipUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {

        return [
            'title' => ['array'],
            'content' => ['array'],
            'video_id' => [],
            'meta_title' => ['array'],
            'meta_description' => ['array'],
            'image' => [
                File::image()
                    ->max(1024)
                    ->dimensions(Rule::dimensions()->maxWidth(1920)->maxHeight(1280)),

            ],
            'feature' => ['array'],
            'feature.*.title' => ['required_with:feature.el.content'],
            'feature.*.title' => [''],
            'feature.*.content' => [''],
            'feature.*.feature_id' => [],
            'feature.*.image' => [
                File::image()
                    ->max(512)
                    ->dimensions(Rule::dimensions()->maxWidth(800)->maxHeight(800)),
            ],
            'feature.*.alt' => [],
            'alt' => ['array'],

        ]
            +
            ($this->input('locale') == 'el' ? $this->greek() : $this->english());;
    }

    public function greek()
    {
        return [
            'title.el' => ['required'],
            'slug.el' => ['required', new UniqueTranslatedSlug('ships', 'slug', 'el', $this->route('ship')->id)],
            
        ];
    }

    public function english()
    {
        return [
            'title.en' => ['required'],
            'slug.en' => ['required', new UniqueTranslatedSlug('ships', 'slug', 'en', $this->route('ship')->id)],
            
        ];
    }

    public function messages()
    {
        return [
            'feature.*.title.required' => 'Ο τίτλος είναι απαραίτητος',
            'feature.*.content.required' => 'Το content είναι απαραίτητο',
            'feature.*.image.dimensions' => 'Η φωτογραφία θα πρέπει να έχει μέγιστη πλευρά 800px'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
