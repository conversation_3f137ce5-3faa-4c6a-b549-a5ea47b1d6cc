<?php

namespace Modules\Cerberus\Http\Requests;

use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Illuminate\Foundation\Http\FormRequest;

class ImageGalleryStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['array'],
            'content' => ['array'],
            'images' => ['array', 'required'],
            'images.*' => [
                File::image()
                    ->max(512)
                    ->dimensions(Rule::dimensions()->maxWidth(800)->maxHeight(800)),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'images.required' => 'Δεν έχετε προσθέσει καμία εικόνα.',
            'images.*.image' => 'Το αρχείο θα πρέπει να είναι εικόνα.',
            'images.*.max' => 'Η εικόνα δεν πρέπει να είναι μεγαλύτερη από 512 kilobytes.',
            'images.*.dimensions' => 'Η εικόνα δεν πρέπει να ξεπερνά τις διαστάσεις τα 800x800 pixels.',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
