<?php

namespace Modules\Cerberus\Http\Traits;

/***
    Redirect after save depending on the save button

*/

trait RedirectAfterSaveTrait {

    public function afterSave($request, $routeName, $object)
    {

        if($request->submit == 'save') {

            return redirect()->route($routeName.'.edit', $object);

        } elseif($request->submit == 'save-close') {

            return redirect()->route($routeName.'.index');

        }elseif($request->submit == 'save-new' ) {
            return redirect()->route($routeName.'.create');
        };
    }

    public function afterSaveSecondLevel($request, $routeName, array $objects)
    {

        if($request->submit == 'save') {

            return redirect()->route($routeName.'.edit', $objects);

        } elseif($request->submit == 'save-close') {

            return redirect()->route($routeName.'.index', $objects[0]);

        }elseif($request->submit == 'save-new' ) {
            return redirect()->route($routeName.'.create', $objects[0]);
        };
    }

    public function afterSaveThirdLevel($request, $routeName, array $objects)
    {

        if($request->submit == 'save') {

            return redirect()->route($routeName.'.edit', $objects);

        } elseif($request->submit == 'save-close') {

            return redirect()->route($routeName.'.index', [$objects[0], $objects[1]]);

        }elseif($request->submit == 'save-new' ) {
            return redirect()->route($routeName.'.create', [$objects[0], $objects[1]]);
        };
    }

}
