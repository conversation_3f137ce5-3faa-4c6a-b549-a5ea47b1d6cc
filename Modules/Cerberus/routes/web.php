<?php

use Illuminate\Support\Facades\Route;
use Modules\Cerberus\Http\Controllers\FaqsController;
use Modules\Cerberus\Http\Controllers\TagsController;
use Modules\Cerberus\Http\Controllers\MenusController;
use Modules\Cerberus\Http\Controllers\PagesController;
use Modules\Cerberus\Http\Controllers\PostsController;
use Modules\Cerberus\Http\Controllers\RolesController;
use Modules\Cerberus\Http\Controllers\ShipsController;
use Modules\Cerberus\Http\Controllers\UsersController;
use Modules\Cerberus\Http\Controllers\AdminsController;
use Modules\Cerberus\Http\Controllers\AliasesController;
use Modules\Cerberus\Http\Controllers\PopupsController;
use Modules\Cerberus\Http\Controllers\BannersController;
use Modules\Cerberus\Http\Controllers\RegionsController;
use Modules\Cerberus\Http\Controllers\SlidersController;
use Modules\Cerberus\Http\Controllers\CerberusController;
use Modules\Cerberus\Http\Controllers\HomepageController;
use Modules\Cerberus\Http\Controllers\OrderingController;
use Modules\Cerberus\Http\Controllers\PackagesController;
use Modules\Cerberus\Http\Controllers\FoodToursController;
use Modules\Cerberus\Http\Controllers\GetCitiesController;
use Modules\Cerberus\Http\Controllers\MenuItemsController;
use Modules\Cerberus\Http\Controllers\CategoriesController;
use Modules\Cerberus\Http\Controllers\CitiesController;
use Modules\Cerberus\Http\Controllers\SubRegionsController;
use Modules\Cerberus\Http\Controllers\PageBannersController;
use Modules\Cerberus\Http\Controllers\PermissionsController;
use Modules\Cerberus\Http\Controllers\ContactLeadsController;
use Modules\Cerberus\Http\Controllers\CleanupFieldsController;
use Modules\Cerberus\Http\Controllers\ContinentsController;
use Modules\Cerberus\Http\Controllers\CountriesController;
use Modules\Cerberus\Http\Controllers\PostsMigrationController;
use Modules\Cerberus\Http\Controllers\PackagesMigrationController;
use Modules\Cerberus\Http\Controllers\FoodToursMigrationController;
use Modules\Cerberus\Http\Controllers\PricelistMigrationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', [Modules\Cerberus\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

Route::group([], function () {
    // TODO: unmake this a resource controller route
    Route::resource('cerberus', CerberusController::class)->names('cerberus');

    Route::get('posts/{post}/en/edit', [PostsController::class, 'editEn'])->name('posts.en.edit');
    Route::post('posts/{post}/clear-translation', [PostsController::class, 'clearTranslation'])->name('posts.clear.translation');
    Route::resource('posts', PostsController::class)->names('posts')->except('show');

    Route::get('packages/{package}/en/edit', [PackagesController::class, 'editEn'])->name('packages.en.edit');
    Route::post('packages/{package}/clear-translation', [PackagesController::class, 'clearTranslation'])->name('packages.clear.translation');
    Route::resource('packages', PackagesController::class)->names('packages')->except('show');

    Route::get('foodtours/{foodtour}/gallery', [FoodtoursController::class, 'gallery'])->name('foodtour.show.gallery');
    Route::post('foodtours/{foodtour}/gallery', [FoodtoursController::class, 'storeGallery'])->name('foodtour.store.gallery');
    Route::get('foodtours/{foodtour}/en/edit', [FoodToursController::class, 'editEn'])->name('foodtours.en.edit');
    Route::resource('foodtours', FoodToursController::class)->names('foodtours')->except('show');

    Route::get('ships/{ship}/en/edit', [ShipsController::class, 'editEn'])->name('ships.en.edit');
    Route::get('ships/{ship}/packages', [ShipsController::class, 'packages'])->name('ship.packages');
    Route::post('ships/{ship}/packages', [ShipsController::class, 'packagesAssign'])->name('ship.assign.packages');
    Route::get('ships/{ship}/galleries/create', [ShipsController::class, 'createGallery'])->name('ship.create.gallery');
    Route::post('ships/{ship}/galleries', [ShipsController::class, 'storeGallery'])->name('ship.store.gallery');
    Route::get('ships/{ship}/galleries/{gallery}/edit', [ShipsController::class, 'editGallery'])->name('ship.edit.gallery');
    Route::patch('ships/{ship}/galleries/{gallery}', [ShipsController::class, 'updateGallery'])->name('ship.update.gallery');
    Route::delete('ships/{ship}/galleries/{gallery}', [ShipsController::class, 'destroyGallery'])->name('ship.destroy.gallery');
    Route::resource('ships', ShipsController::class)->names('ships')->except('show');



    Route::resource('categories', CategoriesController::class)->names('categories');

    Route::resource('tags', TagsController::class)->names('tags');

    Route::get('package-ordering/{tag}', [OrderingController::class, 'packagesOrder'])->name('packages.order');
    Route::get('banners-ordering/{tag}', [OrderingController::class, 'bannersOrder'])->name('banners.order');

    Route::resource('admins', AdminsController::class)->names('admins')->except(['show']);

    Route::resource('users', UsersController::class)->names('users')->except(['show', 'create', 'store']);

    Route::resource('roles', RolesController::class)->names('roles')->except('show');

    Route::resource('permissions', PermissionsController::class)->names('permissions')->except('show');

    Route::resource('menus', MenusController::class)->names('menus')->except('show');
    Route::resource('menus/{menu}/menu-items', MenuItemsController::class)->names('menu-items')->except('show');

    Route::get('travel-leads', [ContactLeadsController::class, 'travel'])->name('leads.travel');
    Route::get('leads/{lead}', [ContactLeadsController::class, 'show'])->name('leads.show');
    Route::get('cruise-leads', [ContactLeadsController::class, 'cruise'])->name('leads.cruise');
    Route::get('food-leads', [ContactLeadsController::class, 'food'])->name('leads.food');
    Route::get('contact-leads', [ContactLeadsController::class, 'contact'])->name('leads.contact');
    Route::get('create-package', [ContactLeadsController::class, 'createPackage'])->name('leads.create_package');
    Route::get('chatbot-leads', [ContactLeadsController::class, 'chatbotIndex'])->name('leads.chatbot.index');
    Route::get('chatbot-leads/{lead}', [ContactLeadsController::class, 'chatbotDetails'])->name('leads.chatbot.details');

    Route::resource('faqs', FaqsController::class)->names('faqs')->except('show');

    Route::get('pages/homepage', [HomepageController::class, 'index'])->name('homepage.index');
    Route::get('pages/{page}/en/edit', [PagesController::class, 'editEn'])->name('pages.en.edit');
    Route::post('pages/{page}/clear-translation', [PagesController::class, 'clearTranslation'])->name('pages.clear.translation');
    Route::resource('pages', PagesController::class)->names('pages')->except('show');

    Route::resource('banners', BannersController::class)->names('banners')->except('show');
    Route::resource('sliders', SlidersController::class)->names('sliders')->except('show');
    Route::resource('popups', PopupsController::class)->names('popups')->except('show');
    Route::resource('page-banners', PageBannersController::class)->names('pagebanners')->except('show');

    Route::resource('continents', ContinentsController::class)->names('continents')->except('show');
    Route::resource('subregions', SubRegionsController::class)->names('subregions')->except('show');
    Route::resource('countries', CountriesController::class)->names('countries')->except('show');
    Route::resource('regions', RegionsController::class)->names('regions')->except('show');
    Route::resource('cities', CitiesController::class)->names('cities')->except(['show', 'store', 'update']);
    Route::resource('aliases', AliasesController::class)->names('aliases');
    
});
