<?php

use Illuminate\Http\Request;
use Illuminate\Foundation\Application;
use App\Http\Middleware\RedirectIfNotEnduser;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Modules\Cerberus\Http\Middleware\RedirectIfNotDev;
use Modules\Cerberus\Http\Middleware\RedirectIfNotAdmin;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'enduser' => RedirectIfNotEnduser::class,
            'admin' => RedirectIfNotAdmin::class,
            'dev' => RedirectIfNotDev::class,
        ]);
        $middleware->web(remove: [
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ]);
        $middleware->web(append: [
            \CodeZero\LocalizedRoutes\Middleware\SetLocale::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (NotFoundHttpException $e, Request $request) {
            
            // Checks the url for language parameter and sets it for 404 page
            if ($request->segment(1) == 'en') {
                return response()->view('errors.404', [
                    app()->setLocale('en')
                ], 404);
            }
        });
    })->create();
