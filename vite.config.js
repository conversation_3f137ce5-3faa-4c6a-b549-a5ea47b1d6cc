import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import path from 'path';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'Modules/Cerberus/resources/assets/css/cerby.css', // Backend CSS
                'resources/css/frontend.css',                     // Frontend CSS
                'Modules/Cerberus/resources/assets/js/cerby.js',  // Backend JS
                'resources/js/app.js'                             // Frontend JS
            ],
            refresh: true,
        }),
    ],
    build: {
        chunkSizeWarningLimit: 1600, // Set a warning limit for chunk size
    },
    resolve: {
        alias: {
            '@': path.resolve('resources'), // Alias for resources
            'Modules': path.resolve('Modules'), // Alias for modules
        },
    },
});
