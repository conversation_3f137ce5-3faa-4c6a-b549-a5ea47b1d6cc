<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\RedirectController;

// Redirects from old website to the new, for pages that were ranking high

Route::get('product/{slug}', [RedirectController::class, 'productRedirect']);
Route::get('food-tours-athens', [RedirectController::class, 'foodIndexRedirect']);
Route::get('blog-2', [RedirectController::class, 'blogIndexRedirect']);



Route::get('product-category/krouazieres-2025/icon-of-the-seas/', function () {
    return redirect('tags/icon-of-the-seas');
});
Route::get('product-category/krouazieres-2024/icon-of-the-seas', function () {
    return redirect('tags/icon-of-the-seas');
});
Route::get('product-category/krouazieres-2025/krouazieres-apo-peiraia/', function () {
    return redirect('tags/kroyazieres-apo-peiraia');
});
Route::get('product-category/makrina-taxidia/vietnam/', function () {
    return redirect('tags/vietnam');
});
Route::get('product-category/paketa-diakopon/protochronia-2025/', function () {
    return redirect('tags/protokhronia');
});
Route::get('product-category/paketa-diakopon/theofaneia-2025/', function () {
    return redirect('tags/theofaneia');
});
Route::get('product-category/protochronia-2025-2/', function () {
    return redirect('tags/protokhronia');
});
Route::get('product-category/protochronia-2025-2/paketa/', function () {
    return redirect('tags/protokhronia');
});
Route::get('product-category/taxidi/nea-yorki/', function () {
    return redirect('tags/nea-yorki');
});
Route::get('ispania-taxidi/', function () {
    return redirect('tags/ispania');
});
Route::get('taxidi-stin-praga/', function () {
    return redirect('tags/pragha');
});
Route::get('product-category/proorismoi/evropi/praga', function () {
    return redirect('tags/pragha');
});
Route::get('malta-taxidi/', function () {
    return redirect('tags/malta');
});
Route::get('product-category/proorismoi/evropi/malta', function () {
    return redirect('tags/malta');
});
Route::get('product-category/christougenna-2024-2/', function () {
    return redirect('tags/xristoughenna');
});
Route::get('paketa-diakopon-pascha-2024/', function () {
    return redirect('tags/paskha');
});
Route::get('/product-category/paketa-diakopon/pascha-2025', function () {
    return redirect('tags/paskha');
});
Route::get('product-category/taxidia-2/28-oktovriou-2024', function () {
    return redirect('tags/28i-oktovrioy');
});
Route::get('product-category/paketa-diakopon/proorismoi/evropi/romi', function () {
    return redirect('tags/italia');
});


Route::get('taxidia-evropi-prosfores/', function () {
    return redirect('travel-category/prosfores');
});
Route::get('paketa-diakopwn-ellada/', function () {
    return redirect('travel-category/ellada');
});
Route::get('krouazieres-2025/', function () {
    return redirect('travel-category/kroyazieres');
});
Route::get('organomena-atomika-taxidia/', function () {
    return redirect('travel-category/atomika-taksidia');
});
Route::get('paketa-diakopon-kalokairi-2024', function () {
    return redirect('travel-category/kalokairi');
});
Route::get('gamilia-taxidia-2024', function () {
    return redirect('travel-category/gamilia-taksidia');
});


Route::get('dimiourgia-paketo-diakopon', function () {
    return redirect('pages/dimioyrgia-paketoy-diakopwn');
});
Route::get('epikoinonia', function () {
    return redirect('pages/epikoinonia');
});
Route::get('politikou-aporritou', function () {
    return redirect('pages/politiki-aporritoy');
});


Route::get('{post:slug}', [RedirectController::class, 'postsRedirect'])
    ->where('post', '^(?!cerby(/.*)?$).*');
