<?php

use Carbon\Carbon;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ShipController;
use App\Http\Controllers\TagsController;
use App\Http\Controllers\PagesController;
use App\Http\Controllers\PostsController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\HomepageController;
use App\Http\Controllers\PackagesController;
use App\Http\Controllers\RedirectController;
use App\Http\Controllers\FoodToursController;
use App\Http\Controllers\CategoriesController;

Route::localized(function () {
    Route::get('/', [HomepageController::class, 'show'])->name('home');

    // blog
    Route::get('blog', [PostsController::class, 'index'])->name('posts.index');
    Route::get('blog/{post:slug}', [PostsController::class, 'show'])->name('posts.show');
    Route::get('pages/{page:slug}', PagesController::class)->name('pages.show');

    // travel packages
    // Route::get('travel', [PackagesController::class, 'index'])->name('travel.index');
    Route::get('travel-category/{tag:slug}',   [TagsController::class, 'showCategory'])->name('category.show');
    Route::get('travel/{package:slug}', [PackagesController::class, 'show'])->name('travel.show');
    Route::get('ships/{ship:slug}', [ShipController::class, 'show'])->name('ship.show');


    // Food Tour Packages
    Route::get('food-tours', [FoodToursController::class, 'index'])->name('food-tours.index');
    Route::get('food-tours/{foodtour:slug}', [FoodToursController::class, 'show'])->name('foodtours.show');


    // Tags
    Route::get('tags/{tag:slug}', [TagsController::class, 'showTag'])->name('tag.show');

    Route::get('search', [SearchController::class, 'index'])->name('search.index');
});

// Chat route
// Route::get('/chat', function () {
//     $package = \App\Models\Package::travel()->first();

//     $categories = $package->categories()->get();
//     $tags = $package->tags()->get();

//     return view('packages.show', compact('package', 'categories', 'tags'));
// })->name('chat');

// test routes
// Only expose routes on local env
if (env('APP_ENV') === 'local') {
    Route::get('test-excel', [\App\Http\Controllers\TestController::class, 'testExcel']);
    Route::get('test-g-translate', [\App\Http\Controllers\TestController::class, 'testGTranslate']);
    Route::get('test-email', [\App\Http\Controllers\TestController::class, 'testEmail']);

    // session-dump
    Route::get('/session-dump', function () {

        dump(config('chat.chat_persistence_seconds'));
        dump(request()->session()->get('emy_chat_last_timestamp') ? request()->session()->get('emy_chat_last_timestamp')->diffInSeconds(Carbon::now()) : '');
        dump(request()->session()->get('emy_chat_last_timestamp') ? request()->session()->get('emy_chat_last_timestamp')->diffInSeconds(Carbon::now()) > config('chat.chat_persistence_seconds') : '');
        dump(request()->session()->get('emy_chat_last_timestamp'));
        dump(request()->session()->get('emy_chat_history'));
        dump(request()->session()->get('emy_chat'));

        echo "<script>
        var compacted = document.querySelectorAll('.sf-dump-compact');
        for (var i = 0; i < compacted.length; i++) {
          compacted[i].className = 'sf-dump-expanded';
        }
    </script>";
        dd(session());
    });
}

//********** Enduser routes below here ***********
Route::get('/dashboard', function () {
    return redirect()->route('cerberus.dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');


Route::middleware(['auth'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__ . '/auth.php';

// Redirect 301 old routes to new ones
require __DIR__ . '/redirects.php';
