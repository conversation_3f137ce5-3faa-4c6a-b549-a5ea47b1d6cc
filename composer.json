{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "codezero/laravel-localized-routes": "^4.0", "echolabsdev/prism": "^0.47.2", "google/cloud-translate": "^1.19", "intervention/image-laravel": "^1.2", "laravel/breeze": "^2.0", "laravel/framework": "^11.0", "laravel/tinker": "^2.9", "livewire/livewire": "^3.4", "maatwebsite/excel": "^3.1", "mhmiton/laravel-modules-livewire": "^3.0", "nwidart/laravel-modules": "^11.0", "opcodesio/log-viewer": "^3.10", "spatie/laravel-permission": "^6.7", "spatie/laravel-searchable": "^1.12", "spatie/laravel-sitemap": "^7.3", "spatie/laravel-sluggable": "^3.6", "spatie/laravel-translatable": "^6.6", "staudenmeir/eloquent-has-many-deep": "^1.7", "symfony/http-client": "^7.1", "symfony/mailgun-mailer": "^7.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["Modules/*/composer.json"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true, "codezero/composer-preload-files": true}}, "minimum-stability": "stable", "prefer-stable": true}