import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',        
        './Modules/Cerberus/resources/views/**/*.blade.php', 
        './Modules/Cerberus/resources/assets/css/cerby.css', 
    ],
    theme: {
        extend: {
            fontFamily: {
                sans: ['Manrope'],
            },
            fontSize: {
                base: '1rem',
            },
            colors: {
                pacific: {
                    50: '#24F3FC',
                    100: '#08F2FB',
                    200: '#04DAE3',
                    300: '#03C8D0',
                    400: '#03BFC6',
                    500: '#03B6BD',
                    600: '#03A4AA',
                    700: '#029297',
                    800: '#027F84',
                    900: '#026D71',
                },
                sun: {
                    50: '#FFE7D3',
                    100: '#FFD4A6',
                    200: '#FFC179',
                    300: '#FFAD4C',
                    400: '#FF9920',
                    500: '#F6941C',
                    600: '#E68719',
                    700: '#D67A16',
                    800: '#C66D13',
                    900: '#B66010',
                },
                wotDark: '#282828',
                wotLighttGrey: '#f2f2f2',
            }
        },
    },
    plugins: [forms],
};
