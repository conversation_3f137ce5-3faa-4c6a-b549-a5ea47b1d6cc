import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './resources/css/frontend.css',
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
            fontFamily: {
                // sans: ['Figtree', ...defaultTheme.fontFamily.sans],
                sans: ['Manrope'],
                funny: ['Mynerve']
            },
            fontSize: {
                base: '1.2rem',
                chat: '1rem',
            },
            listStyleType: {
                disc: 'disc',
                square: 'square',
            },
            colors: {
                pacific: {
                    50: '#24F3FC',
                    100: '#08F2FB',
                    200: '#04DAE3',
                    300: '#03C8D0',
                    400: '#03BFC6',
                    500: '#03B6BD',
                    600: '#03A4AA',
                    700: '#029297',
                    800: '#027F84',
                    900: '#026D71',
                    1000: '#08464C',
                },
                sun: {
                    50: '#FFE7D3',
                    100: '#FFD4A6',
                    200: '#FFC179',
                    300: '#FFAD4C',
                    400: '#FF9920',
                    500: '#F6941C',
                    600: '#E68719',
                    700: '#D67A16',
                    800: '#C66D13',
                    900: '#B66010',
                },
                rainbow: {
                    red: '#FF0000',
                    orange: '#FF7F00',
                    yellow: '#FFFF00',
                    green: '#00FF00',
                    blue: '#0000FF',
                    indigo: '#4B0082',
                    violet: '#8B00FF',
                },
                wotDark: '#282828',
                darkCyan: '#08464c',
                darkBlue: '#132651',
                darkGrey: '#3c3c3c',
                lightGrey: '#f2f2f2',
                grey: '#daddde',
            },
            boxShadow: {
                'sun': '12px 12px theme("colors.sun.300")',
            },
            maxWidth: {
                '8xl': '90rem',
            },
            screens: {
                '3xl': '1660px'
            },
            zIndex: {
                '1000': '1000'
            }
        },
    },

    plugins: [forms],
};
