<?php

namespace App\Exports;

use App\Models\Package;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class FoodToursExport implements FromCollection, WithHeadings, WithMapping
{
    public function __construct(protected $foodtours)
    {
        
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $foodtours = Package::whereIn('id', $this->foodtours)->get();

        return $foodtours;
    }

    public function map($foodtour): array
    {
        return [
            $foodtour->type,
            $foodtour->getTranslation('title', 'el'), 
            strip_tags($foodtour->getTranslation('content', 'el')),
            $foodtour->duration,
            $foodtour->price,
            asset('storage/'.$foodtour->originalImage()),
        ];
    }

    public function headings(): array
    {
        return [
            'Είδος',
            'Τίτλος',
            'Περιγραφή',
            'Διάρκεια',
            'Τιμή',
            'Εικόνα',
        ];
    }
}
