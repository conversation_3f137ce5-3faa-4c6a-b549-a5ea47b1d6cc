<?php

namespace App\Console\Commands;

use App\Models\Tag;
use App\Models\Page;
use App\Models\Post;
use App\Models\Package;
use Spatie\Sitemap\Sitemap;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Spatie\Sitemap\SitemapGenerator;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-sitemap';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Sitemap';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        try {
            $path = public_path('sitemap.xml');

            $sitemap = Sitemap::create();

            // Define supported locales
            $locales = ['el', 'en']; // Add more locales if needed

            foreach ($locales as $locale) {
                if ($locale == 'el') {
                    // Set the application locale
                    app()->setLocale($locale);

                    // Add localized static routes
                    $sitemap->add(route("$locale.home"))
                        ->add(route("$locale.posts.index"));

                    // Add localized categories
                    Tag::category()->get()->each(function ($tag) use ($sitemap, $locale) {
                        $sitemap->add(route("$locale.category.show", $tag));
                    });

                    // Add localized travel packages
                    Package::travel()->published()->get()->each(function ($package) use ($sitemap, $locale) {
                        $sitemap->add(route("$locale.travel.show", $package));
                    });

                    // Add localized food packages
                    Package::food()->published()->get()->each(function ($package) use ($sitemap, $locale) {
                        $sitemap->add(route("$locale.foodtours.show", $package));
                    });

                    // Add localized tags
                    Tag::tag()->get()->each(function ($tag) use ($sitemap, $locale) {
                        $sitemap->add(route("$locale.tag.show", $tag));
                    });

                    // Add localized posts
                    Post::published()->get()->each(function ($post) use ($sitemap, $locale) {
                        $sitemap->add(route("$locale.posts.show", $post));
                    });

                    // Add localized pages
                    Page::published()->get()->each(function ($page) use ($sitemap, $locale) {
                        $sitemap->add(route("$locale.pages.show", $page));
                    });
                }
            }

            // Write the sitemap to a file
            $sitemap->writeToFile($path);

            $this->info('Localized sitemap generated successfully!');
        } catch (\Exception $e) {
            $this->error('Failed to generate sitemap: ' . $e->getMessage());
            Log::error('Sitemap generation failed', ['error' => $e->getMessage()]);
        }
    }
}
