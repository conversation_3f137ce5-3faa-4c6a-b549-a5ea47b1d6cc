<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class <PERSON><PERSON><PERSON><PERSON> extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:user {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates a new user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();

        if ( !empty($user) )
        {
            $this->info('Email already exists in users table. The password will be reset');
        }
        else
        {
            $user = new User();
        }

        $password   = $this->secret('What is the password?');

        $name       = $this->ask('What is the name?');

        $type       = $this->ask('What is the type? (1: enduser, 2: admin, 3: dev)');

        $user->email       = $email;
        $user->password    = Hash::make($password);
        $user->name        = $name;

        switch ($type)
        {
            case 1:
                $user->type = 'enduser';
                break;
            case 2:
                $user->type = 'admin';
                break;
            case 3:
                $user->type = 'dev';
                break;
            default:
                $user->type = 'enduser';
        }

        $user->save();

        $this->info('User stored! Goodbye.');

        return true;
    }
}
