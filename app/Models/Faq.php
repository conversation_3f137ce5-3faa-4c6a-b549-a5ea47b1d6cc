<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use App\Models\Traits\Scopes\VisibleScopes;
use App\Models\Traits\Relationships\FaqRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Faq extends Model
{
    use HasFactory,
        FaqRelationships,
        VisibleScopes,
        HasTranslations;

    public $translatable = [
        'question',
        'answer',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'question',
        'answer',
        'order',
        'featured',
        'published',
    ];

}
