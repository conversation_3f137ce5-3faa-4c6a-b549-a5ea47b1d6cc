<?php

namespace App\Models;

use App\Models\Traits\DeleteImageInstances;
use App\Models\Traits\Imageable;
use App\Models\Traits\Relationships\ShipFeatureRelationships;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ShipFeature extends Model
{
    use HasFactory,
        ShipFeatureRelationships,
        Imageable,
        HasTranslations,
        DeleteImageInstances;

    public $translatable = [
        'title',
        'content',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'content',
        'order',
        'ship_id',
    ];

}
