<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DepartureDate extends Model
{
    use HasFactory;

    protected $fillable = [
        'package_id',
        'date',
    ];

    public function packages():belongsToMany
    {
        return $this->belongsToMany(Package::class);
    }

    // public function date(): Attribute
    // {
    //     return Attribute::make(
    //         get: function($date){
    //             return Carbon::parse($date)->translatedFormat('d F');
    //         }
    //     );
    // }
}
