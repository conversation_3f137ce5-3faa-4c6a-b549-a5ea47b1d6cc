<?php

namespace App\Models;

use App\Models\Traits\Scopes\VisibleScopes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Menu extends Model
{
    use HasFactory,
        VisibleScopes;

    protected $fillable = [
        'title',
        'name',
        'locale',
        'published',
    ];

    public function menuItems(): HasMany
    {
        return $this->hasMany(MenuItem::class);
    }

}
