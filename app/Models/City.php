<?php

namespace App\Models;

use App\Models\Traits\Aliasable;
use App\Models\Traits\AutoAliasable;
use App\Models\Traits\Relationships\CityRelationships;
use Illuminate\Database\Eloquent\Model;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class City extends Model
{
    use CityRelationships,
        Aliasable,
        AutoAliasable,
        HasRelationships;

    protected $fillable = [
        'name',
        'region_id',
    ];
}
