<?php

namespace App\Models;

use App\Models\Traits\Aliasable;
use App\Models\Traits\AutoAliasable;
use App\Models\Traits\Relationships\ContinentRelationships;
use Illuminate\Database\Eloquent\Model;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Continent extends Model
{
    use ContinentRelationships,
        Aliasable,
        AutoAliasable,
        HasRelationships;

    protected $fillable = [
        'name',
    ];

}
