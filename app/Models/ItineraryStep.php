<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use App\Models\Traits\Relationships\ItineraryStepRelationships;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ItineraryStep extends Model
{
    use HasFactory,
        ItineraryStepRelationships,
        HasTranslations;

    public $translatable = [
        'title',
        'content',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'content',
        'order',
        'package_id',
    ];

    // public function order():Attribute
    // {
    //     return Attribute::make(
    //         set: function(){
    //             return static::max('order') + 1;
    //         }
    //     );
    // }

}
