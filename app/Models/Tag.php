<?php

namespace App\Models;

use App\Models\Traits\DeleteImageInstances;
use App\Models\Traits\Imageable;
use App\Models\Traits\PageBannerable;
use Carbon\Carbon;
use Spatie\Sitemap\Tags\Url;
use Spatie\Sluggable\SlugOptions;
use App\Models\Traits\Scopes\TagScopes;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sluggable\HasTranslatableSlug;
use App\Models\Traits\Scopes\VisibleScopes;
use App\Models\Traits\Relationships\TagRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Tag extends Model implements Sitemapable
{
    use HasFactory,
        TagRelationships,
        TagScopes,
        VisibleScopes,
        Imageable,
        PageBannerable,
        HasTranslations,
        HasTranslatableSlug,
        DeleteImageInstances;

    public $translatable = [
        'title',
        'slug',
        'content',
        'meta_title',
        'meta_description',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'slug',
        'content',
        'description',
        'meta_title',
        'meta_description',
        'type',
        'featured',
        'order',
        'template'
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions() : SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    public function toSitemapTag(): Url | string | array
    {
        $routeName = $this->type == 'tag' ? 'tag' : 'category';

        // Return with fine-grained control:
        return Url::create(route(app()->getLocale() . '.'.$routeName.'.show', $this))
            ->setLastModificationDate(Carbon::create($this->updated_at))
            ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
            ->setPriority(0.1);
    }
}
