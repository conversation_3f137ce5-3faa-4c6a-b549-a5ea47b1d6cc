<?php

namespace App\Models;

use App\Models\Traits\Aliasable;
use App\Models\Traits\AutoAliasable;
use App\Models\Traits\Relationships\CountryRelationships;
use Illuminate\Database\Eloquent\Model;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Country extends Model
{
    use CountryRelationships,
        Aliasable,
        AutoAliasable,
        HasRelationships;

    protected $fillable = [
        'name',
        'subregion_id',
        'iso_code',
    ];

}
