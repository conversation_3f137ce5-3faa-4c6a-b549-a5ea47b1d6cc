<?php

namespace App\Models;

use App\Models\Traits\DeleteImageInstances;
use App\Models\Traits\Galleriable;
use App\Models\Traits\Relationships\ShipRelationships;
use App\Models\Traits\Imageable;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\Sluggable\HasTranslatableSlug;
use App\Models\Traits\Scopes\VisibleScopes;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Ship extends Model
{
    use HasFactory,
        SoftDeletes,
        VisibleScopes,
        ShipRelationships,
        Imageable,
        Galleriable,
        HasTranslations,
        HasTranslatableSlug,
        DeleteImageInstances;

    public $translatable = [
        'title',
        'slug',
        'content',
        'meta_title',
        'meta_description',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'slug',
        'content',
        'video_id',
        'meta_title',
        'meta_description',
        'published',
        'featured',
    ];

    protected $with = 'features';



    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

}
