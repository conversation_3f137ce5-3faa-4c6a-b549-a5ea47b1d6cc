<?php

namespace App\Models;

use App\Models\Traits\Imageable;
use App\Models\Traits\Relationships\ImageGalleryRelationships;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use App\Models\Traits\Scopes\VisibleScopes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ImageGallery extends Model
{
    use HasFactory,
        VisibleScopes,
        ImageGalleryRelationships,
        Imageable,
        HasTranslations;

    protected $fillable = [
        'published',
        'featured',
        'title',
        'content',  
        'order',
        'galleriable_id',
        'galleriable_type',
    ];

    public $translatable = [
        'title',
        'content',
    ];

}
