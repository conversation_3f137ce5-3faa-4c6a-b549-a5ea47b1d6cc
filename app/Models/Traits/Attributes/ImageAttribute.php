<?php

namespace App\Models\Traits\Attributes;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Intervention\Image\Laravel\Facades\Image as InterImage;

use function Illuminate\Support\defer;

trait ImageAttribute
{

    /**
     * image folder is YYYY/MM
     * Store initial image in folder 
     * Store different size copies inside respective folders lg, md, sm
     * lg w=1920px
     * md w=800px
     * sm w=4600px
     */

    public function filename(): Attribute
    {

        return Attribute::make(
            set: function ($image) {
                $isBanner = false;
                $isFeature = false;
                $isGallery = false;
    
                // Create base folder based on model
                if ($this->attributes['imageable_type'] == "App\Models\Post") {
                    $baseFolder = 'posts';
                } elseif ($this->attributes['imageable_type'] == 'App\Models\Package') {
                    $baseFolder = 'packages';
                } elseif ($this->attributes['imageable_type'] == 'App\Models\Page') {
                    $baseFolder = 'pages';
                } elseif ($this->attributes['imageable_type'] == 'App\Models\Ship') {
                    $baseFolder = 'ships';
                } elseif ($this->attributes['imageable_type'] == 'App\Models\Banner') {
                    $baseFolder = 'banners';
                    $isBanner = true;
                } elseif ($this->attributes['imageable_type'] == 'App\Models\ShipFeature') {
                    $baseFolder = 'ships/features';
                    $isFeature = true;
                } elseif ($this->attributes['imageable_type'] == 'App\Models\Tag') {
                    $baseFolder = 'tags';
                } elseif ($this->attributes['imageable_type'] == 'App\Models\ImageGallery') {
                    $baseFolder = 'galleries/'.$this->attributes['imageable_id'];
                    $isGallery = true;
                }
                 else {
                    $baseFolder = 'packages';
                }

                if (!$isBanner && !$isFeature && !$isGallery) {

                    $folder =  $baseFolder . '/' . Carbon::now()->year . '/' . Carbon::now()->month;

                    // Filenames are the transliterated original filename with a random string added along with a size index, to avoid duplication 
                    $originalFilename = $image->getClientOriginalName();
                    $filenameWithoutExtension = Str::of(pathinfo($originalFilename, PATHINFO_FILENAME))->replace(' ', '-')->ascii();
                    $extension = $image->getClientOriginalExtension();
                    $imageable_id = $this->attributes['imageable_id'];

                    $baseFileName = $filenameWithoutExtension . '-' . $imageable_id;

                    $filename = $baseFileName . '.' . $extension;
                } elseif ($isBanner) {
                    $originalFilename = $image->getClientOriginalName();
                    $filenameWithoutExtension = Str::of(pathinfo($originalFilename, PATHINFO_FILENAME));
                    $extension = $image->getClientOriginalExtension();
                    $filename = $filenameWithoutExtension . '-' . $this->attributes['imageable_id'] . '.' . $extension;
                    $folder = $baseFolder;
                } elseif ($isFeature) {
                    $filename = 'ship-feature-'.$this->attributes['imageable_id'].'.'.$image->getClientOriginalExtension();
                    $folder = $baseFolder;
                } elseif ($isGallery) {
                    $filename = $image->getClientOriginalName();
                    $folder = $baseFolder;
                }

                // Store initial image
                $image->storeAs($folder . '/' . $filename);

                if (!$isBanner && !$isFeature && !$isGallery) {
                    // Check if lg directory exists and create it if not
                    $lgDirectory = $folder . '/lg';
                    if (!Storage::exists($lgDirectory)) {
                        Storage::makeDirectory($lgDirectory, 755, true);
                    }

                    // Check if md directory exists and create it if not
                    $mdDirectory = $folder . '/md';
                    if (!Storage::exists($mdDirectory)) {
                        Storage::makeDirectory($mdDirectory, 755, true);
                    }

                    // Check if sm directory exists and create it if not
                    $smDirectory = $folder . '/sm';
                    if (!Storage::exists($smDirectory)) {
                        Storage::makeDirectory($smDirectory, 755, true);
                    }


                    // Create 3 different aspects of the image: large, medium, small, and save them to corresponding folders

                    $image = InterImage::read('storage/' . $folder . '/' . $filename);

                    $image->scaleDown(width: 1920)
                        ->save('storage/' . $lgDirectory . '/' . $filename);


                    $image->scaleDown(width: 800)
                        ->save('storage/' . $mdDirectory . '/' . $filename);


                    $image->scaleDown(width: 460)
                        ->save('storage/' . $smDirectory . '/' . $filename);
                }
                return $filename;
            }
        );
    }
}
