<?php

namespace App\Models\Traits;

use Illuminate\Support\Facades\Storage;

trait DeleteImageInstances
{
    public function deleteMainImage()
    {
        Storage::delete('storage/', $this->originalImage());
        Storage::delete('storage/', $this->imageSm());
        Storage::delete('storage/', $this->imageMd());
        Storage::delete('storage/', $this->imageLg());
    }

    public function deletePricelistImage()
    {
        Storage::delete('storage/', $this->originalPricelistImage());
        Storage::delete('storage/', $this->pricelistImageSm());
        Storage::delete('storage/', $this->pricelistImageMd());
        Storage::delete('storage/', $this->pricelistImageLg());
    }
}
