<?php

namespace App\Models\Traits;

use Carbon\Carbon;
use App\Models\Image;
use Illuminate\Support\Facades\App;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait Imageable
{
    /**
     * Get all of the model's images.
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable');
    }

    /**
     * Get all of the model's gallery images. (not main)
     */
    public function galleryImages(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable')->where('main', 0);
    }


    /**
     * Get the main image
     */
    public function mainImage()
    {
        return $this->images->where('main', 1)->first();
    }

    /**
     * Get the pricelist image
     */
    public function pricelistImage()
    {
        return $this->images->where('pricelist', 1)->first();
    }

    /**
     * Get the banner image - Also used fore ship features image
     */
    public function bannerImage()
    {
        return $this->images->first();
    }


    /**
     * Get the original image
     */

    public function originalImage()
    {
        if ($this->mainImage()) {
            return $this->table . '/' . Carbon::parse($this->mainImage()->updated_at)->year . '/' . Carbon::parse($this->mainImage()->updated_at)->month . '/' . $this->mainImage()?->filename;
        } else {
            return 'packages' . '/' . Carbon::parse($this->updated_at)->year . '/' . Carbon::parse($this->updated_at)->month . '/' . $this->filename;
        }
    }

    /**
     * Get the small(sm) image
     */
    public function imageSm()
    {

        if ($this->mainImage()) {
            return $this->table . '/' . Carbon::parse($this->mainImage()->updated_at)->year . '/' . Carbon::parse($this->mainImage()->updated_at)->month . '/sm/' . $this->mainImage()?->filename;
        } else {
            return 'packages' . '/' . Carbon::parse($this->updated_at)->year . '/' . Carbon::parse($this->updated_at)->month . '/sm/' . $this->filename;
        }
    }

    /**
     * Get the medium(md) image
     */
    public function imageMd()
    {
        if ($this->mainImage()) {
            return $this->table . '/' . Carbon::parse($this->mainImage()->updated_at)->year . '/' . Carbon::parse($this->mainImage()->updated_at)->month . '/md/' . $this->mainImage()?->filename;
        } else {
            return 'packages' . '/' . Carbon::parse($this->updated_at)->year . '/' . Carbon::parse($this->updated_at)->month . '/md/' . $this->filename;
        }
    }

    /**
     * Get the large(lg) image
     */
    public function imageLg()
    {
        if ($this->mainImage()) {
            return $this->table . '/' . Carbon::parse($this->mainImage()->updated_at)->year . '/' . Carbon::parse($this->mainImage()->updated_at)->month . '/lg/' . $this->mainImage()?->filename;
        } else {
            return 'packages' . '/' . Carbon::parse($this->updated_at)->year . '/' . Carbon::parse($this->updated_at)->month . '/lg/' . $this->filename;
        }
    }


    /**
     * Get the alt tag of the main image
     */
    public function alt()
    {
        if ($this->mainImage()) {
            return $this->mainImage()->getTranslation('alt', App::getLocale()) ? $this->mainImage()->getTranslation('alt', App::getLocale()) : $this->getTranslation('title', App::getLocale());
        }

        return $this->getTranslation('title', App::getLocale());
    }

    /**
     * Get the original pricelist image
     */

    public function originalPricelistImage()
    {
        if ($this->pricelistImage()) {
            return 'packages' . '/' . Carbon::parse($this->pricelistImage()->updated_at)->year . '/' . Carbon::parse($this->pricelistImage()->updated_at)->month . '/' . $this->pricelistImage()->filename;
        }
    }

    /**
     * Get the small(sm) pricelist image
     */
    public function pricelistImageSm()
    {
        if ($this->pricelistImage()) {
            return $this->table . '/' . Carbon::parse($this->pricelistImage()->updated_at)->year . '/' . Carbon::parse($this->pricelistImage()->updated_at)->month . '/sm/' . $this->pricelistImage()?->filename;
        } 
    }

    /**
     * Get the medium(md) pricelist image
     */
    public function pricelistImageMd()
    {
        if ($this->pricelistImage()) {
            return $this->table . '/' . Carbon::parse($this->pricelistImage()->updated_at)->year . '/' . Carbon::parse($this->pricelistImage()->updated_at)->month . '/md/' . $this->pricelistImage()?->filename;
        } 
    }

    /**
     * Get the large(lg) pricelist image
     */
    public function pricelistImageLg()
    {
        if ($this->pricelistImage()) {
            return $this->table . '/' . Carbon::parse($this->pricelistImage()->updated_at)->year . '/' . Carbon::parse($this->pricelistImage()->updated_at)->month . '/lg/' . $this->pricelistImage()?->filename;
        } 
    }
}
