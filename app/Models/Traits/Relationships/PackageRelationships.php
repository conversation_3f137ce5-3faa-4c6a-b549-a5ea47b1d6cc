<?php

namespace App\Models\Traits\Relationships;

use App\Models\City;
use App\Models\Continent;
use App\Models\Country;
use App\Models\Faq;
use App\Models\Ship;
use App\Models\Seoterm;
use App\Models\Subregion;
use App\Models\DepartureDate;
use App\Models\ItineraryStep;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;

trait PackageRelationships
{
    /**
     * Get the itinerary steps for the holiday package.
     */
    public function itinerary_steps(): HasMany
    {
        return $this->hasMany(ItineraryStep::class)->orderBy('order', 'asc');
    }

    /**
     * Get all of the faqs for the package.
     */
    public function faqs(): MorphToMany
    {
        return $this->morphToMany(Faq::class, 'faqable')
            ->withTimestamps();
    }

    /**
     * A package can have many departure dates
     */
    public function departDates(): HasMany
    {
        return $this->hasMany(DepartureDate::class);
    }

    /**
     * A package can have many ships
     */
    public function ships(): BelongsToMany
    {
        return $this->belongsToMany(Ship::class);
    }

    /**
     * Get the seo term for the package.
     */
    public function seoTerm(): MorphOne
    {
        return $this->morphOne(Seoterm::class, 'seoable');
    }

    /**
     * The cities that are assigned to the package
     */
    public function cities()
    {
        return $this->belongsToMany(City::class)
            ->withTimestamps();
    }

    /**
     * Get the regions associated with the package through cities.
     * Uses HasManyDeep for deep relationship: Package -> City -> Region
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasManyDeep
     */
    public function regions(): HasManyDeep
    {
        return $this->hasManyDeepFromRelations(
            $this->cities(),
            (new City())->region()
        );
    }

    /**
     * Get the countries associated with the package through cities.
     * Uses HasManyDeep for deep relationship: Package -> City -> Region -> Country
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasManyDeep
     */
//    public function countries(): HasManyDeep
//    {
//        return $this->hasManyDeepFromRelations(
//            $this->cities(),
//            (new City())->country()
//        );
//    }
    public function countries(): HasManyDeep
    {
        return $this->hasManyDeepFromReverse(
            (new Country())->packages()
        );
    }

    /**
     * Get the subregions associated with the package through cities.
     */
    public function subregions()
    {
        return $this->hasManyDeepFromReverse(
            (new Subregion())->packages()
        );
    }

    /**
     * Get the continents associated with the package through cities.
     */
    public function continents()
    {
        return $this->hasManyDeepFromReverse(
            (new Continent())->packages()
        );
    }
}
