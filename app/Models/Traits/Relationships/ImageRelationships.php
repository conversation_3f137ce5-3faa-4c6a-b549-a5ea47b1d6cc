<?php

namespace App\Models\Traits\Relationships;

use App\Models\Banner;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

trait ImageRelationships
{

    /**
     * Get the parent imageable model.
     */
    public function imageable(): MorphTo
    {
        return $this->morphTo();
    }


    /**
     * Get the imageable banner 
     */
    public function banner()
    {
        return $this->imageable()->where('type', 'banner')->first();
    }

    /**
     * Get the imageable slider 
     */
    public function slider()
    {
        return $this->imageable()->where('type', 'slider')->first();
    }

    /**
     * Get the imageable slider 
     */
    public function popup()
    {
        return $this->imageable()->where('type', 'popup')->first();
    }
}
