<?php

namespace App\Models\Traits\Relationships;

use App\Models\Faq;
use App\Models\Tag;
use App\Models\Seoterm;
use Illuminate\Support\Facades\App;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait PostRelationships
{

    /**
     * Get all of the faqs for the post.
     */
    public function faqs(): MorphToMany
    {
        return $this->morphToMany(Faq::class, 'faqable')
            ->withTimestamps();
    }

    /**
     * Get the next post
     */
    public function nextPost()
    {
        return self::published()->where('id', '>', $this->id)->whereLocale('title', App::getLocale())->first() ?? null;
    }


    /**
     * Get the previous post
     */
    public function previousPost()
    {
        return self::published()->where('id', '<', $this->id)->whereLocale('title', App::getLocale())->orderBy('id', 'desc')->first() ?? null;
    }

    public function seoTerm(): MorphOne
    {
        return $this->morphOne(Seoterm::class, 'seoable');
    }

}
