<?php

namespace App\Models\Traits\Relationships;

use App\Models\City;
use App\Models\Region;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;
use App\Models\Subregion;
use App\Models\Continent;

trait CountryRelationships
{
    /**
     * Get the continent associated with the country through the subregion.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function continent(): HasOneThrough
    {
        return $this->hasOneThrough(
            Continent::class,   // Final model
            Subregion::class,   // Intermediate model
            'id',              // Foreign key on intermediate model (subregions.id)
            'id',              // Foreign key on final model (continents.id)
            'subregion_id',    // Local key on this model (countries.subregion_id)
            'continent_id'     // Local key on intermediate model (subregions.continent_id)
        );
    }

    /**
     * Get the subregion associated with the country.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function subregion(): BelongsTo
    {
        return $this->belongsTo(Subregion::class);
    }

    /**
     * Get the regions for the country.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function regions(): HasMany
    {
        return $this->hasMany(Region::class);
    }

    /**
     * Get the cities for the country through regions.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function cities(): HasManyThrough
    {
        return $this->hasManyThrough(City::class, Region::class);
    }

    /**
     * The packages that are assigned to the country through cities.
     * Uses HasManyDeep for deep relationship: Country -> Region -> City -> Package
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasManyDeep
     */
    public function packages(): HasManyDeep
    {
        return $this->hasManyDeepFromRelations(
            $this->cities(),
            (new City())->packages()
        );
    }
}
