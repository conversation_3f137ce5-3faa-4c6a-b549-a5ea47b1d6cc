<?php

namespace App\Models\Traits\Relationships;

use App\Models\Ship;
use App\Models\Package;
use App\Models\Seoterm;
use App\Models\ShipFeature;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait ShipRelationships
{
    /**
     * Get the itinerary steps for the holiday package.
     */
    public function features(): HasMany
    {
        return $this->hasMany(ShipFeature::class);
    }


    /**
     * A ship belongs to many Packages
     */

    public function packages(): BelongsToMany
    {
        return $this->belongsToMany(Package::class);
    }

    public function seoTerm(): MorphOne
    {
        return $this->morphOne(Seoterm::class, 'seoable');
    }

}
