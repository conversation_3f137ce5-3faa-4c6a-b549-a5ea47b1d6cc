<?php

namespace App\Models\Traits\Relationships;


use App\Models\Tag;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait PageBannerRelationships
{

    /**
     * The categories that are assigned to the page banner.
     */
    public function categories(): MorphToMany
    {
        return $this->morphedByMany(Tag::class, 'page_bannerable')
            ->where('type', 'category')
            ->withPivot('order')
            ->withTimestamps();
    }


    /**
     * The tags that are assigned to the page banner.
     */
    public function tags(): MorphToMany
    {
        return $this->morphedByMany(Tag::class, 'page_bannerable')
            ->where('type', 'tag')
            ->withPivot('order')
            ->withTimestamps();
    }

}
