<?php

namespace App\Models\Traits\Relationships;

use App\Models\Tag;
use App\Models\Post;
use App\Models\Package;
use App\Models\MenuItem;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait FaqRelationships
{
    /**
     * The posts that are assigned to the faq.
     */
    public function posts(): MorphToMany
    {
        return $this->morphedByMany(Post::class, 'faqable');
    }

    /**
     * The packages that are assigned to the faq.
     */
    public function packages(): MorphToMany
    {
        return $this->morphedByMany(Package::class, 'faqable');
    }

    /**
     * The tags that are assigned to the faq.
     */
    public function tags(): MorphToMany
    {
        return $this->morphedByMany(Tag::class, 'faqable');
    }


}
