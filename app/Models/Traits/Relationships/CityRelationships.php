<?php

namespace App\Models\Traits\Relationships;

use App\Models\Package;
use App\Models\Region;
use App\Models\Country;
use App\Models\Subregion;
use App\Models\Continent;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Staudenmeir\EloquentHasManyDeep\HasOneDeep;

trait CityRelationships
{
    /**
     * Get the region that owns the city.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function region(): BelongsTo
    {
        return $this->belongsTo(Region::class);
    }

    /**
     * Get the country associated with the city through the region.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function country(): HasOneThrough
    {
        return $this->hasOneThrough(
            Country::class,     // Final model
            Region::class,      // Intermediate model
            'id',              // Foreign key on intermediate model (regions.id)
            'id',              // Foreign key on final model (countries.id)
            'region_id',       // Local key on this model (cities.region_id)
            'country_id'       // Local key on intermediate model (regions.country_id)
        );
    }

    /**
     * Get the subregion associated with the city through the region and country.
     * Uses HasOneDeep for deep relationship: City -> Region -> Country -> Subregion
     * Taking the reverse of Subregion -> Country -> Region -> City
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasOneDeep
     */
    public function subregion(): HasOneDeep
    {
        return $this->hasOneDeepFromReverse(
            (new Subregion())->cities()
        );
    }

    /**
     * Get the continent associated with the city through the region, country, and subregion.
     * Uses HasOneDeep for deep relationship: City -> Region -> Country -> Subregion -> Continent
     * Taking the reverse of Continent -> Subregion -> Country -> Region -> City
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasOneDeep
     */
    public function continent(): HasOneDeep
    {
        return $this->hasOneDeepFromReverse(
            (new Continent())->cities()
        );
    }

    /**
     * The packages that are assigned to the city.
     */
    public function packages(): BelongsToMany
    {
        return $this->belongsToMany(Package::class)
            ->withTimestamps();
    }
}
