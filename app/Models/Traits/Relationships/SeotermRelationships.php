<?php 

namespace App\Models\Traits\Relationships;

use App\Models\Tag;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait SeotermRelationships
{
    /**
     * The posts that are assigned to the term.
     */
    public function seoable(): MorphTo
    {
        return $this->morphTo();
    }
}