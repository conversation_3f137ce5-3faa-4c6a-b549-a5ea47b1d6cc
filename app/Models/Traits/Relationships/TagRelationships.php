<?php

namespace App\Models\Traits\Relationships;

use App\Models\Faq;
use App\Models\Post;
use App\Models\Package;
use App\Models\Seoterm;
use App\Models\PageBanner;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait TagRelationships
{
    /**
     * The posts that are assigned to the tag.
     */
    public function posts(): MorphToMany
    {
        return $this->morphedByMany(Post::class, 'taggable')
            ->withPivot('order')
            ->withTimestamps();
    }

    /**
     * The travel packages that are assigned to the tag 
     */
    public function packages(): MorphToMany
    {
        return $this->morphedByMany(Package::class, 'taggable')
            ->withPivot('order')
            ->withTimestamps();
    }

    /**
     * The food packages that are assigned to the tag 
     */
    public function foodtours(): MorphToMany
    {
        return $this->morphedByMany(Package::class, 'taggable')
            ->where('type', 'food')
            ->withPivot('order')
            ->withTimestamps();
    }

    /**
     * Get all of the faqs for the post.
     */
    public function faqs(): MorphToMany
    {
        return $this->morphToMany(Faq::class, 'faqable')
            ->withTimestamps();
    }

    /**
     * The banners that are assigned to the tag 
     */
    public function banners(): MorphToMany
    {
        return $this->morphToMany(PageBanner::class, 'page_bannerable')
            ->withPivot('order')
            ->withTimestamps();
    }

    public function seoTerm(): MorphOne
    {
        return $this->morphOne(Seoterm::class, 'seoable');
    }
}
