<?php

namespace App\Models\Traits\Relationships;

use App\Models\City;
use App\Models\Country;
use App\Models\Region;
use App\Models\Subregion;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;

trait ContinentRelationships
{
    /**
     * Get the subregions for the continent.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function subregions(): HasMany
    {
        return $this->hasMany(Subregion::class);
    }

    /**
     * Get the countries for the continent.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function countries(): HasManyThrough
    {
        return $this->hasManyThrough(Country::class, Subregion::class);
    }

    /**
     * Get the regions for the continent through subregions and countries.
     * Uses HasManyDeep for deep relationship: Continent -> Subregion -> Country -> Region
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasManyDeep
     */
    public function regions(): HasManyDeep
    {
        return $this->hasManyDeep(
            Region::class,
            [Subregion::class, Country::class]
        );
    }

    /**
     * Get the cities for the continent through subregions, countries, and regions.
     * Uses HasManyDeep for deep relationship: Continent -> Subregion -> Country -> Region -> City
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasManyDeep
     */
    public function cities(): HasManyDeep
    {
        return $this->hasManyDeep(
            City::class,
            [Subregion::class, Country::class, Region::class]
        );
    }

    /**
     * Get the packages for the continent through cities.
     * Uses HasManyDeep for deep relationship: Continent -> Subregion -> Country -> Region -> City -> Package
     *
     * @return \Staudenmeir\EloquentHasManyDeep\HasManyDeep
     */
    public function : HasManyDeep
    {
        return $this->hasManyDeepFromRelations(
            $this->cities(),
            (new City())->packages()
        );
    }
}
