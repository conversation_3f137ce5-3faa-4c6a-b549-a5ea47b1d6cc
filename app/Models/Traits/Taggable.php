<?php

namespace App\Models\Traits;

use App\Models\Tag;
use Illuminate\Support\Facades\App;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait Taggable
{
    /**
     * Get all of the tags for the model.
     */
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable')
            ->where('type', 'tag')
            ->whereLocale('title', App::getLocale())
            ->withTimestamps();
    }

    /**
     * Get all of the categories for the model.
     */
    public function categories(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable')
            ->where('type', 'category')
            ->whereLocale('title', App::getLocale())
            ->withTimestamps();
    }

}
