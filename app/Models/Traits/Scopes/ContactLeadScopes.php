<?php

namespace App\Models\Traits\Scopes;

trait ContactLeadScopes
{
    /**
     * Scope a query to only include models that are of type travel.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTravel($query)
    {
        return $query->where('message_type', 'travel');
    }

    /**
     * Scope a query to only include models that are of type food.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFood($query)
    {
        return $query->where('message_type', 'food');
    }
    /**
     * Scope a query to only include models that are of type contact.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeContact($query)
    {
        return $query->where('message_type', 'contact');
    }
}
