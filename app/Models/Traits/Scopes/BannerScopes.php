<?php 

namespace App\Models\Traits\Scopes;

trait BannerScopes
{
    /**
     * Scope a query to only include banners that are of type banner.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBanner($query)
    {
        return $query->where('type', 'banner');
    }

    /**
     * Scope a query to only include banners that are of type hero.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSlider($query)
    {
        return $query->where('type', 'slider');
    }

    /**
     * Scope a query to only include banners that are of type popup.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePopup($query)
    {
        return $query->where('type', 'popup');
    }

}
