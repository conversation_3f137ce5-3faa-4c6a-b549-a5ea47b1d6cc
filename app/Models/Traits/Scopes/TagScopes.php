<?php

namespace App\Models\Traits\Scopes;

trait TagScopes
{
    /**
     * Scope a query to only include models that are of type category.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCategory($query)
    {
        return $query->where('type', 'category');
    }

    /**
     * Scope a query to only include models that are of type tag.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTag($query)
    {
        return $query->where('type', 'tag');
    }

}
