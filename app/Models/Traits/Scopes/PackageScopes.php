<?php

namespace App\Models\Traits\Scopes;

trait PackageScopes
{
    /**
     * Scope a query to only include models that are of type travel.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTravel($query)
    {
        return $query->where('type', 'travel');
    }

    /**
     * Scope a query to only include models that are of type food.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFood($query)
    {
        return $query->where('type', 'food');
    }

    /**
     * Scope a query to only include models that are of type cruise.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCruise($query)
    {
        return $query->where('type', 'cruise');
    }

}
