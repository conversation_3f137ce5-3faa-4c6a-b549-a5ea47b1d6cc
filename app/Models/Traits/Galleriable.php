<?php

namespace App\Models\Traits;

use App\Models\ImageGallery;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

trait Galleriable
{

    /**
     * Get all of the model's galleries.
     */
    public function galleries(): MorphMany
    {
        return $this->morphMany(ImageGallery::class, 'galleriable');
    }


    /**
     * Get the model's most recent gallery.
     */
    public function latestGallery(): MorphOne
    {
        return $this->morphOne(ImageGallery::class, 'galleriable')->latestOfMany();
    }
}
