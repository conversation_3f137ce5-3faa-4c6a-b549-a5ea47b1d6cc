<?php

namespace App\Models\Traits;

use App\Models\PageBanner;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait PageBannerable
{
    /**
     * Get all of the page banners for the model.
     */
    public function page_banners(): MorphToMany
    {
        return $this->morphToMany(PageBanner::class, 'page_bannerable')
            ->withPivot('order')
            ->withTimestamps();
    }

}
