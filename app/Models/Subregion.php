<?php

namespace App\Models;

use App\Models\Traits\Aliasable;
use App\Models\Traits\Relationships\SubregionRelationships;
use Illuminate\Database\Eloquent\Model;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Subregion extends Model
{
    use SubregionRelationships,
        Aliasable,
        HasRelationships;

    protected $fillable = [
        'name',
        'continent_id',
    ];

}
