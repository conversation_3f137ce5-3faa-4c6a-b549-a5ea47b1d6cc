<?php

namespace App\Models;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Scopes\ContactLeadScopes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ContactLead extends Model
{
    use HasFactory, 
        SoftDeletes,
        ContactLeadScopes;
        

    protected $fillable = [
        'package_id',
        'depart_date',
        'first_name',
        'last_name',
        'email',
        'mobile',
        'address',
        'city',
        'persons',
        'kids',
        'subject',
        'message',
        'message_type',
        'contact_by',
    ];

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }


    public function departDate(): Attribute
    {
        return Attribute::make(
            set: function($date){
                return Carbon::parse($date)->format('Y-m-d');
            },
            get: function($date) {
                return Carbon::parse(($date))->format('m-d-Y');
            }
        );
    }

    public function fullName()
    {
        return $this->first_name .' '. $this->last_name;
    }
}
