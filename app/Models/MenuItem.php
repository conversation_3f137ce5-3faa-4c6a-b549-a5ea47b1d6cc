<?php

namespace App\Models;

use App\Models\Traits\Scopes\MenuItemScopes;
use App\Models\Traits\Scopes\VisibleScopes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MenuItem extends Model
{
    use HasFactory,
        VisibleScopes,
        MenuItemScopes;

    protected $fillable = [
        'menu_id',
        'title',
        'type',
        'link',
        'parent_id',
        'page_title',
        'meta_title',
        'meta_description',
        'order',
        'settings',
        'published',
    ];

    public function menu() :BelongsTo
    {
        return $this->belongsTo(Menu::class);
    }


    /**
     * Scope for the children Menu Items
     */
    public function scopeChildren()
    {
        return static::published()->where('parent_id', $this->id)->get();
    }


    /**
     * Checks if there are children menu items
     */
    public function hasChildren(): bool
    {
        return $this->children()->count() > 0;
    }


}
