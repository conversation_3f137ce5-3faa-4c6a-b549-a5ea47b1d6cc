<?php

namespace App\Models;

use App\Models\Traits\DeleteImageInstances;
use Carbon\Carbon;
use Spatie\Sitemap\Tags\Url;
use App\Models\Traits\Imageable;
use App\Models\Traits\Relationships\PageRelationships;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sluggable\HasTranslatableSlug;
use App\Models\Traits\Scopes\VisibleScopes;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Page extends Model implements Sitemapable
{
    use HasFactory,
        SoftDeletes,
        VisibleScopes,
        Imageable,
        PageRelationships,
        HasTranslations,
        HasTranslatableSlug,
        DeleteImageInstances;

    protected $fillable = [
        'published',
        'title',
        'slug',
        'meta_title',
        'meta_description',
        'content',
    ];


    public $translatable = [
        'title',
        'slug',
        'content',
        'meta_title',
        'meta_description',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    public function toSitemapTag(): Url | string | array
    {

        // Return with fine-grained control:
        return Url::create(route(app()->getLocale() . '.pages.show', $this))
            ->setLastModificationDate(Carbon::create($this->updated_at))
            ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
            ->setPriority(0.1);
    }
}
