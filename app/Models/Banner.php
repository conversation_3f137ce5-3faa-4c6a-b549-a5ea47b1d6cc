<?php

namespace App\Models;


use App\Models\Traits\Imageable;
use App\Models\Traits\Scopes\BannerScopes;
use App\Models\Traits\Scopes\VisibleScopes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Banner extends Model
{
    use HasFactory, 
        VisibleScopes,
        BannerScopes,
        Imageable,
        HasTranslations;

    protected  $fillable = [
        'published',
        'image_id',
        'type',
        'title',
        'content',
        'url',
        'description'
    ];

    public $translatable = [
        'title',
        'content',
        'url',
    ];
}
