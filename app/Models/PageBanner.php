<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Spatie\Translatable\HasTranslations;
use App\Models\Traits\Scopes\VisibleScopes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use App\Models\Traits\Relationships\PageBannerRelationships;

class PageBanner extends Model
{
    use VisibleScopes,
        PageBannerRelationships,
        HasTranslations;

    protected  $fillable = [
        'published',
        'title',
        'content',
        'url',
        'filename',
        'alt',
        'description',
    ];

    public $translatable = [
        'title',
        'content',
        'url',
        'alt',
    ];

    // Image mutator
    public function filename(): Attribute
    {
        return Attribute::make(
            set: function ($image) {
                if (isset($image)) {

                    $folder = 'pagebanners';

                    if (!Storage::exists($folder)) {
                        Storage::makeDirectory($folder, 755, true);
                    }

                    // Filenames are the transliterated original filename without spaces 
                    $originalFilename = $image->getClientOriginalName();
                    $filenameWithoutExtension = Str::of(pathinfo($originalFilename, PATHINFO_FILENAME))->replace(' ', '-')->ascii();
                    $extension = $image->getClientOriginalExtension();
                    $filename = $filenameWithoutExtension . '.' . $extension;

                    // Store banner image
                    $image->storeAs($folder . '/' . $filename);

                    return $filename;
                }
            }
        );
    }


    /**
     * While updating check if the image is updated with a different image and if it is, delete the old one.
     */
    protected static function booted(): void
    {
        self::updating(static function (PageBanner $page_banner): void {
            if ($page_banner->isDirty('filename')) {

                $old_image = $page_banner->getOriginal('filename');

                if ($old_image && $old_image !== $page_banner->filename && Storage::exists('pagebanners/' . $old_image)) {
                    Storage::delete('pagebanners/' . $old_image);
                }
            }
        });
    }
}
