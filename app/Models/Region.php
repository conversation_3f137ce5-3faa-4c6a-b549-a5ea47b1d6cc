<?php

namespace App\Models;

use App\Models\Traits\Aliasable;
use App\Models\Traits\AutoAliasable;
use App\Models\Traits\Relationships\RegionRelationships;
use Illuminate\Database\Eloquent\Model;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Region extends Model
{
    use RegionRelationships,
        Aliasable,
        AutoAliasable,
        HasRelationships;

    protected $fillable = [
        'name',
        'country_id',
    ];
}
