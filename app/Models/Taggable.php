<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Taggable extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'order',
    ];


    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class, 'taggable_id');
    }

}
