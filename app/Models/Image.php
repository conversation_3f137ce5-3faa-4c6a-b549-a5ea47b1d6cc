<?php

namespace App\Models;

use App\Models\Traits\Attributes\ImageAttribute;
use App\Models\Traits\Imageable;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Traits\Relationships\ImageRelationships;

class Image extends Model
{
    use HasFactory,
        SoftDeletes,
        ImageRelationships,
        ImageAttribute,
        HasTranslations;

    public $translatable = [
        'alt',
        'caption',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'alt',
        'caption',
        'order',
        'filename',
        'imageable_id',
        'imageable_type',
        'main',
        'pricelist',
    ];
    
}
