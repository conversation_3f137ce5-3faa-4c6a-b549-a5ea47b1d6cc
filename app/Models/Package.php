<?php

namespace App\Models;

use App\Models\Traits\DeleteImageInstances;
use App\Models\Traits\Galleriable;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Spatie\Sitemap\Tags\Url;
use App\Models\Traits\Taggable;
use App\Models\Traits\Imageable;
use Spatie\Searchable\Searchable;
use Spatie\Sluggable\SlugOptions;
use Spatie\Searchable\SearchResult;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sluggable\HasTranslatableSlug;
use App\Models\Traits\Scopes\PackageScopes;
use App\Models\Traits\Scopes\VisibleScopes;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Traits\Relationships\PackageRelationships;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Package extends Model implements Searchable, Sitemapable
{
    use HasFactory,
        SoftDeletes,
        VisibleScopes,
        PackageRelationships,
        PackageScopes,
        Taggable,
        Imageable,
        Galleriable,
        HasTranslations,
        HasTranslatableSlug,
        DeleteImageInstances,
        HasRelationships;

    public $translatable = [
        'title',
        'subtitle',
        'slug',
        'tagline',
        'content',
        'itinerary',
        'info',
        'included',
        'not_included',
        'meta_title',
        'meta_description',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'slug',
        'subtitle',
        'tagline',
        'content',
        'itinerary',
        'info',
        'included',
        'not_included',
        'meta_title',
        'meta_description',
        'published',
        'featured',
        'code',
        'price',
        'price_fixed',
        'duration',
        'location',
        'tour_type',
        'group_size',
        'tour_languages',
        'sku',
        'type',
        'created_at',
    ];

    protected $with = ['itinerary_steps', 'tags'];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    public function getSearchResult(): SearchResult
    {
        if( $this->type === 'food' )
        {
            $url = route('foodtours.show', $this->slug);
        } else
        {
            $url = route('travel.show', $this->slug);
        }

        return new \Spatie\Searchable\SearchResult(
            $this,
            $this->title,
            $url
        );
    }

    public function toSitemapTag(): Url | string | array
    {
        if( $this->type === 'food' )
        {
            $routeName = 'foodtours';
        } else
        {
            $routeName = 'travel';
        }

        // Return with fine-grained control:
        return Url::create(route(app()->getLocale() . '.'.$routeName.'.show', $this))
            ->setLastModificationDate(Carbon::create($this->updated_at))
            ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY)
            ->setPriority(0.1);
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    // public function getRouteKeyName()
    // {
    //     return 'slug';
    // }

    public function price(): Attribute
    {
        return Attribute::make(
            get: function ($price) {
                return $price / 100;
            },
            set: function ($price) {
                return (int) ((float)$price * 100);
            }
        );
    }

    public function excerpt($words): string
    {

        return strip_tags(Str::words($this->content, $words, '...'));
    }

    public function tourLanguages(): Attribute
    {
        return Attribute::make(
            get: function ($value) {

                if (!$value) {
                    return;
                }

                $languages = explode(', ', $value);


                foreach ($languages as $language) {
                    $array[] = __('base.' . $language);
                }

                return implode(', ', $array);
            }
        );
    }
}
