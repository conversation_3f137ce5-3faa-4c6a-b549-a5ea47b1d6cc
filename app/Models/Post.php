<?php

namespace App\Models;

use App\Models\Traits\DeleteImageInstances;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Spatie\Sitemap\Tags\Url;
use App\Models\Traits\Taggable;
use App\Models\Traits\Imageable;
use Spatie\Searchable\Searchable;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Support\Facades\App;
use Spatie\Searchable\SearchResult;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\Sitemap\Contracts\Sitemapable;
use Spatie\Sluggable\HasTranslatableSlug;
use App\Models\Traits\Scopes\VisibleScopes;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Relationships\PostRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Post extends Model implements Searchable, Sitemapable
{
    use HasFactory,
        SoftDeletes,
        VisibleScopes,
        PostRelationships,
        Taggable,
        Imageable,
        HasTranslations,
        HasTranslatableSlug,
        DeleteImageInstances;

    public $translatable = [
        'title',
        'slug',
        'tagline',
        'content',
        'meta_title',
        'meta_description',
        'alt',
        'caption'
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'slug',
        'tagline',
        'content',
        'meta_title',
        'meta_description',
        'published',
        'published_at',
        'featured',
        'created_at',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    public function getSearchResult(): SearchResult
    {
        $url = route('posts.show', $this->slug);

        return new \Spatie\Searchable\SearchResult(
            $this,
            $this->title,
            $url
        );
    }

    public function toSitemapTag(): Url | string | array
    {
      
        // Return with fine-grained control:
        return Url::create(route(app()->getLocale() .'.posts.show', $this))
            ->setLastModificationDate(Carbon::create($this->updated_at))
            ->setChangeFrequency(Url::CHANGE_FREQUENCY_YEARLY)
            ->setPriority(0.1);
    }


    public function excerpt($words): string
    {

        return strip_tags(Str::words($this->getTranslation('content', App::getLocale()), $words, '...'));
    }
}
