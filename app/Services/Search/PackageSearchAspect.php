<?php

namespace App\Services\Search;

use App\Models\Package;
use Spatie\Searchable\SearchAspect;
use Illuminate\Support\Collection;

class PackageSearchAspect extends SearchAspect
{
    public function getResults(string $term): Collection
    {
        return Package::published()
            ->where(function($query) use ($term) {
                $query->whereRaw('LOWER(title->>"$.'.app()->getLocale().'") COLLATE utf8mb4_unicode_ci LIKE ?', ['%' . strtolower($term) . '%'])
                ->orWhereRaw('LOWER(content->>"$.'.app()->getLocale().'") COLLATE utf8mb4_unicode_ci LIKE ?', ['%' . strtolower($term) . '%'])
                ->orWhere('sku', 'LIKE', '%'. strtolower($term) .'%' );
            })
            ->orderBy('created_at', 'desc')
            ->get();
    }

}
