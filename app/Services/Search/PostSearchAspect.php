<?php

namespace App\Services\Search;

use App\Models\Post;
use Spatie\Searchable\SearchAspect;
use Illuminate\Support\Collection;

class PostSearchAspect extends SearchAspect
{
    public function getResults(string $term): Collection
    {
        return Post::published()
            ->where(function($query) use ($term) {
                $query->whereRaw('LOWER(title->>"$.'.app()->getLocale().'") COLLATE utf8mb4_unicode_ci LIKE ?', ['%' . mb_strtolower($term) . '%'])
                ->orWhereRaw('LOWER(content->>"$.'.app()->getLocale().'") COLLATE utf8mb4_unicode_ci LIKE ?', ['%' . mb_strtolower($term) . '%']);
            })
            ->orderBy('created_at', 'desc')
            ->get();
    }

}
