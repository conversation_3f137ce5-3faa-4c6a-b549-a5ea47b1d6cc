<?php

namespace App\Services;

use Google\Cloud\Translate\V3\TranslationServiceClient;

class TranslationService
{

    public function translate($content)
    {
        $config = ['credentials' => storage_path('app/application_default_credentials.json')];

        $translationClient = new TranslationServiceClient($config);

        $targetLanguage = 'en';

        if ($content !== '') {
            $response = $translationClient->translateText(
                [$content],
                $targetLanguage,
                TranslationServiceClient::locationName(config('services.google.cloud_translate.project_id'), 'global')
            );

            foreach ($response->getTranslations() as $key => $translation) {
                return $translation->getTranslatedText();
            }
        }
    }
}
