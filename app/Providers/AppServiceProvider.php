<?php

namespace App\Providers;

use App\Models\Menu;
use App\Models\User;
use EchoLabs\Prism\Prism;
use App\Ai\Tools\LeadTool;
use App\Ai\Tools\PackageTool;
use EchoLabs\Prism\Enums\Provider;
use Illuminate\Support\Facades\App;
use EchoLabs\Prism\Enums\ToolChoice;
use Illuminate\Support\Facades\View;
use App\View\Composers\PopupComposer;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rules\Password;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Add a singleton for the OpenAI chatbot
        $this->app->singleton('prism', function () {
            return Prism::text()
                ->using(Provider::OpenAI, 'gpt-4o')
                ->withMaxSteps(8)
                ->withSystemPrompt(config('chat.system_role'))
                ->withTools([
                    new LeadTool(),
                    new PackageTool()
                ]);
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        Password::defaults(function () {
            $rule = Password::min(8);

            return $this->app->isProduction()
                ? $rule->mixedCase()->uncompromised()
                : $rule;
        });

        // resolve the admin var in routes as user models
        Route::bind('admin', function (string $value) {
            return User::where('id', $value)->firstOrFail();
        });


        View::composer('*', function ($view) {
            $viewName = $view->getName();

            // Check if the view name starts with 'moduleName::' or any other identifier

            if (!str_starts_with($viewName, 'cerberus::')) {

                $view->with('locale', App::currentLocale());
            }
        });

        view()->composer('layouts.main-menu', function ($view) {
            $menu = $this->getMenu()
                ->where('name', 'LIKE', '%nav-menu%')
                ->first();

            $view->with('menu', $menu);
        });

        view()->composer('layouts.footer', function ($view) {

            $footer_menu = $this->getMenu()
                ->where('name', 'LIKE', '%footer-menu%')               
                ->first();

            $company_menu = $this->getMenu()
                ->where('name', 'LIKE', '%footer-company-menu%')        
                ->first();

            $view->with(['footer_menu' => $footer_menu, 'company_menu' => $company_menu]);
        });

        view()->composer('layouts.master', PopupComposer::class);
    }

    public function getMenu()
    {
        $menu = Menu::where('locale', App::currentLocale())
        ->whereHas('menuItems')
        ->with('menuItems');

        return $menu;
    }
}
