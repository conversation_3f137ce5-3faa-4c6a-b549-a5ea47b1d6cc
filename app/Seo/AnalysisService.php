<?php
namespace App\Seo;

use Illuminate\Support\Str;

class AnalysisService
{
    /**
     * Analyze the length of the given key phrase, counting out stopwords
     */
    public function keyphraseLength(string $keyphrase): int
    {
        // Greek stopwords commonly ignored by Yoast
        $stopwords = [
            'ο', 'η', 'το', 'οι', 'τα', 'του', 'της', 'των', 'τον', 'την', 'ένα', 'ένας', 'μια', 'μίας',
            'σε', 'για', 'με', 'από', 'και', 'ή', 'θα', 'να', 'που', 'πως', 'ότι', 'όπως', 'αν', 'τις',
            'στο', 'στη', 'στα', 'στους', 'στις', 'στον', 'στην', 'το', 'τα', 'έχει', 'είναι', 'ήταν',
            'χωρίς', 'μα', 'όμως', 'αλλά', 'προς', 'κάθε', 'κάποιο', 'όπου',
        ];

        // Tokenize and normalize
        $keyphraseWords = preg_split('/\s+/', mb_strtolower($keyphrase, 'UTF-8'));
        $filteredWords  = [];

        foreach ($keyphraseWords as $word) {

            // Remove punctuation
            $cleanWord = trim($word, ".,;!?\"«»“”‘’()[]{}");

            // If not a stop word, add it to count
            if (! in_array($cleanWord, $stopwords) && mb_strlen($cleanWord) > 1) {
                $filteredWords[] = $cleanWord;
            }
        }

        return count($filteredWords);
    }

    /**
     * Check if title contains keyword
     */
    public function checkTitleForKeyword($title, $keyphrase): bool
    {

        return Str::contains($title, $keyphrase, ignoreCase: true);
    }

    /**
     * Check if title starts with keyword
     */
    public function checkTitleStartsWithKeyword($title, $keyphrase): bool
    {
        return Str::startsWith(Str::lower($title), Str::lower($keyphrase), );
    }

    /**
     * Check if meta description contains keyword
     */
    public function checkMetaDescriptionForKeyword($meta_description, $keyphrase): bool
    {
        return Str::contains($meta_description, $keyphrase, ignoreCase: true);
    }

    /**
     * CHeck if h2, h3 tags contain keyword
     */
    public function checkHeadingsForKeyword($content, $keyprhase): bool
    {
        preg_match_all('/<(h2|h3)[^>]*>(.*?)<\/\1>/i', $content, $matches);

        foreach ($matches[2] as $heading) {

            if (Str::contains(Str::lower($heading), Str::lower($keyprhase))) {
                return true;
            }

        }

        return false;
    }

    /**
     * Check content length
     */
    public function checkContentLength($content)
    {
        $content_words = preg_split('/\s+/', mb_strtolower($content, 'UTF-8'), -1, PREG_SPLIT_NO_EMPTY);        

        return count($content_words);
    }

    /**
     * Check 1st paragraph
     */
    public function checkFirstParagraphForKeyword($content, $keyphrase): bool
    {
        $firstParagraph = Str::of($content)->stripTags()->explode("\n")[0];

        return Str::contains(Str::lower($firstParagraph), Str::lower($keyphrase));

    }

    /**
     * Check keyword density
     */
    public function checkKeywordDensity($content, $keyphrase)
    {
        $wordCount = count(preg_split('/\s+/', mb_strtolower($content, 'UTF-8')));

        $keyword_count = Str::substrCount(Str::lower($content), Str::lower($keyphrase));

        $density = $wordCount > 0 ? ($keyword_count / $wordCount) * 100 : 0;

        return [
            'density' => $density,
            'keywordCount' => $keyword_count
        ];
    }

    // Check slug for keyword
    public function checkSlugForKeyword($slug, $keyphrase)
    {
        $transltated_keyphrase = Str::slug($keyphrase);

        return Str::contains($slug, $transltated_keyphrase);
    }

    /**
     * Check image alt tag for keyword 
     * */ 
    public function checkImageAltForKeyword($alt, $keyword)
    {
        return Str::contains(Str::lower($alt), Str::lower($keyword));
    }

    /**
     * Analyse links in content
     */
    public function checkLinks($content)
    {
        $domain = config('app.url');

        preg_match_all('/<a\s[^>]*href="([^"]+)"/i', $content, $matches);

        $links = $matches[0];

        $internal = 0;
        $external = 0;

        foreach ($links as $link) {
            
            if(Str::contains($link, $domain)) {
                $internal ++;
            } elseif (Str::startsWith($link, 'http')) {
                $external ++;
            }            
        }

        return [
            'external' => $external,
            'internal' => $internal, 
        ];
    }

}
