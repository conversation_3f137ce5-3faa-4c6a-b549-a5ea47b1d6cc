<?php

namespace App\Http\Controllers;

use App\Models\Faq;
use App\Models\Tag;
use App\Models\Post;
use App\Models\Banner;
use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class HomepageController extends Controller
{
    public function show()
    {
        // Get the sliders
        $sliders = $this->getBanners()->where('type', 'slider');
        $banners = $this->getBanners()->where('type', 'banner');


        // Break banners into collections of 4 (or give empty collections if there are no banners)
        $banner_chunks = $banners->chunk(4);
        $banners1 = $banner_chunks->get(0, collect()); 
        $banners2 = $banner_chunks->get(1, collect());


        // Get featured food tours
        $foodtours = Package::food()
            ->published()
            ->featured()
            ->whereLocale('title', App::getLocale())
            ->get();

        // Get featured posts
        $posts = Post::published()
            ->featured()
            ->whereLocale('title', App::getLocale())
            ->with('images')
            ->with('tags')
            ->get();

        // Get fatured faqs
        $faqs = Faq::published()
            ->featured()
            ->whereLocale('question', App::getLocale())
            ->get();

        $home_categories = Tag::featured()
            ->whereLocale('title', App::getLocale())
            ->whereHas('packages')
            ->orderBy('order')
            ->with('packages')
            ->with('packages.tags')
            ->with('packages.departDates')
            ->with('packages.images')
            ->get();
        
        
        return view('home', compact('sliders', 'banners1', 'banners2', 'home_categories', 'foodtours', 'posts', 'faqs'));
    }

    public function getBanners()
    {
        $allBanners = Banner::published()->select('banners.*')
        ->join('images', function ($join) {
            $join->on('banners.id', '=', 'images.imageable_id')
                 ->where('images.imageable_type', '=', Banner::class);
        })
        ->orderBy('images.order')
        ->with('images')
        ->get();

        return $allBanners;
    }
}
