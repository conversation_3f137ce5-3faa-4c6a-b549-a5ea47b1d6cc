<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;


class RedirectController extends Controller
{
    public function productRedirect($slug)
    {

        $locale = app()->getLocale();

        $package = Package::travel()
            ->published()
            ->where('slug->' . $locale, $slug)
            ->firstOrFail();

        return redirect()->route('el.travel.show', $package);
    }

    public function foodIndexRedirect()
    {

        $locale = app()->getLocale();

        return redirect()->route($locale.'.food-tours.index');
    }

    public function blogIndexRedirect()
    {
        $locale = app()->getLocale();
        return redirect()->route($locale.'.posts.index');
    }

    public function postsRedirect(Post $post)
    {
        return redirect()->route('el.posts.show', $post);
    }

}
