<?php

namespace App\Http\Controllers;

use App\Models\Faq;
use App\Models\Tag;
use App\Models\Post;
use App\Models\Ship;
use Illuminate\View\View;
use Illuminate\Http\Request;

class TagsController extends Controller
{
    public function showCategory(Tag $tag): View
    {   

        if($tag->type !== 'category') {
            abort(404);
        }

        return $this->loadables($tag);

    }
    
    public function showTag(Tag $tag): View
    {   

      if($tag->type !== 'tag') {
        abort(404);
      }

      return $this->loadables($tag);
              
    }

    protected function loadables(Tag $tag) {
        
        $packages = $tag->packages()
            ->published()
            ->whereLocale('title', app()->getLocale())
            ->with('categories')
            ->with('tags')
            ->with('departDates')
            ->orderByDesc('featured')
            ->orderBy('order')
            ->get();

        $posts = $tag->posts()
            ->published()
            ->whereLocale('title', app()->getLocale())
            ->orderBy('featured')
            ->orderByDesc('published_at')
            ->take(12)
            ->get();

        $faqs = $tag->faqs()
            ->published()
            ->whereLocale('question', app()->getLocale())
            ->orderBy('order')
            ->get();

        $ships = Ship::published()
            ->whereLocale('title', app()->getLocale())
            ->whereHas('packages', function ($query) use ($packages) {
                $query->whereIn('packages.id', $packages->pluck('id'));
            })
            ->get();

        $banners = $tag->page_banners()->published()->orderBy('order')->get();

        return view('tags.show', compact('tag', 'packages', 'posts', 'faqs', 'ships', 'banners'));
    }
}
