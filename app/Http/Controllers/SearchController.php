<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Services\Search\PackageSearchAspect;
use App\Services\Search\PostSearchAspect;
use Illuminate\Http\Request;
use Spatie\Searchable\Search;

class SearchController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = $request->get('q');

        // initialise search results
        $search_results = collect();

        if($query)
        {
    
            // perform the search
            $search_results = (new Search())                
                ->registerAspect(PackageSearchAspect::class)
                ->registerAspect(PostSearchAspect::class)
                ->limitAspectResults(50)
                ->search($query);
        }
        

        return view('search.index', compact('search_results', 'query'));
    }

}
