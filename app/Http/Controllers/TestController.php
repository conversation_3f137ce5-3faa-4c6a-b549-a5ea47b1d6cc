<?php

namespace App\Http\Controllers;

use App\Mail\ContactFormMail;
use App\Models\ContactLead;
use App\Models\Post;
use Google\Cloud\Translate\V3\TranslationServiceClient;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class TestController extends Controller
{
    public function testExcel()
    {
        if ($file = fopen(public_path('post_tags.csv'), 'r'))
        {
            $firstline = fgets($file);

            while (($data = fgetcsv($file,0,';')))
            {
//                echo 'post id: ' . $data[0] . ' tag ids: ' . $data[1] . "<br>";
//
//                if(!empty($data[1]))
//                {
//                    $tag_ids = explode(',', $data[1]);
//                    foreach ($tag_ids as $id)
//                    {
//                        echo $id . "<br>";
//                    }
//                }
//                else
//                {
//                    echo 'no tag ids found!' . "<br>";
//                }


                // get post
                $post = Post::find($data[0]);
                if($post)
                {
                    echo 'post with id: ' . $data[0] . ' found!' . "<br>";

                    // work on the relationships
                    if(!empty($data[1]))
                    {
                        $tag_ids = explode(',', $data[1]);
                        $post->tags()->syncWithoutDetaching($tag_ids);
                    }
                    else
                    {
                        echo 'no tag ids found!' . "<br>";
                    }
                }
                else
                {
                    echo 'post with id: ' . $data[0] . ' NOT found!' . "<br>";
                }


            }
            fclose($file);
        }

    }

    public function testEmail()
    {
        $attributes = [
            'message_type' => 'contact',
            'first_name' => 'Vlassis',
            'last_name' => "Mponatsos",
            'email' => '<EMAIL>',
            'mobile' => '123456789',
            'subject' => 'Email apo MAILGUN',
            'message' => 'Me endiaferei to akinito stin odo Aeropagitou',
            'persons' => 45,
        ];

        // Created the Lead
        $lead = ContactLead::create($attributes);

        // send the lead via mail to admins
        Mail::send(new ContactFormMail($lead));

        echo 'sent';
    }

    public function testGTranslate()
    {
        $config = ['credentials' => storage_path('app/application_default_credentials.json')];

        $translationClient = new TranslationServiceClient($config);
        $content = ['Μετά το πρωινό στο ξενοδοχείο αναχώρηση με τα πόδια για το Μουσείο του Prado. Θα περάσουμε από την Plaza Colon, το Paseo de Recoletos, και την Plaza de Cibeles με την Τράπεζα Ισπανίας και το Δημαρχείο και από το Paseo del Prado για να καταλήξουμε στο μουσείο. Το Μουσείο του Prado είναι μία από τις σημαντικότερες πινακοθήκες στον κόσμο με πληθώρα διάσημων έργων που θα χρειαζόμασταν μέρες ολόκληρες για να μπορέσουμε να τα δούμε, στην 1 ώρα και 35 λεπτά που μας δίδονται για να κάνουμε την προγραμματισμένη μας ξενάγηση με επίσημο ξεναγό θα απολαύσουμε κυρίως από την Ισπανική Σχολή έργα των El Greco, Velazquez και Goya καθώς και κάποιους πίνακες της Βενετικής Σχολής όπως του Tintoretto ή της Φλαμανδικής Σχολής όπως του Rubens. επίσης και εδώ θα έχουμε μία ώρα ελεύθερη για να μπορέσετε να δείτε και άλλα έργα ή να επισκεφτείτε το κατάστημα του μουσείου. Στην συνέχεια θα περπατήσουμε από την Μαδρίτη των Βουρβόνων με τους μεγάλους ανοικτούς χώρους δηλαδή πλατείες, λεωφόρους κ.λπ. και συγκεκριμένα από την Λεωφόρο Prado, γνωστή και ως Λεωφόρος της Τέχνης, στην Μαδρίτη των Αψβούργων με τα πολλά στενά δρομάκια, και θα περάσουμε την Γειτονιά των Γραμμάτων Barrio de las Letras. Από εκεί και μετά θα είστε ελεύθεροι να «χαθείτε» σε αυτά τα δρομάκια, να δοκιμάσετε μία chocolate con churros στην Σοκαλατερία San Ginés, ή να βρεθείτε στην οδό Cava Baja στην γειτονιά La Latina με τα πολλά Tapas Bar. Για αργότερα το απόγευμα, σας προτείνουμε να επισκεφτείτε το Museo Nacional Centro de Arte Reina Sofia και συγκεκριμένα τον 2ο όροφο όπου βρίσκεται η μόνιμη έκθεση. Εκεί θα έχετε την δυνατότητα να θαυμάσετε μερικά από τα πιο διάσημα έργα των Joan Miró, Salvador Dalí και φυσικά το αριστούργημα της παγκόσμιας τέχνης την Guernica του Pablo Picasso όπως και άλλα πολύ γνωστά έργα του καλλιτέχνη.'];
        $targetLanguage = 'en';
        $response = $translationClient->translateText(
            $content,
            $targetLanguage,
            TranslationServiceClient::locationName(config('services.google.cloud_translate.project_id'), 'global')
        );

        foreach ($response->getTranslations() as $key => $translation) {
            echo $translation->getTranslatedText();
        }

    }

}
