<?php

namespace App\Http\Controllers;

use App\Models\Ship;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ShipController extends Controller
{
    public function show(Ship $ship): View
    {

        if(!$ship->published && !(auth()->user() && auth()->user()->hasRole(['super admin', 'admin', 'manager'])) ){
            abort(404);
        }

        return view('ships.show', compact('ship'));
    }
}
