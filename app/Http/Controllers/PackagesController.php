<?php

namespace App\Http\Controllers;

use App\Models\Package;
use Illuminate\View\View;
use Illuminate\Http\Request;

class PackagesController extends Controller
{
    public function index(): View
    {
        $packages = Package::travel()
            ->published()
            ->whereLocale('title', app()->getLocale())
            ->with('categories')
            ->with('tags')
            ->orderBy('featured', 'desc')
            ->get();

        return view('packages.index', compact('packages'));
    }

    public function show(Package $package): View
    {

        if(!$package->published && !(auth()->user() && auth()->user()->hasRole(['super admin', 'admin', 'manager'])) ){
            abort(404);
        }

        if($package->type == 'food') {
            abort(404);
        }
        
        $categories = $package->categories()->get();
        $tags = $package->tags()->get();


        return view('packages.show', compact('package', 'categories', 'tags'));
    }

}
