<?php

namespace App\Http\Controllers;

use App\Models\Package;
use Illuminate\Http\Request;
use Illuminate\View\View;

class FoodToursController extends Controller
{
    public function index(): View
    {
        $foodtours = Package::food()
            ->whereLocale('title', app()->getLocale())
            ->published()
            ->get();

        return view('foodtours.index', compact('foodtours'));
    }

    public function show(Package $foodtour)
    {

        if(!$foodtour->published && !(auth()->user() && auth()->user()->hasRole(['super admin', 'admin', 'manager'])) ){
            abort(404);
        }

        if($foodtour->type !== 'food') {
            abort(404);
        }

        return view('foodtours.show', compact('foodtour'));
    }
}
