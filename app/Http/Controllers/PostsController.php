<?php

namespace App\Http\Controllers;

use App\Models\Tag;
use App\Models\Post;
use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;

class PostsController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $posts = Post::published()
            ->whereLocale('title', app()->getLocale())
            ->orderBy('published_at', 'desc')
            ->paginate(19);

        return view('posts.index', compact('posts'));
    }


    /**
     * Show the specified resource.
     */
    public function show(Post $post)
    {
        // <PERSON><PERSON> can see post for review even if it is not published
        if(!$post->published && !(auth()->user() && auth()->user()->hasRole(['super admin', 'admin', 'manager'])) ){
            abort(404);
        }

        $tags = Tag::tag()->whereLocale('title', App::getLocale())->whereHas('posts')->get();

        $featuredPosts = Post::featured()->whereLocale('title', app()->getLocale())->get();


        return view('posts.show', compact('post', 'tags', 'featuredPosts'));
    }
}
