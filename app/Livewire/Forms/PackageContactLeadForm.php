<?php

namespace App\Livewire\Forms;

use Livewire\Component;
use App\Models\ContactLead;
use Livewire\Attributes\On;
use Livewire\Attributes\Validate;
use App\Mail\PackageContactLeadMail;
use Illuminate\Support\Facades\Http;

use Illuminate\Support\Facades\Mail;
use function Illuminate\Support\defer;
use App\Jobs\SendPackageContactLeadJob;
use Illuminate\Validation\ValidationException;

class PackageContactLeadForm extends Component
{
    public $package;

    #[Validate('required')]
    public $message_type;

    #[Validate('required', message: 'form.first_name.required')]
    public $first_name;

    #[Validate('required', message: 'form.last_name.required')]
    public $last_name;

    #[Validate('required | email', message: 'form.email.required')]
    public $email;

    #[Validate('required', message: 'form.mobile.required')]
    public $mobile;

    #[Validate('required', message: 'form.persons.required')]
    public $persons;

    #[Validate('')]
    public $kids;

    #[Validate('')]
    public $address;

    #[Validate('')]
    public $city;

    #[Validate('')]
    #[Validate('date_format:d-m-Y', message: 'form.depart_date.format')]
    public $depart_date;

    #[Validate('')]
    public $message;

    #[Validate('accepted', message: 'form.gdpr.required')]
    public $accept_terms = false;


    #[On('formSubmitted')]
    public function sendLead($token)
    {
        $this->validate();
        $this->validateRecaptcha($token);

        $attributes = [
            'package_id' => $this->package->id,
            'message_type' => $this->message_type,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'mobile' => $this->mobile,
            'persons' => $this->persons,
            'kids' => $this->kids,
            'address' => $this->address,
            'city' => $this->city,
            'depart_date' => $this->depart_date,
            'message' => $this->message,
        ];

        // Created the Lead
        $lead = ContactLead::create($attributes);

        // Notify the user
        session()->flash('success', __('form.success'));

        // Reset the fields
        $this->resetExcept('package');

        // send the lead via mail to admins
        SendPackageContactLeadJob::dispatch($lead, $this->package);

    }

    public function render()
    {

        return view('livewire.forms.package-contact-lead-form');
    }

    protected function validateRecaptcha(string $token): void
    {
        // validate Google reCaptcha.
        $response = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
            'secret' => config('services.recaptcha.secret_key'),
            'response' => $token,
            'remoteip' => request()->ip(),
        ]);


        $throw = fn($message) => throw ValidationException::withMessages(['recaptcha' => $message]);

        if (! $response->successful() || ! $response->json('success')) {
            $throw($response->json(['error-codes'])[0] ?? 'An error occurred.');
        }

        // if response was score based (the higher the score, the more trustworthy the request)
        if ($response->json('score') < .6) {
            $throw(__('form.recaptcha_fail'));
        }
    }
}
