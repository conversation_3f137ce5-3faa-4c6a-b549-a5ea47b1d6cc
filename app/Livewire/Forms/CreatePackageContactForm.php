<?php

namespace App\Livewire\Forms;

use Livewire\Component;
use App\Models\ContactLead;
use Livewire\Attributes\On;
use App\Jobs\SendContactLeadJob;
use App\Jobs\SendCreatePackageContactLeadJob;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\ValidationException;

class CreatePackageContactForm extends Component
{
    public $tag;

    #[Validate('required', message: 'form.first_name.required')]
    public $first_name;

    #[Validate('required', message: 'form.last_name.required')]
    public $last_name;

    #[Validate('required | email', message: 'form.email.required')]
    public $email;

    #[Validate('required', message: 'form.mobile.required')]
    public $mobile;

    #[Validate('required', message: 'form.persons.required')]
    public $persons;

    #[Validate('nullable')]
    public $kids;

    #[Validate('nullable')]
    #[Validate('date_format:d-m-Y', message: 'form.depart_date.format')]
    public $depart_date;

    #[Validate('required', message: 'form.required')]
    public $subject;

    #[Validate('')]
    public $message;

    #[Validate('')]
    public $contact_by;

    #[Validate('accepted', message: 'form.gdpr.required')]
    public $accept_terms = false;

    #[On('formSubmitted')]
    public function sendLead($token)
    {
        $this->validate();
        $this->validateRecaptcha($token);

        $attributes = [

            'message_type' => 'create_package',
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'persons' => $this->persons,
            'kids' => $this->kids,
            'depart_date' => $this->depart_date,
            'email' => $this->email,
            'mobile' => $this->mobile,
            'subject' => $this->subject,
            'contact_by' => $this->contact_by,
            'message' => $this->message,
        ];

        // Created the Lead
        $lead = ContactLead::create($attributes);

        // Notify the user
        session()->flash('success', __('form.success'));

        $this->reset();

        // send the lead via mail to admins
        SendCreatePackageContactLeadJob::dispatch($lead);
    }


    public function render()
    {
        return view('livewire.forms.create-package-contact-form');
    }

    protected function validateRecaptcha(string $token): void
    {
        // validate Google reCaptcha.
        $response = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
            'secret' => config('services.recaptcha.secret_key'),
            'response' => $token,
            'remoteip' => request()->ip(),
        ]);


        $throw = fn($message) => throw ValidationException::withMessages(['recaptcha' => $message]);

        if (! $response->successful() || ! $response->json('success')) {
            $throw($response->json(['error-codes'])[0] ?? 'An error occurred.');
        }

        // if response was score based (the higher the score, the more trustworthy the request)
        if ($response->json('score') < 0.6) {
            $throw(__('form.recaptcha_fail'));
        }
    }
}
