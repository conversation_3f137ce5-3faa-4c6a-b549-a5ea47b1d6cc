<?php

namespace App\Livewire;

use Carbon\Carbon;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;
use Livewire\Attributes\Validate;
use Livewire\Component;

class Chat extends Component
{
    /**
     * Validation rules for user input
     *
     * @var string
     */
    #[Validate('required|max:1000')]
    public string $body = '';

    private string $session_emy_chat                = 'emy_chat';
    private string $session_emy_chat_history        = 'emy_chat_history';
    private string $session_emy_chat_last_timestamp = 'emy_chat_last_timestamp';

    /**
     * Holds system, user and assistant messages for chatbot context
     *
     * @var array
     */
    public array $messages = [];

    public function __construct()
    {
        // Check if session should be cleared
        $session_emy_chat_last_timestamp = session($this->session_emy_chat_last_timestamp);
        if (
            $session_emy_chat_last_timestamp &&
            $session_emy_chat_last_timestamp->diffInSeconds(Carbon::now()) > config('chat.chat_persistence_seconds')
        ) {
            $this->clearMessages();
        }
        // Load messages from session
        $this->messages = session($this->session_emy_chat, $this->messages);
    }

    /**
     * Add the user input to the list of messages
     *
     * @return void
     */
    public function send(): void
    {
        // Validate input
        $this->validate();

        // Load session data
        $this->messages = session($this->session_emy_chat, $this->messages);
        // Add user input
        $this->messages[] = ['role' => 'user', 'content' => $this->body];

        // Keep messages to send to AI
        $messages = $this->messages;

        // Add an empty assistant message to show the loading response assistant message
        $this->messages[] = ['role' => 'assistant', 'content' => ''];

        // Save messages
        $this->persistMessages($this->session_emy_chat, false);

        // Log data
        $this->logMessages('U: ' . $this->body);

        // Unset the body to clear the user input field
        $this->body = '';

        // Trigger assistant-response
        $this->dispatch('assistant-response', data: $messages)->to(Chat::class);
    }

    /**
     * Load chat view
     *
     * @return Factory|View|Application
     */
    public function render(): Factory|View|Application
    {
        return view('livewire.chat');
    }

    /**
     * Add the assistant message to the array of messages
     *
     * @param $data
     * @return void
     */
    #[On('assistant-response')]
    public function assistantResponse($data): void
    {

        // Get singleton and send messages to Open AI
        $response = app('prism')->withMessages($this->convertMessages($data))->generate();

        // Init the response from assistant
        $response_mean = 'assistant';

        // Get the response cleared
        $response_data = str_replace('<br />', '', nl2br(str_replace('**', '', $response->text)));


        // If the response is empty check if there was a tool call
        if ($response->toolResults && $response->toolResults[0]->result) {
            // Set the tool as the response mean
            $response_mean = 'toolResult';
            $response_data = json_encode($response->toolResults[0]);
        }

        // Fire new event
        $this->dispatch($response_mean . '-message', data: $response_data)->to(Chat::class);
    }

    /**
     * Add the assistant message to the array of messages
     *
     * @param $data
     * @return void
     */
    #[On('assistant-message')]
    public function assistantMessage($data): void
    {
        $this->messages[] = ['role' => 'assistant', 'content' => $data];

        // Log data
        $this->logMessages('A: ' . $data);

        // Delay to give a reality impression
        if (app()->isProduction()) {
            sleep(rand(3, 8));
        }

        // Save messages
        $this->persistMessages();
    }

    /**
     * Add the tool result message to the array of messages
     *
     * @param $data
     * @return void
     */
    #[On('toolResult-message')]
    public function toolResultMessage($data): void
    {
        $this->messages[] = ['role' => 'toolResult', 'content' => $data];

        // Log data
        $this->logMessages('TR: ' . $data);

        // Chat has ended, save data in chat history
        $this->persistMessages($this->session_emy_chat_history);

        // Reset data
        $this->messages = [];

        // Save messages
        $this->persistMessages();
    }

    /**
     * persistMessages
     * Save the array of messages in the session variable
     *
     * @return void
     */
    private function persistMessages($session_variable = 'emy_chat', $clearEmpty = true): void
    {
        $messages = [];
        foreach ($this->messages as $message) {
            if ($clearEmpty && empty($message['content'])) {
                continue;
            }
            $messages[] = $message;
        }

        // Save messages to session
        request()->session()->put($session_variable, $messages);
        // Refresh last chat timestamp
        request()->session()->put($this->session_emy_chat_last_timestamp, now());
    }

    /**
     * Convert array of messages to array of UserMessage or AssistantMessage to be sent to Open AI
     *
     * @param $data
     * @return array
     */
    private function convertMessages($data = null): array
    {
        // Get the messages to convert
        $messages = $data ?? $this->messages;
        // init the converted messages array
        $convertedMessages = [];
        // Loop through data
        foreach ($messages as $message) {
            if ($message['content']) {
                $content = $message['content'];

                if ($message['role'] === 'toolResult') {
                    // This is very buggy.
                    // Will not add the message. the conversation should be finished.
                    //                    $toolResult = json_decode($message['content'], true);
                    //                    $content = [
                    //                        new ToolResult(
                    //                            toolCallId: $toolResult['toolCallId'],
                    //                            toolName: $toolResult['toolName'],
                    //                            args: $toolResult['args'],
                    //                            result: $toolResult['result'],
                    //                        )
                    //                    ];
                    continue;
                }
                $classname = '\EchoLabs\Prism\ValueObjects\Messages\\' . ucfirst($message['role']) . 'Message';
                $convertedMessages[] = new $classname($content);
            }
        }
        return $convertedMessages;
    }

    /**
     * Clear all session variables
     *
     * @return void
     */
    private function clearMessages()
    {
        // Clear messages
        request()->session()->put($this->session_emy_chat, []);
        request()->session()->put($this->session_emy_chat_history, []);
        request()->session()->put($this->session_emy_chat_last_timestamp);
    }

    /**
     * @param string $data
     * @return void
     */
    private function logMessages(string $data = ''): void
    {
        if (env('DISABLE_CHAT_LOG') !== true && !empty($data)) {
            // Log data
            Log::info(request()->session()->getId() . ' | ' . $data);
        }
    }
}
