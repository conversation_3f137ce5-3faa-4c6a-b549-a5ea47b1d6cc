<?php

namespace App\Listeners;

use App\Events\LeadData;
use App\Jobs\SendChatLeadJob;
use App\Models\ChatLead;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class LeadListener implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(LeadData $event): void
    {
        Log::info("LeadData event: " . json_encode($event));

        // persist the data in the database
        $chat_lead = ChatLead::create($event->data);

        // send notification email to admins
        SendChatLeadJob::dispatch($chat_lead);
    }
}
