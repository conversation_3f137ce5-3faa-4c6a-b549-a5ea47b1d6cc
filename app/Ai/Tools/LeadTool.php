<?php

namespace App\Ai\Tools;

use App\Events\LeadData;
use EchoLabs\Prism\Tool;
use Illuminate\Support\Facades\Log;

class LeadTool extends Tool
{

    public function __construct()
    {
        $this
            ->as('LeadTool')
            ->for('saving the user travel details')
            ->withStringParameter('name', 'Name')
            ->withStringParameter('phone', 'Phone')
            ->withStringParameter('email', 'Email')
            ->withStringParameter('destination', 'Destination')
            ->withStringParameter('dates', 'Dates')
            ->withStringParameter('travelers', 'Travelers')
            ->withStringParameter('underage', 'Underage')
            ->withStringParameter('preferences', 'Preferences')
            ->withStringParameter('query', 'any available info.')
            ->using($this);
    }

    public function __invoke(
        string $name,
        string $phone,
        string $email,
        string $destination,
        string $dates,
        string $travelers,
        string $underage,
        string $preferences,
        string $query
    ): string
    {

        /* TODO remove */

        Log::info("name:        " . $name);
        Log::info("phone:       " . $phone);
        Log::info("email:       " . $email);
        Log::info("destination: " . $destination);
        Log::info("dates:       " . $dates);
        Log::info("travelers:   " . $travelers);
        Log::info("underage:    " . $underage);
        Log::info("preferences: " . $preferences);
        Log::info("query:       " . $query);

        LeadData::dispatch(
            [
                "name"         => $name,
                "phone"        => $phone,
                "email"        => $email,
                "destination"  => $destination,
                "dates"        => $dates,
                "travelers"    => $travelers,
                "underage"     => $underage,
                "preferences"  => $preferences,
                "query"        => $query,
            ]
        );

        return __('chat.lead_tool_response');
    }
}
