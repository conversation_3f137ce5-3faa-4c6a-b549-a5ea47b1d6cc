<?php
namespace App\Ai\Tools;

use App\Models\ItineraryStep;
use App\Models\Package;
use EchoLabs\Prism\Tool;

class GeographyTool extends Tool
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {

        $this
            ->as('GeographyTool')
            ->for('Reading data to extract city names')
            ->withStringParameter('query', 'Query about the cities')
            ->using($this);
    }

    public function __invoke(string $query = ''): string
    {
        $steps = ItineraryStep::get(['title']);

        $data = $steps->map(function ($step) {
            return [
                'title' => $step->title,
            ];
        });

        return json_encode($data->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

}
