<?php
namespace App\Ai\Tools;

use App\Models\Alias;
use App\Models\Package;
use EchoLabs\Prism\Tool;

class PackageTool extends Tool
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        $this
            ->as('PackageTool')
            ->for('Displaying packages')
            ->withStringParameter('query', 'The search query for packages')
            ->using($this);
    }

    public function __invoke(string $query = '')
    {

        $normalized_query = mb_strtolower($this->removeGreekAccents($query));

        try {
            // $packages = Package::whereRaw('LOWER(' . $this->removeGreekAccents('title') . ') LIKE ?', ['%' . $normalized_query . '%'])
            //     ->pluck('title', 'content', 'duration');

            $alias = Alias::whereRaw('LOWER(' . $this->removeGreekAccents('name') . ') LIKE ?', ['%' . $normalized_query . '%'])
                ->first();

            $packages = $alias->aliasable->packages->pluck('title');

            return json_encode($packages);

        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * Normalize Greek accents for query
     */
    private function removeGreekAccents($text)
    {
        $greekAccents   = ['ά', 'έ', 'ή', 'ί', 'ό', 'ύ', 'ώ', 'ϊ', 'ΐ', 'ϋ', 'ΰ', 'Ά', 'Έ', 'Ή', 'Ί', 'Ό', 'Ύ', 'Ώ'];
        $greekNoAccents = ['α', 'ε', 'η', 'ι', 'ο', 'υ', 'ω', 'ι', 'ι', 'υ', 'υ', 'Α', 'Ε', 'Η', 'Ι', 'Ο', 'Υ', 'Ω'];

        return str_replace($greekAccents, $greekNoAccents, $text);
    }
}
