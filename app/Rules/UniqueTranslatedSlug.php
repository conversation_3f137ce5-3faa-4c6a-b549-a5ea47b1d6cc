<?php

namespace App\Rules;

use Closure;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueTranslatedSlug implements ValidationRule
{


    public function __construct(
        protected $table,
        protected $column,
        protected $language,
        protected $exceptId
    ) {
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $query = DB::table($this->table)
            ->where('slug->' . $this->language, $value)
            ->where(function ($query) {
                if ($this->exceptId) {
                    $query->where('id', '!=', $this->exceptId);
                }
            })
            ->first();

        if ($query) {
            $fail('Το slug αυτό χρησιμοποιείται ήδη');
        }
    }
}
