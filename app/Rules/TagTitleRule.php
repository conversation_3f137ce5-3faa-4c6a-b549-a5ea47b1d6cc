<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class TagTitleRule implements DataAwareRule, ValidationRule
{

    protected $data = [];

    public function setData(array $data): static
    {
        $this->data = $data;
 
        return $this;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {

       
        if(!$value) {
            if ( $this->data['slug']['en'] || $this->data['content']['en'] ||$this->data['meta_title']['en'] || $this->data['meta_description']['en']) {
                $fail('Το πεδίο Τίτλος Αγγγλικά είναι απαραίτητο αν είναι συμπληρωμένα άλλα πεδία στα αγγλικά');
            }
        }
    }
}
