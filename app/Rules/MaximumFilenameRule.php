<?php

namespace App\Rules;

use Closure;
use Illuminate\Support\Str;
use Illuminate\Contracts\Validation\ValidationRule;

class MaximumFilenameRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if(Str::length($value->getClientOriginalName()) > 40){
            $fail('Το όνομα αρχείου δεν πρέπει να ξεπερνάει τους 40 χαρακτήρες');
        };
    }
}
