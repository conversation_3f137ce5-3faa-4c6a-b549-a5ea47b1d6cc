image: rastasheep/ubuntu-sshd
pipelines:
  branches:
    integration:
        - step:
            name: Integration - Build & Deploy

            script:
              # just execute the deployment script on remote server
              - echo $remote_user_pass | ssh -tt foufos@************** 'sudo ./deploy_wot_integration'
    master:
      - step:
          name: Production - Build & Deploy

          script:
            # just execute the deployment script on remote server
            - echo $remote_user_pass | ssh -tt foufos@************** 'sudo ./deploy_wot_master'
