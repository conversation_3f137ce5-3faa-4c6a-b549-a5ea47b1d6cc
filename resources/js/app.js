import './bootstrap';

import '@nextapps-be/livewire-sortablejs';

import flatpickr from "flatpickr";
import tinymce from 'tinymce';
import 'tinymce/icons/default/icons';
import 'tinymce/plugins/link';
import 'tinymce/plugins/table';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/code';
import "tinymce/themes/silver";
import "tinymce/skins/ui/oxide/skin";
import "tinymce/skins/ui/oxide/content.css";
import 'tinymce/skins/content/default/content';
import 'tinymce/models/dom';
import Swiper from 'swiper';
import { Autoplay, Navigation, Pagination, Scrollbar } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import focus from '@alpinejs/focus';
import persist from '@alpinejs/focus';
import ScrollReveal from 'scrollreveal';

Alpine.plugin(focus);
Alpine.plugin(persist);


flatpickr("#published_at", {
    enableTime: true,
    dateFormat: "d-m-Y H:i",
});

flatpickr('#depart_dates', {
    mode: "multiple",
    minDate: "today",
    dateFormat: "Y-m-d",
    defaultDate: window.defaultDepartDates,
});

document.addEventListener('DOMContentLoaded', function () {
    // Select all textareas with the class 'editor'
    const editors = document.querySelectorAll('.editor');

    // Loop through each textarea and initialize TinyMCE
    editors.forEach(textarea => {
        tinymce.init({
            target: textarea,
            base_url: '/node_modules/tinymce',
            theme_url: '/node_modules/tinymce/themes/silver/theme.js',
            theme: 'silver',
            plugins: 'link table lists code contextmenu',
            toolbar: 'undo redo | styleselect | bold italic | link | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | code',
            contextmenu: "paste | link image inserttable | cell row column deletetable",
            relative_urls: false,
            remove_script_host: false,
            entity_encoding: "raw",
            height: textarea.dataset.height || 400 // Use data-height attribute or default to 400px
        });
    });
});


// Slider
const swiper = new Swiper('.swiper', {
    modules: [Autoplay, Navigation, Pagination],
    loop: true,
    autoplay: {
        delay: 5000,
    },
    pagination: {
        el: '.swiper-pagination',
    },
});

// Ship Galleries
const swiperGal = new Swiper(".mySwiper", {
    modules: [Navigation, Pagination, Scrollbar], // Include Scrollbar module
    slidesPerView: 1.2, // Show part of the next slide
    spaceBetween: 20, // Space between slides
    // centeredSlides: true, // Center the active slide
    loop: false,
    autoplay: false, // Explicitly set autoplay to false
    pagination: false, // Disable pagination
    // scrollbar: {
    //     el: ".swiper-scrollbar",
    //     draggable: true,
    // },
    breakpoints: {
        640: {
            slidesPerView: 2.2, // Show part of the next slide
            spaceBetween: 20,
        },
        768: {
            slidesPerView: 3.1, // Show part of the next slide
            spaceBetween: 30,
        },
        1024: {
            slidesPerView: 3.1, // Show part of the next slide
            spaceBetween: 30,
        },
    },
});
