<div class="group md:w-[48%] lg:w-[30%] py-2 lg:p-2 relative">

    <div class="relative">

        <a href="{{ route($locale . '.travel.show', $cruise->slug) }}">
            <img class="h-[350px] lg:h-[500px] object-cover group-hover:brightness-90"
                src="{{ $cruise->mainImage() ? asset('storage/' . $cruise->imageMd()) : '' }}" alt="{{ $cruise->alt() }}" loading="lazy">
        </a>
        <div
            class="absolute bottom-0 left-0 right-0 z-20 p-2 text-white font-bold flex gap-2 justify-between items-center">
            <div class="flex gap-2">
                @if ($cruise->duration)
                    <x-icons.clock-icon class="w-6 h-6 !text-white" />

                    {{ $cruise->duration }} <span class="uppercase">{{ __('base.days') }}</span>
                @endif
            </div>
            <div class="font-bold">
                @if (!$cruise->price_fixed)
                    <span class="uppercase">{{ __('base.from') }}</span>
                @endif
                {{ $cruise->price }}€/<span class="uppercase text-xs">{{ __('base.person') }}</span>
            </div>

        </div>
        <div
            class="absolute bottom-0 left-0 right-0 h-16 z-10 bg-gradient-to-t from-darkCyan/50 via-darkCyan/30 to-transparent">
        </div>
    </div>
    <div class="px-2 font-bold py-3 min-h-[5rem]">

        <div class="flex items-center gap-1">
            <x-icons.location-icon class="w-8" />
            <h4 class="p-2 text-xl font-bold">
                <a href="{{ route($locale . '.travel.show', $cruise->slug) }}">
                    {!! $cruise->getTranslation('title', $locale) !!}
                </a>
            </h4>

        </div>
        {{-- 
        <div class="flex flex-wrap items-center">
            @forelse ($cruise->categories->take(2) as $category)
                <span class="hover:text-pacific-500 pr-2 py-1 text-[.9rem]">
                    <a href="{{ route($locale . '.cruises.show', $category) }}">{{ strip_tags($category->title) }}{{ !$loop->last ? ',' : '' }}
                    </a>
                </span>
            @empty
            @endforelse
        </div>

        <div class="flex flex-wrap items-center">
            @forelse ($cruise->tags->take(6) as $tag)
                <span class="uppercase text-pacific-500 hover:text-darkCyan pr-1 py-1 text-sm">
                    <a href="{{ route($locale . '.tag.show', $tag) }}">{{ $tag->title }}{{ !$loop->last ? ',' : '' }}
                    </a>
                </span>
            @empty
            @endforelse
        </div> --}}
    </div>
</div>
