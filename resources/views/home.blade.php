<x-app-layout>

    <x-slot:header>

        @if ($sliders->count())
            @include('homepage.partials.__slider')
        @else
            <img class="hero-image" src="{{ asset('images/bulk/budapest.jpg') }}" alt="">
            <img src="{{ asset('images/icons/scribble.svg') }}" alt="" class="absolute bottom-0">
        @endif

    </x-slot:header>

    <img src="{{ asset('images/icons/blog-travel-doodles.svg') }}" alt="">

    <div class="my-6">
        <h1 class="text-center">WORLD OF TRAVEL</h1>
        <div class="lg:w-3/5 mx-auto my-6">
            <p class="text-center">
                {!! __('base.welcome_paragraph') !!}
            </p>
        </div>
    </div>

    {{-- Banners Section --}}

    @if (isset($banners1) && $banners1->count())
        @include('homepage.partials.__banners', ['banners' => $banners1])
    @endif


    {{-- Packages Categories Section --}}

    @foreach ($home_categories as $category)

        @if ($category->packages->where('featured', 1)->count())
            <section class="{{ $loop->even ? 'bg-lightGrey pt-12 pb-16 mt-12' : 'py-8' }}  relative">

                @if ($loop->even)
                    <img src="{{ asset('images/icons/camera.svg') }}" alt=""
                        class="w-32 absolute -top-12 left-4">
                    <img src="{{ asset('images/icons/airplane.svg') }}" alt=""
                        class="hidden md:block w-48 absolute -top-16 right-6">
                @endif
                
                <h2 class="text-4xl text-center text-darkCyan font-semibold my-8 uppercase">
                    {!! $category->getTranslation('title', $locale) !!} </h2>

                <div class="flex flex-wrap items-stretch {{ $loop->odd ? 'lg:px-12' : '' }}">

                    @php
                        // If the category is not Prosfores, limit packages to 4
                        if (
                            $category->getTranslation('slug', 'el') == 'prosfores' ||
                            $category->getTranslation('slug', 'en') == 'offers'
                        ) {
                            $category_packages = $category
                                ->packages()
                                ->whereLocale('title', app()->getLocale())
                                ->published()
                                ->featured()
                                ->orderBy('order')
                                ->get();
                        } else {
                            $category_packages = $category
                                ->packages()
                                ->whereLocale('title', app()->getLocale())
                                ->published()
                                ->featured()
                                ->orderBy('order')
                                ->take(4)
                                ->get();
                        }
                    @endphp


                    @if ($category->packages->where('featured', 1)->count())
                        @foreach ($category_packages as $package)
                            @include('packages.partials.__package-item')
                        @endforeach
                    @endif


                </div>

                <div class="text-center">
                    <a href="{{ route($locale . '.'.$category->type.'.show', $category) }}">
                        <x-cta-secondary>
                            {{ __('base.find_more') }}
                        </x-cta-secondary>
                    </a>
                </div>

                @if ($loop->even && !$loop->last)
                    <img src="{{ asset('images/icons/cardpostal-bicycle.svg') }}" alt="" loading="lazy"
                        class="hidden md:block w-64 absolute -bottom-8 right-48">
                @endif

            </section>

            @if ($category->getTranslation('slug', 'el') == 'prosfores' || $category->getTranslation('slug', 'en') == 'offers')
                @if ($banners2->count())
                    @include('homepage.partials.__banners', ['banners' => $banners2])
                @endif
            @endif
        @endif
    @endforeach


    {{-- Food Tours Section --}}
    @if ($foodtours->count())
        <section class="my-16 bg-lightGrey py-12 relative">

            <img src="{{ asset('images/icons/souvlaki.svg') }}" alt="" class="w-32 absolute -top-12 left-20" loading="lazy">
            <img src="{{ asset('images/icons/cupcake.svg') }}" alt=""
                class="hidden md:block w-20 absolute -top-16 right-32" loading="lazy">

            <h2 class="text-4xl text-center text-darkCyan font-semibold my-8"> FOOD <span
                    class="text-pacific-500">TOURS</span> </h2>

            <div class="lg:w-4/5 mx-auto lg:flex justify-evenly gap-4">


                @include('foodtours.partials.__foodtour-item')


            </div>
        </section>
    @endif

    {{-- CTA Section --}}
    @include('layouts.partials.__cta')

    {{-- Faqs Section --}}
    @if ($faqs->count())
        <section class="w-4/5 mx-auto my-8">
            <x-faqs :collection="$faqs" />
        </section>
    @endif

    {{-- Posts Section --}}
    @if ($posts->count())
        <section class="bg-darkCyan px-6 py-16 my-6">

            <h2 class="text-4xl text-center text-white font-bold">
                {!! __('base.featured_blog_posts_title') !!}
            </h2>

            <div class="lg:w-4/5 mx-auto mt-8">
                <div class="flex flex-wrap justify-between gap-y-12">
                    @foreach ($posts as $post)
                        @include('posts.partials.__post-item')
                    @endforeach
                </div>
            </div>
        </section>
    @endif

</x-app-layout>
