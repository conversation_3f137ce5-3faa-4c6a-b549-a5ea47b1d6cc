<x-app-layout>
    <x-slot:header>
        <div class="relative">
            <img src="{{ asset('images/bulk/search.webp') }}" alt="" class="">

            <img src="{{ asset('images/icons/scribble.svg') }}" alt="" class="absolute bottom-0 z-50">
        </div>
    </x-slot>

    <div class="w-[90%] lg:w-4/5 mx-auto -mt-3 z-50 relative ">
        <ul class="!text-sm text-gray-500 flex !list-none">
            <li>
                <a href="/"><x-icons.home-icon /></a>
            </li>

            <li class="before:content-['/\00a0'] before:p-2">
                Search
            </li>

        </ul>
    </div>
    <div class="py-12">

        <x-search-form />

        @if ($query)

            <div class="my-8">

                @if ($search_results->count())
                    <h1 class="text-center my-4">{{ __('base.search_results') }}: <strong>{{ $query }}</strong>
                    </h1>
                    <div class="text-center">{{ $search_results->count() }} αποτελέσματα</div>
                @else
                    <h1 class="text-center my-4">{{ __('base.search_results') }}</h1>
                    <div class="text-center">{{ __('base.no_results') }}</div>
                @endif

            </div>

            @foreach ($search_results->groupByType() as $type => $modelSearchResults)
                <section class="{{ $loop->even ? 'bg-lightGrey' : '' }} py-4">
                    <h2 class="uppercase text-center text-darkCyan font-semibold">{{ __('base.searchtype_' . $type) }}
                        ({{ $modelSearchResults->count() }})
                    </h2>
                    <div class="p-6 text-gray-900 flex flex-wrap justify-evenly gap-y-6 ">
                        @foreach ($modelSearchResults as $key => $result)
                            @include('search.partials.__result-item')
                        @endforeach
                    </div>
                </section>
            @endforeach
        @else
        @endif
    </div>



</x-app-layout>
