<x-app-layout>

    @section('head')
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    @section('metatitle', $foodtour->getTranslation('meta_title', $locale) ? $foodtour->getTranslation('meta_title',
        $locale) : $foodtour->getTranslation('title', $locale))
    @section('metadescription', $foodtour->getTranslation('meta_description', $locale))

@endsection

<x-slot:header>
    <div class="relative">

        <div class="min-h-[500px] lg:min-h-[700px] bg-fixed bg-left bg-no-repeat bg-cover"
            style="background-image: url('{{ $foodtour->mainImage() ? asset('/storage/' . $foodtour->imageLg()) : asset('images/bulk/packages-header.webp') }}')"
            aria-label="{{ $foodtour->alt() }} ">

        </div>

        <img src="{{ asset('images/icons/scribble.svg') }}" alt="" class="absolute -bottom-3 z-10">
    </div>
</x-slot:header>

{{-- Breadcrumbs --}}
<div class="w-[90%] lg:w-4/5 mx-auto lg:-mt-3 z-20 relative ">
    <ul class="text-sm text-gray-500 flex ">
        <li>
            <a href="/"><x-icons.home-icon /></a>
        </li>

        <li class="before:content-['/\00a0'] before:p-2">
            <a href="{{ route($locale . '.food-tours.index') }}">Food Tours</a>
        </li>
        <li class="before:content-['/\00a0'] before:p-2">
            {{ $foodtour->getTranslation('title', $locale) }}
        </li>
    </ul>


</div>

<section class="lg:w-4/5 mx-auto">

    <div class="mt-8 lg:mb-8 px-6">

        @if (auth()->user() &&
                auth()->user()->hasAnyRole(['super admin', 'admin']))
            <div class="flex justify-end mb-4 -mt-4">
                @if (!$foodtour->published)
                    <span class="text-red-500 text-md">Admin Notification: Package is not published</span>
                @endif
                <x-cerberus::form.link href="{{ route('cerberus.foodtours.edit', $foodtour) }}">Επεξεργασία
                </x-cerberus::form.link>

            </div>

        @endif
        <h1 class="text-center text-4xl leading-snug"> {{ $foodtour->getTranslation('title', $locale) }} </h1>
    </div>

    <div class="lg:flex lg:gap-4 lg:justify-center">
        <div class="lg:block lg:w-2/3">
            @if ($foodtour->latestGallery->images->count())
                <!-- Slider main container -->
                <div class="swiper w-full h-auto">
                    <!-- Additional required wrapper -->
                    <div class="swiper-wrapper">
                        @foreach ($foodtour->latestGallery->images as $image)
                            <!-- Slides -->
                            <div class="swiper-slide">
                                <img src="{{ asset('storage/galleries/' . $foodtour->latestGallery->id . '/' . $image->filename) }}"
                                    alt="" class="w-full mt-12 xl:mt-0">
                            </div>
                        @endforeach
                    </div>
                    <!-- If we need pagination -->
                    <div class="swiper-pagination"></div>
                </div>
            @elseif($foodtour->mainImage())
                <img src="{{ asset('storage/' . $foodtour->imageLg()) }}" alt="" class="hero-image">
            @endif
        </div>

        @if ($foodtour->duration || $foodtour->location || $foodtour->group_size || $foodtour->tour_languages)

            <div class="bg-lightGrey px-6 py-4 lg:w-1/3">

                <div
                    class="flex flex-wrap lg:flex-col lg:justify-center gap-y-4 lg:gap-y-10 gap-x-2 justify-between h-full lg:px-6 text-sm lg:text-lg uppercase">

                    @if ($foodtour->duration)
                        <div class="w-[35%] lg:w-full flex gap-1 lg:gap-4 items-center"
                            title="{{ __('base.duration') }}">
                            <x-icons.clock-icon class="w-8" />
                            <div>{{ $foodtour->duration }} <span>{{ __('base.hours') }}</span></div>
                        </div>
                    @endif


                    @if ($foodtour->location)
                        <div class="w-[60%] lg:w-full flex gap-1 lg:gap-4 items-center "
                            title="{{ __('base.location') }}">
                            <img class="w-8" src="{{ asset('images/icons/location.svg') }}" alt="">
                            <div class="">{{ $foodtour->location }} </div>
                        </div>
                    @endif

                    @if ($foodtour->group_size)
                        <div class="w-[35%] lg:w-full flex gap-1 lg:gap-4 items-center"
                            title="{{ __('base.group_size') }}">
                            <x-icons.group-icon class="w-8" />
                            <div>{{ $foodtour->group_size }} <span>{{ __('base.persons') }}</span>
                            </div>
                        </div>
                    @endif

                    @if ($foodtour->tour_languages)
                        <div class="w-[60%] lg:w-full flex gap-1 lg:gap-4 items-center"
                            title="{{ __('base.tour_languages') }}">
                            <img class="w-8" src="{{ asset('images/icons/globe.svg') }}" alt="">
                            <div> {{ $foodtour->tour_languages }} </div>
                        </div>
                    @endif

                </div>
            </div>
        @endif
    </div>
</section>

<section class="lg:hidden">
    <div class="relative bg-darkCyan py-4 px-6 h-48"
        style="background-image: url('{{ asset('images/icons/food-doodles-dark.svg') }}'); background-repeat: no-repeat">

        <div class="text-center text-white text-3xl font-semibold">
            @if (!$foodtour->price_fixed)
                {{ __('base.from') }}
            @endif
            {{ $foodtour->price }} € / {{ __('base.person') }}
        </div>
        <div class="text-center text-white text-2xl ">
            <img src="{{ asset('images/icons/arrow-plane-button.svg') }}"
                class="hidden lg:block absolute top-0 lg:left-[10%] xl:left-[12%] 2xl:left-[15%] w-1/4">

            <x-cta-primary>
                <a href="#reservation">{{ __('base.make-reservation') }}</a>
            </x-cta-primary>
        </div>
    </div>
</section>

<section class="my-4 lg:my-12 py-4 px-6 lg:px-0 lg:py-0 lg:w-4/5 mx-auto">
    <h2 class="text-center lg:text-left text-pacific-500 text-xl uppercase">{{ $foodtour->subtitle }}</h2>
    <div>
        {!! $foodtour->content !!}
    </div>
</section>

<section class="hidden lg:block">
    <div class="relative bg-darkCyan py-4 px-6 h-48"
        style="background-image: url('{{ asset('images/icons/food-doodles-dark.svg') }}'); background-repeat: no-repeat">

        <div class="text-center text-white text-3xl font-semibold">
            @if (!$foodtour->price_fixed)
                {{ __('base.from') }}
            @endif
            {{ $foodtour->price }} € / {{ __('base.person') }}
        </div>
        <div class="text-center text-white text-2xl ">
            <img src="{{ asset('images/icons/arrow-plane-button.svg') }}"
                class="hidden lg:block absolute top-0 lg:left-[10%] xl:left-[12%] 2xl:left-[15%] w-1/4">

            <x-cta-primary>
                <a href="#reservation">{{ __('base.make-reservation') }}</a>
            </x-cta-primary>
        </div>
    </div>
</section>

@if ($foodtour->info)
    <section class="bg-lightGrey py-4 px-6 lg:py-6 lg:w-4/5 mx-auto lg:my-16">

        <div class="relative">

            <h2 class="text-center text-4xl font-bold uppercase w-3/5 sm:w-full mx-auto">{!! __('base.best_moments') !!}
            </h2>

            <img src="{{ asset('images/icons/cheese.svg') }}" alt=""
                class="absolute left-0 top-2 lg:-top-10 w-16 lg:w-24">

            <img src="{{ asset('images/icons/glass-of-wine.svg') }}" alt=""
                class="absolute right-0 -top-8 sm:-top-12  w-16">

        </div>

        <div class="p-4 md:text-center">
            {!! $foodtour->info !!}
        </div>

    </section>
@endif

@if ($foodtour->included || $foodtour->not_included)
    <section class="my-4 bg-darkCyan py-4 px-6 lg:w-4/5 mx-auto">
        <div class="lg:flex lg:px-6">
            <div class="lg:w-1/2">
                <h2 class="text-pacific-500 text-2xl font-semibold uppercase border-b border-white pb-2">
                    {{ __('base.included') }}</h2>
                <div class="text-white included">
                    {!! $foodtour->included !!}
                </div>
            </div>
            <div class="lg:w-1/2">
                <h2
                    class="text-pacific-500 text-2xl font-semibold uppercase border-b border-white pb-2 mt-4 lg:mt-0">
                    {{ __('base.not_included') }}</h2>
                <div class="text-white not-included">
                    {!! $foodtour->not_included !!}
                </div>
            </div>
        </div>

    </section>
@endif

@if ($foodtour->itinerary_steps->count())
    <section class="py-4 px-6 mt-20 lg:mt-12 relative lg:w-4/5 mx-auto">
        <img src="{{ asset('images/icons/arrow-down-right.svg') }}" alt=""
            class="w-3/4 lg:hidden sm:w-3/5 absolute -top-16">
        <div>
            <h3 class="text-3xl lg:text-center font-bold mb-12 uppercase">{!! __('base.faqs') !!}</h3>

            <div itemscope itemtype="https://schema.org/FAQPage" class="text-center">
                @foreach ($foodtour->itinerary_steps as $step)
                    <div x-data="{ show: false }"
                        class="border-b border-black mt-4 py-4 {{ $loop->first ? 'border-t' : '' }}" itemscope
                        itemprop="mainEntity" itemtype="https://schema.org/Question">
                        <h4 class="cursor-pointer" @click="show = !show" itemprop="name">
                            <span class="text-pacific-500">></span> {{ $step->title }}
                        </h4>
                        <div class="mt-2 px-2" x-show="show" x-transition.duration.300ms itemscope
                            itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                            <div itemprop="text">
                                {!! $step->content !!}
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

        </div>
    </section>
@endif


<section>

    <div id="reservation" class="text-center my-12 px-6 uppercase flex justify-center items-center gap-4">
        <div>
            <a href="tel:+30 2102207506"><img class="w-24"
                    src="{{ asset('images/icons/phone-kratiseis.svg') }}" alt=""></a>
        </div>
        <div>
            <p class="text-2xl ">{{ __('form.call-us') }}</p>
            <p><x-address.phone class="font-bold text-2xl text-pacific-500 hover:text-wotDark" /></p>
        </div>
    </div>

    {{-- Contact Form --}}
    <livewire:forms.package-contact-lead-form :package="$foodtour" :message_type="$foodtour->type" />


</section>
</x-app-layout>
