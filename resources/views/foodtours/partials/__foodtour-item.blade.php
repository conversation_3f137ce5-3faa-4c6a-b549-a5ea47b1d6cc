<div class="lg:w-4/5 mx-auto lg:flex justify-evenly gap-4 flex-wrap">

    @foreach ($foodtours as $tour)
        <div class="lg:w-[45%] group">
            <div class="bg-darkCyan p-6">
                <a href="{{ route($locale . '.foodtours.show', $tour) }}">
                    <img class="border-2 border-white w-full group-hover:brightness-90"
                        src="{{ $tour->mainImage() ? asset('storage/' . $tour->imageSm()) : '' }}"
                        alt="{{ $tour->alt() }}">
                </a>
                <a href="{{ route($locale . '.foodtours.show', $tour) }}">
                    <h3 class="text-center text-pacific-500 text-2xl font-bold mt-4 mb-0 group-hover:text-pacific-600">
                        {{ $tour->getTranslation('title', $locale) }}</h3>
                </a>
            </div>
            <div class="bg-lightGrey p-4">
                <div class="flex justify-between">
                    <div class="flex gap-2 items-end uppercase">
                        <div class="flex gap-1 items-end">
                            <img class="w-10" src="{{ asset('images/icons/group.svg') }}" alt="">
                            <div>{{ $tour->group_size }} <span class="text-sm">{{ __('base.persons') }}</span>
                            </div>
                        </div>
                        <div class="flex gap-1 items-end">
                            <x-icons.clock-icon class="w-10" />
                            <div>{{ $tour->duration }} <span class="text-sm">{{ __('base.hours') }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-wotDark font-bold">
                        @if (!$tour->price_fixed)
                            <span class="uppercase text-sm font-normal">{{ __('base.from') }}</span>
                        @endif
                        {{ Number::currency($tour->price, in: 'euro', locale: $locale) }}
                    </div>
                </div>
                <div class="my-6">
                    {!! $tour->excerpt(50) !!}
                </div>
                <div class="flex justify-end">
                    <x-read-more href="{{ route($locale . '.foodtours.show', $tour) }}">
                        {{ __('base.food_tour_read_more') }}
                    </x-read-more>
                </div>
            </div>
        </div>
    @endforeach

</div>
