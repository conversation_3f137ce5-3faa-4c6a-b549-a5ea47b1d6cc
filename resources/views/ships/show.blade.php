<x-app-layout>
    @section('head')
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    @endsection


    @section('metatitle', $ship->getTranslation('meta_title', $locale) ? $ship->getTranslation('meta_title', $locale) :
        $ship->getTranslation('title', $locale))
    @section('metadescription', $ship->getTranslation('meta_description', $locale))
    @section('og_title', $ship->getTranslation('meta_title', $locale) ? $ship->getTranslation('meta_title', $locale) :
        $ship->getTranslation('title', $locale))
    @section('og_description', $ship->getTranslation('meta_description', $locale))

    @section('modified_date', $ship->updated_at)
    @section('og_image', asset('storage/' . $ship->imageLg()))


    <x-slot:header>
        <div class="relative">

            @if ($ship->video_id)
                <div id="player" class="pointer-events-none"></div>
            @else
                {{-- Desktop with parallax --}}
                <div class="hidden lg:block min-h-[500px] lg:min-h-[700px] bg-fixed lg:bg-left bg-no-repeat bg-cover"
                    style="background-image: url('{{ $ship->mainImage() ? asset('/storage/' . $ship->imageLg()) : asset('images/bulk/ships-header.webp') }}')"
                    aria-label="{{ $ship->alt() }} ">
                </div>
                {{-- Mobile without parallax --}}
                <div class="lg:hidden">
                    <img src="{{ $ship->mainImage() ? asset('storage/' . $ship->imageLg()) : asset('images/bulk/ships-header.webp') }}"
                        alt="{{ $ship->alt() }}" class="w-full">
                </div>
            @endif




            <img src="{{ asset('images/icons/scribble.svg') }}" alt="" class="absolute -bottom-1">
        </div>
    </x-slot:header>

    {{-- Breadcrumbs --}}
    <div class="w-[90%] lg:w-4/5 mx-auto lg:-mt-3 z-10 relative ">
        <ul class="text-sm text-gray-500 flex ">
            <li>
                <a href="/"><x-icons.home-icon /></a>
            </li>
            <li class="before:content-['/\00a0'] before:p-2">
                {{ $ship->getTranslation('title', $locale) }}
            </li>
        </ul>
    </div>

    <div class="w-[90%] lg:w-4/5 mx-auto my-4">
        @if (auth()->user() &&
                auth()->user()->hasAnyRole(['super admin', 'admin']))
            <div class="flex justify-end mb-4 -mt-4">
                @if (!$ship->published)
                    <span class="text-red-500 text-md">Admin Notification: Ship is not published</span>
                @endif
                <x-cerberus::form.link href="{{ route('cerberus.ships.edit', $ship) }}">Επεξεργασία
                </x-cerberus::form.link>

            </div>

        @endif
        <h1 class="text-center mb-4 font-bold text-pacific-800  text-7xl">{!! $ship->getTranslation('title', $locale) !!}</h1>

        <div>
            {!! $ship->getTranslation('content', $locale) !!}
        </div>

        <div class="my-12">
            <img class="lg:hidden" src="{{ asset('images/icons/cruise-doodle-mobile.svg') }}" alt="">
            <img class="hidden lg:block" src="{{ asset('images/icons/cruise-doodle-desktop.svg') }}" alt="">
        </div>


        @if ($ship->features->count())
            <section>
                <h2 class="text-center text-5xl capitalize font-bold text-pacific-800 my-8">
                    {{ __('base.main_features') }}
                </h2>
                <div>
                    @foreach ($ship->features->sortBy('order') as $feature)
                        <div class="feature  lg:px-6">

                            <div class="relative lg:flex lg:gap-6 lg:items-center">
                                @if ($feature->bannerImage())
                                    <div class="relative mb-2 lg:w-1/3 {{ $loop->even ? 'order-1' : 'order-2' }}">
                                        <div
                                            class="bg-pacific-500 p-4 text-center text-white text-3xl absolute top-0 left-2">
                                            {{ $loop->iteration }}
                                        </div>
                                        <img src="{{ asset('storage/ships/features/' . $feature->bannerImage()->filename) }}"
                                            alt="{{ $feature->bannerImage()->alt }}" class="w-full">
                                    </div>
                                @endif
                                <div
                                    class="{{ $feature->bannerImage() ? 'lg:w-2/3' : 'lg:w-full' }} p-4 lg:px-16 lg:py-12 mb-4 {{ $loop->even ? 'order-2' : 'order-1' }} bg-pacific-500 text-white ">
                                    <h3 class="text-3xl font-bold my-8">
                                        {{ $feature->getTranslation('title', $locale) }}
                                    </h3>
                                    {!! $feature->getTranslation('content', $locale) !!}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </section>
        @endif

        {{-- Galeries --}}
        @if ($ship->galleries->count())

            <section class="mt-16 mb-10">
                @foreach ($ship->galleries->sortBy('order') as $gallery)
                    <div class="my-12">
                        <h3
                            class="text-center font-semibold text-sun-500 text-3xl underline decoration-2 decoration-pacific-500 underline-offset-8">
                            {{ $gallery->getTranslation('title', $locale) }}
                        </h3>
                        <div class="mb-6">
                            {!! $gallery->getTranslation('content', $locale) !!}
                        </div>

                        <div class="mySwiper w-full h-full overflow-hidden">
                            <div class="swiper-wrapper">
                                @forelse ($gallery->images->sortBy('order') as $image)
                                    <div class="swiper-slide text-center flex justify-center items-center">
                                        <img src="{{ asset('storage/galleries/' . $gallery->id . '/' . $image->filename) }}"
                                            alt="{{ $image->alt }}" class="w-full h-[300px] block object-cover">
                                        @if ($image->caption)
                                            <div
                                                class="absolute bottom-3 left-0 right-0 bg-pacific-500/80 text-white p-2">
                                                {{ $image->caption }}
                                            </div>
                                        @endif
                                    </div>
                                @empty
                                @endforelse
                                <div class="swiper-scrollbar "></div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </section>
        @endif

        {{-- Related Packages --}}

        @if ($ship->packages->count())
            <section class="mt-16 mb-10">
                <h2 class="text-3xl text-center font-semibold text-pacific-500 mb-6">{{ __('base.cruises_with') }}
                    {{ $ship->getTranslation('title', $locale) }}</h2>
                <div class="flex flex-wrap justify-center">
                    @foreach ($ship->packages as $cruise)
                        @include('cruises.partials.__cruise-item')
                    @endforeach
                </div>
            </section>
        @endif

        <div class="my-12">
            <img class="lg:hidden" src="{{ asset('images/icons/cruise-doodle-mobile.svg') }}" alt="">
            <img class="hidden lg:block" src="{{ asset('images/icons/cruise-doodle-desktop.svg') }}" alt="">
        </div>

        @section('footer-scripts')

            <script>
                // 2. This code loads the IFrame Player API code asynchronously.
                var tag = document.createElement('script');

                tag.src = "https://www.youtube.com/iframe_api";
                var firstScriptTag = document.getElementsByTagName('script')[0];
                firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

                // 3. This function creates an <iframe> (and YouTube player)
                //    after the API code downloads.
                var player;

                function onYouTubeIframeAPIReady() {

                    let width = document.getElementById("player").offsetWidth;
                    let height = document.getElementById("player").offsetWidth * 9 / 16;

                    player = new YT.Player('player', {
                        height: height,
                        width: width,
                        videoId: '{{ $ship->video_id }}',
                        playerVars: {
                            'playsinline': 1,
                            'autoplay': 1,
                            'mute': 1,
                            'controls': 0,
                            'loop': 1,
                            'playlist': '{{ $ship->video_id }}',
                            'playsinline': 1,
                        },
                        events: {
                            'onReady': onPlayerReady,
                            'onStateChange': onPlayerStateChange
                        }
                    });
                }

                // 4. The API will call this function when the video player is ready.
                function onPlayerReady(event) {
                    event.target.playVideo();
                }

                // 5. The API calls this function when the player's state changes.
                function onPlayerStateChange(event) {
                    if (event.data == YT.PlayerState.ENDED) {
                        event.target.playVideo();
                    }
                }
            </script>

        @endsection

</x-app-layout>
