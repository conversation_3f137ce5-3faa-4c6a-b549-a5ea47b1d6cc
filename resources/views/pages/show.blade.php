<x-app-layout>

    @section('head')
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    @endsection

    @section('metatitle', $page->getTranslation('meta_title', $locale) ? $page->getTranslation('meta_title', $locale) :
        $page->getTranslation('title', $locale))

    @section('metadescription', $page->getTranslation('meta_description', $locale))

    <x-slot:header>
        <div class="relative">

            @if ($page->mainImage())
                <img src="{{ asset('/storage/' . $page->imageLg()) }}" alt="{{ $page->alt() }}" class="w-full">
            @else
                <div class="h-[250px] bg-darkCyan">

                </div>
            @endif

            <img src="{{ asset('images/icons/scribble.svg') }}" alt="" class="absolute -bottom-1">
        </div>
    </x-slot:header>



    <img src="{{ asset('images/icons/arrow-down-right.svg') }}" alt=""
        class="hidden xl:block absolute xl:left-0 xl:w-96">

    <section class="w-4/5 mx-auto my-8 relative">

        @if (auth()->user() &&
                auth()->user()->hasAnyRole(['super admin', 'admin']))
            <div class="flex justify-end mb-4 -mt-4">
                @if (!$page->published)
                    <span class="text-red-500 text-md">Admin Notification: Page is not published</span>
                @endif
                <x-cerberus::form.link href="{{ route('cerberus.pages.edit', $page) }}">Επεξεργασία
                </x-cerberus::form.link>
            </div>
        @endif

        <img src="{{ asset('images/icons/airplane.svg') }}" alt=""
            class="w-48 hidden xl:block absolute xl:right-0">

        <h1 class="text-center">{{ $page->getTranslation('title', $locale) }}</h1>

        <div class="mt-12">
            {!! $page->getTranslation('content', $locale) !!}
        </div>

    </section>

    <section class="w-4/5 mx-auto my-8 relative">

        {{-- Check if it is contact page --}}
        @if ($page->slug == 'contact-us' || $page->slug == 'epikoinonia')
            <livewire:forms.contact-form />

            @include('pages.partials.__google-map')

            {{-- ...or create package page --}}
        @elseif($page->slug == 'dimioyrgia-paketoy-diakopwn' || $page->slug == 'create-vacation-package')
            <livewire:forms.create-package-contact-form />
        @endif
    </section>

    <img src="{{ asset('images/icons/blog-travel-doodles.svg') }}" alt="">

</x-app-layout>
