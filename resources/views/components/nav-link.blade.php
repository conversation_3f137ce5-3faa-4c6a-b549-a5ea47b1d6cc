@props(['active'])

@php
$classes = ($active ?? false)
            ? 'text-nowrap items-center px-3 pt-2 text-sm font-medium leading-5 bg-white focus:outline-none focus:border-b focus:border-white-700 transition duration-700 '
            : 'text-nowrap items-center px-3 py-1 border-transparent font-medium leading-5 lg:hover:bg-white lg:hover:text-darkCyan focus:outline-none focus:text-gray-700 focus:border-gray-300 transition duration-700';
@endphp

<a {{ $attributes->merge(['class' => $classes.' text-xl cursor-pointer']) }}>
    {{ $slot }}
</a>
