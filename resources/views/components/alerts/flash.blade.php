@props([
    'type' => 'error',
    'colors' => [
        'success' => 'bg-pacific-200 border-pacific-600 text-white font-semibold title-box-shadow',
        'error' => 'bg-red-200 border-red-600 text-red-600',
        'info' => 'bg-indigo-200 border-indigo-600 text-indigo-600'
    ],
    'timeout' => false
])

@if(session()->has($type))
    <div x-data="{show: true}" >
        <div        
            x-show="show" x-transition
            @click.outside="show = false"
            @if($timeout)
                x-init="setTimeout(() => show = false, {{ $timeout * 1000}})"
            @endif
            class="{{$colors[$type]}} w-4/5 border-l-4 inline-block mx-auto mb-4 p-2 fixed bottom-16 lg:bottom-32 xl:bottom-8 right-4 left-4 z-50" role="alert"
        
            {{$attributes(['type' => $type])}}
        >
            <div class="flex justify-between">
                <p class="mt-1">
                    {{session($type)}}
                </p>
                <button
                    @click.prevent="show = false"
                    class="mx-3"
                >
                x
                </button>
            </div>
        </div>
    </div>
@endif
