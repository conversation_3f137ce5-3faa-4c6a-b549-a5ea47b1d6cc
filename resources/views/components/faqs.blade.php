@props(['collection'])

<div itemscope itemtype="https://schema.org/FAQPage" class="text-center">
    @foreach ($collection as $item)
        <div x-data="{ show: false }"
            class="border-b border-black mt-4 py-4 {{ $loop->first ? 'border-t' : '' }}" itemscope
            itemprop="mainEntity" itemtype="https://schema.org/Question">
            <h4 class="cursor-pointer text-darkCyan" @click="show = !show" itemprop="name">
                <span class="text-pacific-500">></span> {{ $item->getTranslation('question', $locale) }}
            </h4>
            <div class="mt-2 px-2" x-show="show" x-transition.duration.300ms itemscope
                itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                <div itemprop="text">
                    {!! $item->getTranslation('answer', $locale) !!}
                </div>
            </div>
        </div>
    @endforeach
</div>