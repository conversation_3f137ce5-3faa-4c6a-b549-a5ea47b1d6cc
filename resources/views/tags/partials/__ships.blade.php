<h2 class="text-center uppercase text-pacific-500 font-semibold">{{ $ships->count() > 1 ?  __('base.ships') :  __('base.ship') }}</h2>

<div class="flex gap-1 flex-wrap justify-evenly p-2">
    @foreach ($ships as $ship)
        <div class="bg-darkCyan mb-2 p-3 md:w-[48%] lg:w-[33%] xl:w-[24%] ">
            <a href="{{ route($locale.'.ship.show', $ship) }}">
                <img
                    src="{{ asset('storage/'. $ship->imageSm()) }}"
                    alt="{{ $ship->mainImage()?->getTranslation('alt', $locale) }}"
                    class="border border-white w-full"
                >
                <h3 class="text-center text-pacific-500 text-2xl font-semibold  my-3">{{ $ship->getTranslation('title', $locale) }}</h3>
            </a>
        </div>
    @endforeach
</div>
