<div class="w-4/5 mx-auto">
    
    <h2 class="mb-12 uppercase font-bold text-darkCyan text-5xl text-center" >{!! __('base.associated_posts') !!}</h2>

    <div class="flex flex-wrap justify-center gap-6">

        @foreach ($posts as $post)

        <div class="lg:w-[45%] xl:w-[30%] relative">
            <div class="{{ $post->mainImage() ? 'absolute left-2 top-2 inline' :'' }}  z-20 bg-pacific-500 text-white w-16 h-16 p-2 uppercase text-center">
                {{ Carbon\Carbon::parse($post->published_at)->translatedFormat('d M ') }}
            </div>
            @if ($post->mainImage())
                <a href="{{ route($locale . '.posts.show', $post) }}"><img src="{{ asset('storage/' . $post->imageMd()) }}"
                        alt="" class="w-full relative z-0"></a>
            @endif
        
            <div class="{{ $post->mainImage() ? '-mt-8 lg:-mt-24  mx-4' : '' }} relative z-20 bg-white border-2 border-pacific-800 py-4 px-6 ">
                <span>
                    @foreach ($post->tags()->whereLocale('title', App::getLocale())->get() as $tag)
                        <a href="{{ route($locale.'.tag.show', $tag) }}" class="uppercase text-pacific-500 text-sm">
                            {{ $tag->getTranslation('title', $locale) }}{{ !$loop->last ? ',' : '' }}
                        </a>
                    @endforeach
                </span>

                <h2 class="text-2xl text-darkCyan font-semibold"><a href="{{ route('posts.show', $post->slug) }}">{{ $post->title }}</a></h2>

                <p class="">{!! $post->excerpt(20) !!}</p>

                <div>
                    <x-read-more href="{{ route($locale . '.posts.show', $post) }}" />
                </div>

            </div>
        </div>
        
        @endforeach

    </div>

</div>