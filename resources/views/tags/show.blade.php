<x-app-layout>

    @section('head')
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    @endsection

    @if ($tag->type == 'tag')
        @section('metatitle', $tag->getTranslation('meta_title', $locale) ?
            strip_tags($tag->getTranslation('meta_title', $locale)) : strip_tags($tag->getTranslation('title', $locale)))
        @section('metadescription', $tag->getTranslation('meta_description', $locale))
    @else
        @section('metatitle', $tag->getTranslation('meta_title', $locale) ?
            strip_tags($tag->getTranslation('meta_title', $locale)) : strip_tags($tag->getTranslation('title', $locale)))
        @section('metadescription', $tag->getTranslation('meta_description', $locale))
    @endif

    <x-slot:header>

        @if ($tag->type == 'tag')
            {{-- Desktop with parallax --}}
            <div class="hidden lg:block min-h-[500px] lg:min-h-[700px] bg-fixed lg:bg-left bg-no-repeat bg-cover"
                style="background-image: url('{{ $tag->mainImage() ? asset('storage/' . $tag->imageLg()) : asset('images/bulk/blog-tag-header.webp') }}')"
                aria-label="{{ $tag->alt() }} ">

            </div>
            {{-- Mobile without parallax --}}
            <div class="lg:hidden">
                <img src="{{ $tag->mainImage() ? asset('storage/' . $tag->imageLg()) : asset('images/bulk/blog-tag-header.webp') }}"
                    alt="{{ $tag->alt() }}" class="w-full">
            </div>
        @else
            {{-- Desktop with parallax --}}
            <div class="hidden lg:block min-h-[500px] lg:min-h-[700px] bg-fixed bg-left bg-no-repeat bg-cover"
                style="background-image: url('{{ $tag->mainImage() ? asset('storage/' . $tag->imageLg()) : asset('images/bulk/packages-header.webp') }}')"
                aria-label="{{ $tag->alt() }}">

            </div>
            {{-- Mobile without parallax --}}
            <div class="lg:hidden">
                <img src="{{ $tag->mainImage() ? asset('storage/' . $tag->imageLg()) : asset('images/bulk/blog-tag-header.webp') }}"
                    alt="{{ $tag->alt() }}" class="">
            </div>
        @endif
        <img src="{{ asset('images/icons/scribble.svg') }}" alt="" class="absolute -bottom-1">

        </x-slot::header>

        {{-- Breadcrumbs --}}
        <div class="w-[90%] lg:w-4/5 mx-auto lg:-mt-3 z-10 relative ">
            <ul class="!text-sm text-gray-500 flex !list-none">
                <li>
                    <a href="/"><x-icons.home-icon /></a>
                </li>

                <li class="before:content-['/\00a0'] before:p-2">
                    @if ($tag->type == 'tag')
                        <a
                            href="{{ route($locale . '.tag.show', $tag) }}">{{ strip_tags($tag->getTranslation('title', $locale)) }}</a>
                    @else
                        <a
                            href="{{ route($locale . '.category.show', $tag) }}">{{ strip_tags($tag->getTranslation('title', $locale)) }}</a>
                    @endif

                </li>

            </ul>
        </div>

        {{-- Banners --}}
        @if ($banners->count())
            @include('templates.' .$tag->template. '.partials.__banners')
        @endif

        <div>
            @include('templates.' . $tag->template . '.tags.show.title-area')

            {{-- Content --}}
            <div class="mt-8 px-6 lg:px-8">
                {!! html_entity_decode($tag->content) !!}
            </div>
        </div>

        {{-- Top doodle --}}
        @include('templates.' . $tag->template . '.tags.show.top-doodle')

        {{-- Packages --}}
        @if ($packages->count())
            @include('templates.' . $tag->template . '.tags.show.packages')
        @endif

        {{-- Ship --}}
        @if ($ships->count())
            @include('templates.' . $tag->template . '.tags.show.ships')
        @endif

        {{-- CTA --}}
        {{-- @include('templates.' . $tag->template . '.partials.__cta') --}}

        {{-- Contact form --}}
        @include('templates.' . $tag->template . '.tags.show.contact-form')

        {{-- Blog Posts --}}
        @if ($posts->count())
            <section id="posts" class="relative bg-lightGrey mt-[5rem] mb-12 py-12">
                <img src="{{ asset('images/icons/cardpostal-heart.svg') }}" alt=""
                    class="w-[10rem] absolute -top-12 left-6">
                <img src="{{ asset('images/icons/arrow-down.svg') }}" alt=""
                    class="h-[5rem] xl:h-[7rem] 2xl:h-[10rem] absolute  -top-12 right-0 xl:-right-[2rem] 2xl:-right-10 3xl:right-0">

                @include('tags.partials.__posts')

                <div class="flex justify-center w-4/5 mx-auto mt-10">


                    <x-cta-primary>
                        <a href="{{ route($locale . '.posts.index') }}">Διαβάστε Περισσότερα</a>
                    </x-cta-primary>
                </div>

                <img src="{{ asset('images/icons/envelope-glasses.svg') }}" alt=""
                    class="w-[10rem] xl:w-[14rem] absolute right-8 xl:right-[10rem] bottom-6">
            </section>
        @endif

        {{-- Faqs --}}
        @if ($faqs->count())
            <section class="w-4/5 mx-auto my-8">
                @include('templates.' . $tag->template . '.tags.show.faqs')
                <x-faqs :collection="$faqs" />
            </section>
        @endif


        <div class="pb-2">
            <img src="{{ asset('/images/icons/blog-travel-doodles.svg') }}" alt="">
        </div>
</x-app-layout>
