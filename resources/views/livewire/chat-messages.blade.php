@if (!empty($messages))
    @foreach ($messages as $key => $message)
        @if ($message['role'] === 'user')
            <div class="w-3/4 space-y-0.5 self-end">
                <div class="text-right text-white mt-3">{{ __('chat.visitor') }}</div>
                <div class="rounded-xl rounded-tr-none px-3 py-1.5 text-chat bg-white">
                    {{ $message['content'] }}
                </div>
            </div>
        @elseif ($message['role'] === 'assistant')
            <div class="w-3/4 space-y-0.5">
                <div class="text-chat text-white flex items-center mt-3">

                    <img src="{{ asset('images/bulk/emy.webp') }}" alt="" class="w-8 rounded-full mr-2">

                    <div>Emy</div>
                </div>
                <div class="bg-slate-200 min-h-10 rounded-xl rounded-tl-none px-3 py-1.5 text-chat">

                    @if (!empty($message['content']))
                        <div  
                            x-data="{ playSound: () => { 
                            @if (app()->isProduction())
                               new Audio('{{ asset('sounds/emy-sound.wav') }}').play();  
                            @endif
                            } }" 
                            x-init="playSound()" 
                            >
                            {!! $message['content'] !!}
                        </div>
                    @else
                        <div wire:loading.class="!flex flex-row gap-2 items-center w-full mt-3">
                            <div
                                wire:loading.class='h-[3px] w-[3px] bg-slate-800 rounded-full animate-bounce [animation-delay:-0.3s]'>
                            </div>
                            <div
                                wire:loading.class='h-[3px] w-[3px] bg-slate-800 rounded-full animate-bounce [animation-delay:-0.15s]'>
                            </div>
                            <div wire:loading.class='h-[3px] w-[3px] bg-slate-800 rounded-full animate-bounce'></div>
                        </div>
                    @endif
                </div>
            </div>
        @elseif ($message['role'] === 'toolResult')
            <div class="text-chat text-white flex items-center mt-3">

                <img src="{{ asset('images/bulk/emy.webp') }}" alt="" class="w-8 rounded-full mr-2">

                <div>Emy</div>
            </div>
            <div class="bg-slate-200 rounded-xl rounded-tl-none text-chat flex justify-end gap-1 items-center mt-2">

                <div class="px-2">
                    <p class="mb-0 text-chat">
                    <div>{{ json_decode($message['content'], true)['result'] }}</div>
                    </p>
                </div>
            </div>
            <hr class="m-2">
        @endif
    @endforeach
@endif
