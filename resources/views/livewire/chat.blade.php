<div x-data="{show: false, chatOpen: $persist(null).using(sessionStorage)}" x-init="chatOpen == false ? show = false :  setTimeout(() => {show = true; chatOpen = true}, 5000) ">
    <div x-show="chatOpen !== false ? chatOpen : show" x-transition
        style="display: none;"
        class="max-w-lg mx-auto mb-3 border border-white rounded-xl overflow-hidden fixed z-50 bottom-1 right-2 lg:right-6 w-[300px] lg:w-[400px]
        bg-gradient-to-t from-[#0d838f]  to-darkCyan
         ">
        <img src="{{ asset('images/logo/logo-w.webp') }}" alt="" class="w-2/3">

        <span class="absolute top-1 right-5 text-white cursor-pointer" @click="show = false; chatOpen = false">-</span>

        <div class="p-6 flex space-y-1.5 overflow-scroll flex-col-reverse  h-[50vH] ">

            <div class="flex flex-col">

                <div class="flex justify-end gap-1 items-center">
                    <div>
                        <img src="{{ asset('images/bulk/emy.webp') }}" alt="" class="w-24 rounded-full">
                    </div>
                    <div class="px-2 text-white">
                        <p class="mb-0 text-chat">
                            Γεια σας! Είμαι η Emy, από το World of Travel. Το ταξιδιωτικό γραφείο μας εξυπηρετεί τους πελάτες του 24/7.</p>
                        <p class="mτ-1 text-chat">
                            Είμαι εδώ για να να οργανώσουμε μαζί το επόμενο ταξίδι σας. Πού σκέφτεστε να ταξιδέψετε σήμερα;
                        </p>
                    </div>
                </div>

                @include('livewire.chat-messages', ['messages' => (array)request()->session()->get('emy_chat_history')])

                @include('livewire.chat-messages', ['messages' => (array)request()->session()->get('emy_chat')])

            </div>

        </div>
        <div wire:poll.visible>
            <form wire:submit="send" class=" p-3 flex space-x-2">
                <div class="rounded-full grow relative flex items-center h-10 ">
                    <input
                        class="bg-white rounded-full grow px-4 py-2 text-sm h-10 border-gray-300 focus:outline-none focus:ring-pacific-500 focus:border-none"
                        placeholder="Γράψτε μου το μήνυμά σας" wire:model="body" />
                    <button
                        class="bg-pacific-500 ml-1 text-slate-50 rounded-3xl text-chat font-medium size-10 flex items-center justify-center">
                        <x-icons.send-icon />
                    </button>
                </div>
            </form>
        </div>
    </div>
    <div x-show="chatOpen !== false ? !chatOpen : !show" class="fixed z-[150] bottom-3 right-0 lg:right-6 cursor-pointer" @click="show = true; chatOpen=true" x-transition x-cloak="chatOpen=false">
        <img src="{{ asset('images/bulk/emy.webp') }}" alt="" class="w-24 rounded-full border-2 border-darkCyan">
        <div class="text-center bg-darkCyan text-white rounded-lg mt-1">Emy</div>
    </div>
</div>
