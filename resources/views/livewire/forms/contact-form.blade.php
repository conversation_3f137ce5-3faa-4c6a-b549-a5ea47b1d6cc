<div class="">

    <div class="p-4 bg-pacific-500 text-center text-white text-2xl uppercase">
        {{ __('form.title-contact') }}
    </div>
    <div class="bg-lightGrey p-3">
        <form @submit.prevent="doCaptcha" x-data="{
            siteKey: @js(config('services.recaptcha.site_key')),
            init() {
                // load our recaptcha.
                if (!window.recaptcha) {
                    const script = document.createElement('script');
                    script.src = 'https://www.google.com/recaptcha/api.js?render=' + this.siteKey;
                    document.body.append(script);
                }
            },
        
            doCaptcha() {
                grecaptcha.execute(this.siteKey, { action: 'submit' }).then(token => {
                    Livewire.dispatch('formSubmitted', { token: token });
                });
            },
        }">
            @csrf

            <p class="mt-2 mb-6">{!! __('form.contact-prompt') !!}</p>

            <div class="md:flex">
                <x-form.field-block class="md:w-1/2">
                    <x-form.label for="first_name" :required="true">
                        {{ __('form.first_name.name') }}
                    </x-form.label>
                    <x-form.input id="first_name" name="first_name" wire:model.blur="first_name" />
                    <x-form.error value="first_name" />
                </x-form.field-block>

                <x-form.field-block class="md:w-1/2">
                    <x-form.label for="last_name" :required="true">
                        {{ __('form.last_name.name') }}
                    </x-form.label>
                    <x-form.input id="last_name" name="last_name" wire:model.blur="last_name" />
                    <x-form.error value="last_name" />
                </x-form.field-block>
            </div>

            <div class="md:flex">
                <x-form.field-block class="md:w-1/2">
                    <x-form.label for="email" :required="true">
                        {{ __('form.email.name') }}
                    </x-form.label>
                    <x-form.input id="email" name="email" wire:model.blur="email" />
                    <x-form.error value="email" />
                </x-form.field-block>

                <x-form.field-block class="md:w-1/2">
                    <x-form.label for="mobile" :required="true">
                        {{ __('form.mobile.name') }}
                    </x-form.label>
                    <x-form.input id="mobile" type="tel" name="mobile" wire:model.blur="mobile" />
                    <x-form.error value="mobile" />
                </x-form.field-block>
            </div>

            <x-form.field-block class="">
                <x-form.label for="subject">
                    {{ __('form.subject') }}
                </x-form.label>
                <x-form.input id="subject" name="subject" wire:model.blur="subject" />
                <x-form.error value="subject" />
            </x-form.field-block>

            <x-form.field-block>
                <x-form.label for="message">
                    {{ __('form.message.name') }}
                </x-form.label>
                <x-form.textarea name="message" wire:model="message">

                </x-form.textarea>
            </x-form.field-block>

            <x-form.info>
                {{ __('form.asterisk') }}
            </x-form.info>

            <x-form.field-block>

                <x-form.label for="message" class="flex gap-2 text-sm">

                    <x-form.input id="message" type="checkbox" name="" wire:model="accept_terms"
                        class="!w-2 border !border-slate-500" />

                    {{ __('form.gdpr.name') }}

                </x-form.label>
                <x-form.error value="accept_terms" />

            </x-form.field-block>

            <x-form.error value="recaptcha" />

            <div class="flex justify-center my-8">
                <button
                    class="bg-pacific-500 hover:bg-wotDark title-box-shadow text-white hover:text-pacific-500 text-lg font-bold uppercase p-3">
                    {{ __('form.submit') }} </button>
            </div>
        </form>

        <x-alerts.flash type="success" timeout=7 />


    </div>
</div>
