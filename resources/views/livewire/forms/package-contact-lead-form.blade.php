<div class="w-[90%] lg:w-4/5 mx-auto my-12" id="contact-form">

    <div class="p-4 bg-pacific-500 text-center text-white text-2xl uppercase">
        {{ __('form.title') }}
    </div>
    <div class="bg-lightGrey p-3">
        <form @submit.prevent="doCaptcha" x-data="{
            siteKey: @js(config('services.recaptcha.site_key')),
            init() {
                // load our recaptcha.
                if (!window.recaptcha) {
                    const script = document.createElement('script');
                    script.src = 'https://www.google.com/recaptcha/api.js?render=' + this.siteKey;
                    document.body.append(script);
                }
            },
        
            doCaptcha() {
                grecaptcha.execute(this.siteKey, { action: 'submit' }).then(token => {
                    Livewire.dispatch('formSubmitted', { token: token });
                });
            },
        }">
            @csrf

            <p class="mt-2 mb-6">{!! __('form.prompt', ['package' => $package->getTranslation('title', $locale)]) !!}</p>

            <div class="md:flex">
                <x-form.field-block class="md:w-1/2">
                    <x-form.label for="first_name" :required="true">
                        {{ __('form.first_name.name') }}
                    </x-form.label>
                    <x-form.input id="first_name" name="first_name" wire:model.blur="first_name" />
                    <x-form.error value="first_name" />
                </x-form.field-block>

                <x-form.field-block class="md:w-1/2">
                    <x-form.label for="last_name" :required="true">
                        {{ __('form.last_name.name') }}
                    </x-form.label>
                    <x-form.input id="last_name" name="last_name" wire:model.blur="last_name" />
                    <x-form.error value="last_name" />
                </x-form.field-block>
            </div>

            <div class="md:flex">
                <x-form.field-block class="md:w-1/2">
                    <x-form.label for="email" :required="true">
                        {{ __('form.email.name') }}
                    </x-form.label>
                    <x-form.input id="email" name="email" wire:model.blur="email" />
                    <x-form.error value="email" />
                </x-form.field-block>

                <x-form.field-block class="md:w-1/2">
                    <x-form.label for="mobile" :required="true">
                        {{ __('form.mobile.name') }}
                    </x-form.label>
                    <x-form.input id="mobile" type="tel" name="mobile" wire:model.blur="mobile" />
                    <x-form.error value="mobile" />
                </x-form.field-block>
            </div>

            @if ($message_type == 'travel')
                <div class="md:flex">
                    <x-form.field-block class="md:w-2/3">
                        <x-form.label for="address">
                            {{ __('form.address.name') }}
                        </x-form.label>
                        <x-form.input id="address" name="address" wire:model.blur="address" />
                        <x-form.error value="address" />
                    </x-form.field-block>

                    <x-form.field-block class="md:w-1/3">
                        <x-form.label for="city">
                            {{ __('form.city.name') }}
                        </x-form.label>
                        <x-form.input id="city" name="city" wire:model.blur="city" />
                        <x-form.error value="city" />
                    </x-form.field-block>
                </div>
            @endif



            <div class="flex items-end">
                <x-form.field-block class="w-1/2">
                    <x-form.label for="persons" :required="true">
                        {{ __('form.persons.name') }}
                    </x-form.label>
                    <x-form.input id="persons" type="number" name="persons" wire:model.blur="persons" />
                    <x-form.error value="persons" />
                </x-form.field-block>

                <x-form.field-block class="w-1/2">
                    <x-form.label for="kids">
                        {{ __('form.kids.name') }}
                    </x-form.label>
                    <x-form.input id="kids" type="number" name="kids" wire:model.blur="kids" />
                    <x-form.error value="kids" />
                </x-form.field-block>

            </div>

            <x-form.field-block class="">
                <x-form.label for="depart_date">
                    {{ $message_type == 'travel' ? __('form.depart_date.name_travel') : __('form.depart_date.name_food') }}
                </x-form.label>
                <x-form.input id="depart_date" name="depart_date" wire:model.blur="depart_date" />
                <x-form.error value="depart_date" />
            </x-form.field-block>

            <x-form.field-block>
                <x-form.label for="message">
                    {{ __('form.message.name') }}
                </x-form.label>
                <x-form.textarea name="message" wire:model="message">

                </x-form.textarea>
            </x-form.field-block>

            <x-form.info>
                {{ __('form.asterisk') }}
            </x-form.info>

            <x-form.field-block>

                <x-form.label for="message" class="flex gap-2 text-sm">

                    <x-form.input id="message" type="checkbox" name="" wire:model="accept_terms"
                        class="!w-2 border !border-slate-500" />

                    {{ __('form.gdpr.name') }}

                </x-form.label>
                <x-form.error value="accept_terms" />

            </x-form.field-block>

            <x-form.error value="recaptcha" />

            <div class="flex justify-center my-8">
                <button
                    class="bg-pacific-500 hover:bg-wotDark title-box-shadow text-white hover:text-pacific-500 text-lg font-bold uppercase p-3">
                    {{ __('form.submit') }} </button>
            </div>
        </form>

        <x-alerts.flash type="success" timeout=7 />

        @section('footer-scripts')
            <script>
                flatpickr("#depart_date", {
                    dateFormat: "d-m-Y",
                    minDate: "today"
                });
            </script>
        @endsection

    </div>
</div>
