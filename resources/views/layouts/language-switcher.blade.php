<div x-data="{ showLang: false, caretDown: true }" @click.outside="showLang = false"
    class="ml-3 relative bg:wotDark lg:bg-pacific-500 text-white flex">

    <button x-show="caretDown" @click="showLang=true; caretDown = false"
        class="relative flex gap-2 px-8 py-4">{{ __('base.' . app()->getLocale()) }} <span
            class="inline-block rotate-180">&#94;</span>
    </button>

    <button x-show="!caretDown" @click="showLang=false; caretDown = true" style="display: none;"
        class="relative flex gap-2 px-8 py-4">{{ __('base.' . app()->getLocale()) }} <span
            class="inline-block ">&#94;</span>
    </button>

    <div class="absolute top-12 left-0 right-0 z-50 py-2 bg-pacific-500" style="display: none;" x-show="showLang"
        class="flex flex-col">
        @if (Route::isLocalized() || Route::isFallback())
            @foreach (LocaleConfig::getLocales() as $locale)
                @if (!App::isLocale($locale))
                    <div class="w-full py-2 px-8">

                        @php
                            if (request()->routeIs('el.travel.show') && App::getLocale() == 'el') {
                                $slug = Request::segment(2);
                                $package = App\Models\Package::where('slug->el', $slug)->first();
                            }
                            if (request()->routeIs('el.foodtours.show') && App::getLocale() == 'el') {
                                $slug = Request::segment(2);
                                $foodtour = App\Models\Package::where('slug->el', $slug)->first();
                            }
                            if (request()->routeIs('el.cruises.show') && App::getLocale() == 'el') {
                                $slug = Request::segment(2);
                                $cruise = App\Models\Package::where('slug->el', $slug)->first();
                            }
                            if (request()->routeIs('el.posts.show') && App::getLocale() == 'el') {
                                $slug = Request::segment(2);
                                $post = App\Models\Post::where('slug->el', $slug)->first();
                            }
                            if (request()->routeIs('el.tag.show') && App::getLocale() == 'el') {
                                $slug = Request::segment(2);
                                $tag = App\Models\Tag::tag()->where('slug->el', $slug)->first();
                            }
                            if (request()->routeIs('el.category.show') && App::getLocale() == 'el') {
                                $slug = Request::segment(2);
                                $category = App\Models\Tag::category()->where('slug->el', $slug)->first();
                            }
                            if (request()->routeIs('el.pages.show') && App::getLocale() == 'el') {
                                $slug = Request::segment(2);
                                $page = App\Models\Page::where('slug->el', $slug)->first();
                            }
                            if (request()->routeIs('el.ship.show') && App::getLocale() == 'el') {
                                $slug = Request::segment(2);
                                $ship = App\Models\Ship::where('slug->el', $slug)->first();
                            }

                        @endphp

                        @if (request()->routeIs('el.travel.show') && $package && $package->getTranslation('title', 'en') == '')
                            <a href="{{ route($locale . '.travel.index') }}">{{ __('base.' . $locale) }}</a>
                        @elseif (request()->routeIs('el.foodtours.show') && $foodtour && $foodtour->getTranslation('title', 'en') == '')
                            <a href="{{ route($locale . '.food-tours.index') }}">{{ __('base.' . $locale) }}</a>
                        @elseif (request()->routeIs('el.cruises.show') && $cruise && $cruise->getTranslation('title', 'en') == '')
                            <a href="{{ route($locale . '.cruises.index') }}">{{ __('base.' . $locale) }}</a>
                        @elseif (request()->routeIs('el.posts.show') && $post && $post->getTranslation('title', 'en') == '')
                            <a href="{{ route($locale . '.posts.index') }}">{{ __('base.' . $locale) }}</a>
                        @elseif (request()->routeIs('el.tag.show') && $tag && $tag->getTranslation('title', 'en') == '')
                            <a href="{{ route($locale . '.posts.index') }}">{{ __('base.' . $locale) }}</a>
                        @elseif (request()->routeIs('el.category.show') && $category && $category->getTranslation('title', 'en') == '')
                            <a href="{{ route($locale . '.posts.index') }}">{{ __('base.' . $locale) }}</a>
                        @elseif (request()->routeIs('el.pages.show') && $page && $page->getTranslation('title', 'en') == '')
                            <a href="{{ route('en.home') }}">{{ __('base.' . $locale) }}</a>
                        @elseif (request()->routeIs('el.ship.show') && $ship && $ship->getTranslation('title', 'en') == '')
                            <a href="{{ route('en.home') }}">{{ __('base.' . $locale) }}</a>
                        @else
                            <a href="{{ Route::localizedUrl($locale) }}">{{ __('base.' . $locale) }}</a>
                        @endif

                    </div>
                @endif
            @endforeach
        @endif
    </div>
</div>
