<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>

    {{-- Tag Manager Head --}}
    @if (app()->isProduction())
        @include('layouts.partials.__tag-manager-head')
    @endif

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    {{-- Meta tags --}}
    <title>@yield('metatitle', 'ΠΑΚΕΤΑ ΔΙΑΚΟΠΩΝ | World of Travel')</title>
    <meta name="description" content="@yield('metadescription', 'ΠΑΚΕΤΑ ΔΙΑΚΟΠΩΝ. Ταξιδέψτε σε όλο το κόσμο και ζήστε την απόλυτη ταξιδιωτική εμπειρία. Ταξιδιωτικό γραφείο Καλλιθέα, Αθήνα.')" />
    <meta property="og:locale" content="el_GR" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="@yield('og_title', 'ΠΑΚΕΤΑ ΔΙΑΚΟΠΩΝ | World of Travel')" />
    <meta property="og:description" content="@yield('og_description', 'ΠΑΚΕΤΑ ΔΙΑΚΟΠΩΝ. Ταξιδέψτε σε όλο το κόσμο και ζήστε την απόλυτη ταξιδιωτική εμπειρία. Ταξιδιωτικό γραφείο Καλλιθέα, Αθήνα.')" />
    <meta property="og:url" content="{{ request()->url() }}" />
    <meta property="og:site_name" content="World of Travel" />
    <meta property="article:publisher" content="https://www.facebook.com/world.of.travel.by.alice/" />
    <meta property="article:modified_time" content="@yield('modified_date', Carbon\Carbon::now())" />
    <meta property="og:image" content="@yield('og_image', asset('images/bulk/share-image.jpg'))" />
    <meta property="og:image:width" content="1280" />
    <meta property="og:image:height" content="831" />
    <meta property="og:image:type" content="image/jpeg" />
    <meta name="twitter:card" content="summary_large_image" />


    {{-- Favicon --}}
    <link rel="icon" type="image/png" href="{{ asset('images/favicon/favicon-96x96.png') }}" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="{{ asset('images/favicon/favicon.svg') }}" />
    <link rel="shortcut icon" href="{{ asset('images/favicon/favicon.ico') }}" />
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/favicon/apple-touch-icon.png') }}" />
    <link rel="manifest" href="{{ asset('images/favicon/site.webmanifest') }}" />


    @livewireStyles
    <!-- Scripts -->
    @vite(['resources/css/frontend.css', 'resources/js/app.js'])

    @yield('head')
    @if (app()->isProduction())
        @include('layouts.partials.__consent-mode')

        {{-- Google verification --}}
        <meta name="google-site-verification" content="TrRW-PuMcUTOOwgyhmAonHyDE3-4jSuJK3afAv9dDzU" />
        <!-- Start cookieyes banner -->
        <script id="cookieyes" type="text/javascript"
            src="https://cdn-cookieyes.com/client_data/794fba3a57b4dc27ea420453/script.js"></script> <!-- End cookieyes banner -->
    @endif

</head>

<body
    class="relative font-sans antialiased max-w-[1920px] mx-auto text-base {{ request()->routeIs($locale . '.posts.*') ? 'blog' : '' }}">

    {{-- Tag Manager Body --}}
    @if (app()->isProduction())
        @include('layouts.partials.__tag-manager-body')
    @endif

    <div class="min-h-screen">

        @include('layouts.heading')


        <!-- Page Heading -->
        @if (isset($header))
            <header class="relative">
                <div
                    class="absolute top-0 left-0 right-0 h-24 z-10 bg-gradient-to-b from-pacific-500 via-pacific-500/70 to-transparent xl:from-darkCyan/50 xl:via-darkCyan/30 xl:to-transparent">
                </div>
                @include('layouts.navigation')
                {{ $header }}
            </header>
        @endif

        <!-- Page Content -->
        <main>
            {{ $slot }}
        </main>

        <footer class="mb-8">
            @include('layouts.footer')
        </footer>


        {{-- Chatbot - Emy || Does not appear if logged user is admin --}}
        @if ((app()->getLocale() == 'el' && auth()->user() == null) || auth()->user() && auth()->user()->type !== 'admin')
            <livewire:chat />
        @endif


    </div>

    {{-- Mobile phone call button --}}
    <div class="md:hidden fixed bottom-16 left-4 z-1000 text-center">
        <div class="bg-green-600 mx-auto w-[3.5rem] h-[3.5rem] p-3 rounded-full ">
            <a href="tel:+302102207506">
                <x-icons.phone-icon class="w-[2rem] stroke-white " />
            </a>
        </div>
    </div>

    {{-- Popup banner --}}
    @if ($popup)
        <div x-data="{
            showBanner: false,
            lastShown: $persist(null),
            shouldShow() {
                if (!this.lastShown) return true;
                const lastDate = new Date(this.lastShown);
                const today = new Date();
                return lastDate.toDateString() !== today.toDateString();
            }
        }" x-init="if (shouldShow()) {
            setTimeout(() => {
                showBanner = true;
                lastShown = new Date().toISOString();
            }, 20000);
        } else {
            showBanner = false;
        }" @keyup.escape.window="showBanner = false">
            <div x-cloak x-show="showBanner" x-transition
                class="fixed top-0 bottom-0 left-0 right-0 z-50 bg-wotDark/60">
                <span class="cursor-pointer text-white absolute right-4 top-6" @click="showBanner=false">x</span>

                <a href="{{ $popup->url }}" class="block w-[95%] lg:w-1/2 mx-auto my-16 lg:my-4 ">
                    <img src="{{ asset('storage/banners/' . $popup->bannerImage()->filename) }}"
                        @click.outside="showBanner = false" alt="{{ $popup->bannerImage()->alt }}"
                        class="mx-auto lg:max-h-[800px]">
                </a>

            </div>
        </div>
    @endif

    @livewireScripts

    @yield('footer-scripts')

</body>

</html>
