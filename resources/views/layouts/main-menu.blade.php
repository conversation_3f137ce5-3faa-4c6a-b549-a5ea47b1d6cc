<div
    class="
    flex flex-col xl:flex-row xl:justify-evenly gap-4 xl:grow xl:items-start xl:relative 
    top-0 lg:-top-auto bottom-0
    left-0 lg:left-auto -right-3 lg:right-0 z-50 
    bg-darkCyan/90 lg:bg-transparent 
    text-center
    lg:p-4
    ">


    <div class="flex justify-between items-center lg:hidden bg-pacific-500 px-4">
        <x-logo-white class="w-1/2" />

        <button @click="open = ! open"
            class="inline-flex items-center justify-center 
        p-2 rounded-md text-white 
        focus:outline-none 
        transition duration-150 ease-in-out
        right-1 bottom-1">
            <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">


                <path :class="{ 'hidden': !open, 'inline-flex': open }" class="hidden" stroke-linecap="round"
                    stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    @if (isset($menu))
        <div class="relative flex flex-col lg:flex-row xl:flex-wrap gap-2 mt-12 lg:mt-0 lg:-mb-6">

            @foreach ($menu->menuItems->where('parent_id', 0)->sortBy('order') as $item)
                <div class="relative mx-1" x-data="{ showMenu: false, timeout: null }"
                    @if ($item->hasChildren()) @mouseover="clearTimeout(timeout); showMenu = true"
                        @mouseleave="timeout = setTimeout(() => showMenu = false, 50)" @endif>

                    @if ($item->hasChildren() && $item->children()->count())
                        @if ($item->link)
                            <x-nav-link href="{{ $item->link }}"
                                class="{{ $item->children()->pluck('link')->contains(request()->getRequestUri())? 'text-pacific-500 lg:bg-white lg:text-darkCyan ': 'text-white' }}">
                                {{ $item->title }}
                            </x-nav-link>
                        @else
                            <x-nav-link
                                class="{{ $item->children()->pluck('link')->contains(request()->getRequestUri())? 'text-pacific-500 lg:bg-white lg:text-darkCyan': 'text-white' }} !cursor-default transition duration-700">
                                {{ $item->title }}
                            </x-nav-link>
                        @endif
                        <x-icons.arrow-right class="inline lg:hidden text-white w-4 cursor-pointer"
                            @click="showMenu = true" x-show="!showMenu" />
                        <x-icons.arrow-down class="inline lg:hidden text-white w-4 cursor-pointer"
                            @click="showMenu = false" x-show="showMenu" />


                        <div class="relative block -ml-1 lg:pt-6 z-10">
                            <div x-show="showMenu" x-transition.duration.750 @mouseleave="showMenu = false"
                                style="display: none;"
                                class="relative lg:absolute lg:top-4 lg:-right-4 lg:w-64 flex flex-col items-center p-2 ">
                                @foreach ($item->children()->sortBy('order') as $subItem)
                                    <x-nav-link href="{{ $subItem->link }}"
                                        class="!text-wrap w-full !text-[90%] text-white py-1 -ml-2 mb-1 {{ request()->getRequestUri() == $subItem->link ? 'bg-pacific-500' : 'bg-sun-500' }} transition duration-700 ">
                                        {{ $subItem->title }}
                                    </x-nav-link>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <x-nav-link href="{{ $item->link }}"
                            class="relative  
                                {{ $item->link == request()->getRequestUri() ? 'text-pacific-500 lg:text-white' : 'text-white' }}
                            ">

                            {{ $item->title }}
                        </x-nav-link>
                    @endif


                </div>
            @endforeach

        </div>
    @endif


    <div class="xl:hidden bg-wotDark flex flex-col items-center">
        {{-- @include('layouts.partials.__shop-icons') --}}

    </div>
</div>
