<x-app-layout>
    @section('head')
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    @endsection


    @section('metatitle', $package->getTranslation('meta_title', $locale) ? $package->getTranslation('meta_title',
        $locale) : $package->getTranslation('title', $locale))
    @section('metadescription', $package->getTranslation('meta_description', $locale))
    @section('og_title', $package->getTranslation('meta_title', $locale) ? $package->getTranslation('meta_title',
        $locale) : $package->getTranslation('title', $locale))
    @section('og_description', $package->getTranslation('meta_description', $locale))

    @section('modified_date', $package->updated_at)
    @section('og_image', asset('storage/' . $package->imageLg()))


    <x-slot:header>

        {{-- Desktop with parallax --}}
        <div class="hidden lg:block lg:min-h-[700px] bg-fixed bg-left bg-no-repeat bg-cover"
            style="background-image: url('{{ $package->mainImage() ? asset('/storage/' . $package->imageLg()) : asset('images/bulk/packages-header.webp') }}')"
            aria-label="{{ $package->alt() }} ">
        </div>
        {{-- Mobile without parallax --}}
        <div class="lg:hidden">
            <img src="{{ $package->mainImage() ? asset('storage/' . $package->imageLg()) : asset('images/bulk/blog-tag-header.webp') }}"
                alt="{{ $package->alt() }}" class="">
        </div>
        <img src="{{ asset('images/icons/scribble.svg') }}" alt="" class="absolute -bottom-1">

    </x-slot:header>

    {{-- Breadcrumbs --}}
    <div class="w-[90%] lg:w-4/5 mx-auto -mt-3 z-10 relative ">
        <ul class="text-sm text-gray-500 flex ">
            <li>
                <a href="/"><x-icons.home-icon /></a>
            </li>
            <li class="before:content-['/\00a0'] before:p-2">
                {{ $package->getTranslation('title', $locale) }}
            </li>
        </ul>
    </div>

    <div class="w-[90%] lg:w-4/5 mx-auto my-4">
        @if (auth()->user() &&
                auth()->user()->hasAnyRole(['super admin', 'admin']))
            <div class="flex justify-end mb-4 -mt-4">
                @if (!$package->published)
                    <span class="text-red-500 text-md">Admin Notification: Package is not published</span>
                @endif
                <x-cerberus::form.link href="{{ route('cerberus.packages.edit', $package) }}">Επεξεργασία
                </x-cerberus::form.link>

            </div>

        @endif

        <h1 class="text-center mb-4">{{ $package->getTranslation('title', $locale) }}</h1>

        @if ($package->duration)
            <div
                class="uppercase my-8 text-center text-2xl md:text-3xl font-extrabold text-pacific-500 flex justify-center gap-2">
                <img src="{{ asset('images/icons/clock-darkCyan.svg') }}" alt="" class="w-8 ">
                {{ $package->duration }} {{ __('base.days') }}
            </div>
        @endif


        <div class="text-pacific-500 uppercase my-8 text-center">
            @if ($package->sku)
                <span class="bg-pacific-500 text-white px-6 py-2 rounded-3xl font-extrabold">{{ __('base.sku') }}:
                    {{ $package->sku }}</span>
            @endif
        </div>

        @if ($package->subtitle)
            <div class="bg-grey text-center px-6 py-2 rounded-3xl font-extrabold">
                <h2 class="font-semibold text-darkCyan my-0">{{ $package->getTranslation('subtitle', $locale) }}</h2>
            </div>
        @endif


        <div class="my-4 my-8">
            <p>{{ $package->getTranslation('tagline', $locale) }}</p>
        </div>

        <div class="my-8 lg:my-12">
            {!! $package->getTranslation('content', $locale) !!}
        </div>

        <div class="flex flex-wrap gap-4 justify-between mb-8">
            <div class="w-full xl:w-7/12 order-2 lg:order-1 mt-8 lg:mt-0">
                {{-- Package Info & Itinerary steps --}}
                <div x-data="{ show: 1 }" class=" relative">
                    <div>
                        <div
                            class="my-2 p-4 bg-pacific-500 flex flex-col lg:flex-row items-center gap-1 lg:gap-2 uppercase text-white font-semibold text-2xl text-center lg:divide-x-2 divide-white">
                            <div class="w-full  cursor-pointer px-2"
                                :class="show == 1 ? 'text-darkCyan font-extrabold' : ''"
                                @click="show != 1 ? show = 1 : show = false ">
                                {{ __('base.itinerary') }}
                            </div>
                            <div class="w-full  cursor-pointer px-2"
                                :class="show == 2 ? 'text-darkCyan font-extrabold' : ''"
                                @click="show != 2 ? show = 2 : show = false ">
                                {{ __('base.info') }}
                            </div>
                            @if ($package->pricelistImage())
                                <div class="w-full  cursor-pointer px-2"
                                    :class="show == 3 ? 'text-darkCyan font-extrabold' : ''"
                                    @click="show != 3 ? show = 3 : show = false ">
                                    {{ $package->type !== 'cruise' ? __('base.pricelist') : __('base.arrivals-departures') }}
                                </div>
                            @endif
                        </div>
                        @include('packages.partials.__itinerary')

                        @include('packages.partials.__info')
                        @if ($package->pricelistImage())
                            @include('packages.partials.__pricelist')
                        @endif

                    </div>


                </div>
            </div>
            <div class="w-full lg:w-4/12 order-1 lg:order-2">
                {{-- Departure Dates --}}
                @include('packages.partials.__depart_dates')
            </div>
        </div>


    </div>

    <div id="main-cta" class="relative bg-darkCyan my-4 py-4 h-48"
        style="background-image: url('{{ asset('images/icons/travel-package-doodle.svg') }}'); background-repeat: no-repeat">

        <div class="text-center text-white text-3xl font-semibold">
            @if (!$package->price_fixed)
                {{ __('base.from') }}
            @endif

            {{ $package->price }} € / {{ __('base.person') }}
        </div>
        <div class="text-center text-white text-2xl ">
            <img src="{{ asset('images/icons/arrow-plane-button-orange.svg') }}"
                class="hidden lg:block absolute top-0 lg:left-[10%] xl:left-[12%] 2xl:left-[15%] w-1/4">

            <x-cta-tertiary>
                <a href="#reservation">{{ __('base.make-reservation') }}</a>
            </x-cta-tertiary>
        </div>
    </div>

    <div id="reservation" class="text-center my-12 uppercase flex justify-center items-center gap-4">
        <div>
            <a href="tel:+30 2102207506"><img class="w-24" src="{{ asset('images/icons/phone-kratiseis.svg') }}"
                    alt=""></a>
        </div>
        <div>
            <p class="text-2xl ">{{ __('form.call-us') }}</p>
            <p><x-address.phone class="font-bold text-2xl text-pacific-500 hover:text-wotDark" /></p>
        </div>
    </div>

    {{-- Contact Form --}}
    <livewire:forms.package-contact-lead-form :package="$package" :message_type="$package->type" />

    {{-- Categories and Tags --}}
    <div id="taxonomies" class="w-[90%] lg:w-4/5 mx-auto my-12">
        @include('packages.partials.__taxonomies')
    </div>

    <img src="{{ asset('/images/icons/travel-doodles.svg') }}" alt="">


    <div x-data="{
        isInView: false,
        ids: ['first-cta', 'main-cta', 'reservation', 'contact-form', 'taxonomies'],
        visibilityMap: {},
        init() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    this.visibilityMap[entry.target.id] = entry.isIntersecting;
                });
    
                // Update isInView based on any element being in view
                this.isInView = Object.values(this.visibilityMap).some(v => v === true);
            }, { threshold: 0.5 });
    
            // Observe all elements by ID
            this.ids.forEach(id => {
                const el = document.getElementById(id);
                if (el) {
                    this.visibilityMap[id] = false; // default value
                    observer.observe(el);
                }
            });
        }
    }">
        <button
            x-show="!isInView"
            @click="document.getElementById('reservation').scrollIntoView({ behavior: 'smooth' })"
            class="bg-sun-500 hover:bg-sun-700 text-white fixed bottom-0 left-[28%] right-[28%] lg:right-[10%] z-30 lg:left-[10%] text-center uppercase font-bold p-1"
        >
            {{ __('base.make-reservation') }}
        </button>
    </div>
    
    

</x-app-layout>
