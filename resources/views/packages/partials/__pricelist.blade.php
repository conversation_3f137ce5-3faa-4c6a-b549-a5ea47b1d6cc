<div x-show="show == 3"  x-data="{ showIncluded: false, showNotIncluded: false, isMobile: false, isTapped:false }" class="divide-y-8 divide-white mt-4" 
x-init="() => { isMobile = window.innerWidth < 768 ? true : false }">
    <div x-data="{modal: false}" class="capitalize">
        <img src="{{ asset('storage/'.$package->pricelistImageMd()) }}" alt="{{ __('base.pricelist') }} {{ $package->title }} " @click="isMobile && (isTapped = !isTapped) ? isTapped=true : modal=true " class="mx-auto" :class="isMobile && isTapped ? 'scale-125' : 'scale-100'">
        <div x-show="modal" class="hidden lg:block absolute top-0 left-0 right-0  z-50">
            <img src="{{ asset('storage/'.$package->pricelistImagelg()) }}" alt="" @click.outside="modal=false" class="mx-auto" >
        </div>
    </div>
</div>