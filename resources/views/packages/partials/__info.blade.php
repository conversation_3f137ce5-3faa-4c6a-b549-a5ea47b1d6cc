<div x-show="show == 2" x-data="{ showIncluded: false, showNotIncluded: false }" class="divide-y-8 divide-white mt-4">



    @if ($package->getTranslation('included', $locale))
        <div class="bg-lightGrey py-3 px-4">
            <div class="flex justify-between" @click="showIncluded = !showIncluded">
                <h3 class="font-semibold uppercase">{{ __('base.included') }}</h3>
                <div class="text-pacific-500 text-2xl cursor-pointer">
                    <span x-show="!showIncluded" x-transition.duration.500ms>+</span>
                    <span x-show="showIncluded" x-transition.duration.500ms style="display: none;">-</span>
                </div>
            </div>
            <div x-show="showIncluded" x-transition.duration.300ms class="pl-6">
                {!! $package->getTranslation('included', $locale) !!}
            </div>
        </div>
    @endif
    @if ($package->getTranslation('included', $locale))
        <div class="bg-lightGrey py-3 px-4">
            <div class="flex justify-between" @click="showNotIncluded = !showNotIncluded">
                <h3 class="font-semibold uppercase">{{ __('base.not_included') }}</h3>
                <div class="text-pacific-500 text-2xl cursor-pointer">
                    <span x-show="!showNotIncluded" x-transition.duration.500ms>+</span>
                    <span x-show="showNotIncluded" x-transition.duration.500ms style="display: none;">-</span>
                </div>
            </div>
            <div x-show="showNotIncluded" x-transition.duration.300ms class="pl-6">
                {!! $package->getTranslation('not_included', $locale) !!}
            </div>
        </div>
    @endif

    @if ($package->getTranslation('info', $locale))
        <div class="py-3 px-4">
            {!! $package->getTranslation('info', $locale) !!}
        </div>
    @endif


</div>
