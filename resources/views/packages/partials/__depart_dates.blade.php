@if ($package->departDates->count() && Carbon\Carbon::parse($package->departDates->max('date')) > Carbon\Carbon::now())
    <div>
        <div class="capitalize bg-darkCyan text-white font-extrabold text-2xl p-4 my-2">
            {{ $package->departDates->count() > 1 ? __('base.departures') : __('base.departure') }}
        </div>

        <div class="flex flex-wrap mt-6" x-data="{ showAll: false }">
            @foreach ($package->departDates as $departDate)
                {{-- Show only dates after today --}}
                @if (Carbon\Carbon::parse($departDate->date) > Carbon\Carbon::now())
                    <div class="w-1/2 flex items-center gap-2 mb-2 py-1"
                        x-show="{{ $loop->iteration <= 11 ? true : 'showAll' }}" x-cloak x-transition>
                        <img src="{{ asset('images/icons/calendar.svg') }}" alt="" class="w-6">
                        {{ Carbon\Carbon::parse($departDate->date)->format('d-m-Y') }}
                    </div>
                @endif
            @endforeach

            @if ($package->departDates->count() > 11)
                <button x-show="!showAll" @click="showAll = true"
                    class="cursor-pointer text-center text-pacific-500 text-xl font-bold">
                    ...+
                </button>

                <button x-show="showAll" @click="showAll = false"
                    class="cursor-pointer text-center text-pacific-500 text-xl font-bold">
                    ...-
                </button>
            @endif
        </div>
    </div>
@else
    <div class="capitalize bg-darkCyan text-white font-extrabold text-2xl p-4 my-2">
        {{ __('base.departures') }}
    </div>
    <p>{{ __('base.contact_for_departures') }}</p>
@endif

<div class="flex justify-center" id="first-cta">
    <a href="#reservation" class=" ">
        <x-cta-tertiary>
            {{ __('base.make-reservation') }}
        </x-cta-tertiary>
    </a>
</div>
