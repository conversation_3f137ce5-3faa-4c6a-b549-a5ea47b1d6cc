<div class="group md:w-[48%] lg:w-1/3 2xl:w-1/4 py-2 lg:p-2 relative">

    <div class="relative">
        <div class="absolute right-0 z-10 max-w-[80%]">
            <h4 class="p-2 bg-pacific-500 text-white group-hover:bg-pacific-600 text-xl font-bold">
                <a href="{{ route($locale . '.travel.show', $package->slug) }}">
                    {!! $package->getTranslation('title', $locale) !!}
                </a>
            </h4>

        </div>
        <a href="{{ route($locale . '.travel.show', $package->slug) }}">
            <img class="h-[350px] lg:h-[500px] object-cover group-hover:brightness-90"
                src="{{ $package->mainImage() ? asset('storage/' . $package->imageMd()) : '' }}"
                loading="lazy"
                alt="{{ $package->alt() }}">
        </a>
        <div
            class="absolute bottom-0 left-0 right-0 z-20 p-2 text-white font-bold flex gap-2 justify-between items-center">
            <div class="flex gap-2">
                @if ($package->duration)
                    <x-icons.clock-icon />

                    {{ $package->duration }} <span class="uppercase">{{ __('base.days') }}</span>
                @endif
            </div>
            <div class="font-bold">
                @if (!$package->price_fixed)
                    <span class="uppercase">{{ __('base.from') }}</span>
                @endif
                {{ $package->price }}€/<span class="uppercase text-xs">{{ __('base.person') }}</span>
            </div>

        </div>
        <div
            class="absolute bottom-0 left-0 right-0 h-16 z-10 bg-gradient-to-t from-darkCyan/50 via-darkCyan/30 to-transparent">
        </div>
    </div>
    <div class="bg-darkCyan px-2 text-white text-center font-bold py-3 min-h-[5rem]">


        <div class="flex flex-wrap mb-2">
            @if ($package->departDates->count())
                <span class="uppercase text-sm mr-2">{{ __('base.departures') }}: </span>
            @endif
            @forelse ($package->departDates as $departDate)
                <span class="text-sm mr-1 block">
                    {{ Carbon\Carbon::parse($departDate->getRawOriginal('date'))->format('d/m') }}{{ !$loop->last ? ',' : '' }}
                </span>
            @empty
            @endforelse

        </div>

        <div class="flex flex-wrap items-center">
            @forelse ($package->categories as $category)
                <span class="hover:text-pacific-500 pr-2 py-1 text-[.9rem]">
                    <a href="{{ route($locale . '.category.show', $category) }}">{{ strip_tags($category->getTranslation('title', $locale)) }}{{ !$loop->last ? ',' : '' }}
                    </a>
                </span>
            @empty
            @endforelse
        </div>

        <div class="flex flex-wrap items-center">
            @forelse ($package->tags as $tag)
                <span class="uppercase text-pacific-500 hover:text-white pr-1 py-1 text-sm">
                    <a href="{{ route($locale . '.tag.show', $tag) }}">{{ $tag->getTranslation('title', $locale) }}{{ !$loop->last ? ',' : '' }}
                    </a>
                </span>
            @empty
            @endforelse
        </div>
    </div>
</div>
