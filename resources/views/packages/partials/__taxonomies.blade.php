{{-- Categories --}}
@if ($categories->count())
    <div class="uppercase my-3 text-center">
        <div class="bg-wotDark text-white inline-block px-2 leading-8 font-semibold">
            {{ __('base.categories') }}
        </div>

        <div class="mt-2">

            @forelse ($categories as $category)
                <x-tag
                    href="{{ route($locale.'.category.show', $category) }}">{{ $category->getTranslation('title', $locale) }}{{ !$loop->last ? ',' : '' }}</x-tag>
            @empty
            @endforelse
        </div>
    </div>
@endif


{{-- Tags --}}
@if ($tags->count())
    <div class="uppercase my-3 text-center">
        <div class="bg-wotDark text-white inline-block px-2 leading-8 font-semibold">
            {{ __('base.tags') }}
        </div>

        <div class="mt-2">

            @forelse ($tags as $tag)
                <x-tag href="{{ route($locale.'.tag.show', $tag) }}">{{ $tag->getTranslation('title', $locale) }}{{ !$loop->last ? ',' : '' }}</x-tag>
            @empty
            @endforelse
        </div>
    </div>
@endif
