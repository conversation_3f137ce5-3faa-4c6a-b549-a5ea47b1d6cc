<div x-show="show == 1" class="divide-y-8 divide-white mt-4">

    <div>
        {!! $package->getTranslation('itinerary', $locale) !!}
    </div>

    @foreach ($package->itinerary_steps as $step)
        @if ($step->getTranslation('title', $locale))
            <div x-data="{ showContent: false }" class="bg-lightGrey py-3 px-4">
                <div class="flex justify-between" @click="showContent = !showContent">
                    <h4 class="font-semibold">{{ $step->title }}</h4>
                    <div class="text-pacific-500 text-2xl cursor-pointer relative">
                        <span x-show="!showContent" x-transition.duration.500ms x-cloak class="absolute right-3">+</span>
                        <span x-show="showContent" x-transition.duration.500ms x-cloak class="absolute right-3">-</span>
                    </div>
                </div>
                <div class="mt-5" x-show="showContent" x-transition.duration.300ms>
                    {!! $step->getTranslation('content', $locale) !!}
                </div>
            </div>
        @endif
    @endforeach
</div>
