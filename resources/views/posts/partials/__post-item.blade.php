{{-- $searchItem variable comes from search results page --}}
<div class="{{ isset($searchItem) ? 'lg:w-[23%]' : 'lg:w-[48%]' }} relative">
    <div class="{{ $post->mainImage() ? 'absolute left-2 top-2 inline' :'' }}  z-20 bg-pacific-500 text-white w-16 h-16 p-2 uppercase text-center">
        {{ Carbon\Carbon::parse($post->published_at)->translatedFormat('d M ') }}
    </div>
    @if ($post->mainImage())
        <a href="{{ route($locale . '.posts.show', $post) }}"><img src="{{ asset('storage/' . $post->imageMd()) }}"
                alt="" class="w-full relative z-0"></a>
    @endif

    <div class="{{ $post->mainImage() ? '-mt-8 lg:-mt-24  mx-4' : '' }} relative z-20 bg-white border-2 border-pacific-800 py-4 px-6 ">
        <span>
            @foreach ($post->tags()->whereLocale('title', App::getLocale())->get() as $tag)
                <a href="{{ route($locale.'.tag.show', $tag) }}" class="uppercase text-pacific-500 text-sm">
                    {{ $tag->getTranslation('title', $locale) }}{{ !$loop->last ? ',' : '' }}
                </a>
            @endforeach
        </span>
        <h2 class="text-3xl text-darkCyan"><a href="{{ route('posts.show', $post->slug) }}">{{ $post->title }}</a></h2>
        <p class="">{!! $post->excerpt(30) !!}</p>
        <div>
            <x-read-more href="{{ route($locale . '.posts.show', $post) }}" >
                {{ __('blog.readmore') }}
            </x-read-more>
        </div>
    </div>
</div>
