<div class="mt-8">
    {{-- Tags --}}
    @if ($tags->count())
        <div class="uppercase my-3">
            <div class="bg-wotDark text-white inline-block px-2 leading-8 font-semibold">
                {{ $type == 'tag' ? __('base.tags') : __('base.categories') }}
            </div>

            <div class="mt-2">

                @forelse ($tags as $tag)
                    <x-tag
                        href="{{ route($locale.'.'.$type.'.show', $tag) }}">{{ $tag->getTranslation('title', $locale) }}{{ !$loop->last ? ',' : '' }}</x-tag>
                @empty
                @endforelse
            </div>
        </div>
    @endif
</div>