<div id="sidebar" class="lg:w-1/5 border-l px-6 mt-12 lg:mt-0 space-y-10">

    @if ($tags->count())
        <div>
            <h3 class="bg-pacific-500 text-lg text-white text-center font-bold title-box-shadow px-6 py-3 mb-4">TAGS</h3>
            <div class="mt-6">
                @foreach ($tags as $tag)
                    <div class="my-2">
                        <a class="hover:text-darkCyan uppercase" href="{{ route($locale.'.tag.show', $tag) }}">{{ $tag->title }}
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    @if ($featuredPosts->count())
        <div>
            <h3
                class="bg-pacific-500 text-lg text-white text-center font-bold title-box-shadow px-6 py-3 mb-4 uppercase">
                {{ __('blog.featured-posts') }}
            </h3>

            <div class="mt-6">
                @foreach ($featuredPosts as $post)
                    <div class="my-2 py-3 border-b border-wotDark">
                        <a class="hover:text-darkCyan" href="{{ route('posts.show', $post) }}">{{ $post->title }}</a>
                    </div>
                @endforeach
            </div>
        </div>
    @endif


</div>
