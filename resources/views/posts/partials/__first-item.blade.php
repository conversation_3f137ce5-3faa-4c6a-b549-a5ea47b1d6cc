<div class="w-full relative">
    <div class="{{ $post->mainImage() ? 'absolute left-2 top-2 inline' : '' }} bg-pacific-500 text-white w-16 h-16 p-2 uppercase text-center">
        {{ Carbon\Carbon::parse($post->published_at)->translatedFormat('d M ') }}
    </div>
    <a href="{{ route('posts.show', $post) }}">
        @if ($post->mainImage())
            <img src="{{ asset('storage/' . $post->imageMd()) }}" alt="" class="w-full">
        @endif
    </a>
    <div
        class=" {{ $post->mainImage() ? 'relative lg:absolute lg:w-[50%] xl:w-[35%] lg:max-h-[98%] -mt-8' : '' }} lg:bottom-4 lg:right-4 z-10  lg:mt-0 bg-white border-2 border-pacific-800 py-4 px-6">
        <span>
            @foreach ($post->tags()->whereLocale('title', App::getLocale())->get() as $tag)
                <a href="{{ route($locale.'.tag.show', $tag) }}" class="uppercase text-pacific-500 text-sm">
                    {{ $tag->getTranslation('title', $locale) }}{{ !$loop->last ? ',' : '' }}
                </a>
            @endforeach
        </span>
        <h2 class="text-3xl text-darkCyan"><a href="{{ route('posts.show', $post->slug) }}">{{ $post->title }}</a></h2>
        <p class="">{!! $post->excerpt(70) !!}</p>
        <div>
            <x-read-more href="{{ route($locale . '.posts.show', $post) }}" >
                {{ __('blog.readmore') }}
            </x-read-more>
        </div>
    </div>
</div>
