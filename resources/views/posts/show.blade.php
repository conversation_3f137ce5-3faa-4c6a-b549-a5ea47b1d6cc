<x-app-layout>

    @section('head')
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    @endsection

    @section('metatitle', $post->getTranslation('meta_title', $locale) ? $post->getTranslation('meta_title', $locale) :
        $post->getTranslation('title', $locale))
    @section('metadescription', $post->getTranslation('meta_description', $locale))

    @section('og_title', $post->getTranslation('meta_title', $locale) ? $post->getTranslation('meta_title', $locale) :
        $post->getTranslation('title', $locale))
    @section('og_description', $post->getTranslation('meta_description', $locale))

    @section('modified_date', $post->updated_at)
    @section('og_image', asset('storage/' . $post->imageLg()))

    <x-slot:header>
        <div class="relative">
            {{-- Desktop with parallax --}}
            <div class="hidden lg:block lg:min-h-[600px] bg-fixed bg-bottom bg-no-repeat bg-content"
                style="background-image: url('{{ asset('images/bulk/blog-item-header.webp') }}')">
            </div>
            {{-- Mobile without parallax --}}
            <div class="lg:hidden">
                <img src="{{ asset('images/bulk/blog-item-header.webp') }}" alt="" class="">
            </div>
            <img src="{{ asset('images/icons/scribble.svg') }}" alt="" class="absolute -bottom-2 lg:-bottom-1">
        </div>

        <div
            class="md:absolute md:bottom-[30%] md:left-[20%] lg:left-auto lg:right-10 2xl:bottom-[30%] 2xl:right-[10%] text-center">
            <h1 class="text-pacific-500 text-3xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl  font-bold">
                {{ __('blog.title') }} </h1>
            <h2 class="md:text-white text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-funny md:mt-3">
                {{ __('blog.subtitle') }}</h2>
        </div>

    </x-slot>

    {{-- Breadcrumbs --}}
    <div class="w-[90%] lg:w-4/5 lg:mx-auto lg:-mt-3 relative ">
        <ul class="!text-sm text-gray-500 flex flex-wrap !list-none">
            <li>
                <a href="/"><x-icons.home-icon /></a>
            </li>

            <li class="before:content-['/\00a0'] before:p-1">
                <a href="{{ route($locale . '.posts.index') }}">Blog</a>
            </li>
            <li class="before:content-['/\00a0'] before:p-1">
                {{ $post->getTranslation('title', $locale) }}
            </li>
        </ul>
    </div>

    <div class="max-w-8xl mx-auto mt-8 mb-16 sm:px-6 lg:px-8">
        <div class="lg:flex">
            <div id="post-item" class="lg:w-4/5 px-4">
                @if (auth()->user() &&
                        auth()->user()->hasAnyRole(['super admin', 'admin']))
                    <div class="flex justify-end mb-4 -mt-4">
                        @if (!$post->published)
                            <span class="text-red-500 text-md">Admin Notification: Post is not published</span>
                        @endif
                        <x-cerberus::form.link href="{{ route('cerberus.posts.edit', $post) }}">Επεξεργασία
                        </x-cerberus::form.link>

                    </div>

                @endif
                <h1 class="mb-8">{{ $post->title }}</h1>

                @if ($post->mainImage())
                    <div class="relative">
                        <img src="{{ asset('storage/' . $post->imageLg()) }}" alt="{{ $post->alt() }}" class="w-full">
                        <div class="absolute bottom-0 right-0 bg-pacific-500 text-white p-4 text-xl uppercase">
                            {{ Carbon\Carbon::parse($post->published_at)->translatedFormat('d F Y') }}
                        </div>
                    </div>
                @endif

                <div class="mt-10">
                    {!! $post->getTranslation('content', $locale) !!}
                </div>

                @include('posts.partials.__tags', ['tags' => $post->categories, 'type' => 'category'])
                @include('posts.partials.__tags', ['tags' => $post->tags, 'type' => 'tag'])



                <div id="navigation" class="flex justify-between items-center text-left mt-12">
                    <div class="w-1/2">
                        @if ($post->previousPost())
                            <a class="text-darkCyan text-xl font-semibold flex items-center gap-2"
                                href="{{ route($locale . '.posts.show', $post->previousPost()) }}">

                                <x-icons.left-arrow />

                                {{ $post->previousPost()->title }}
                            </a>
                        @endif

                    </div>
                    <div class="w-1/2 text-right">
                        @if ($post->nextPost())
                            <a class="text-darkCyan text-xl font-semibold flex items-center justify-end gap-1"
                                href="{{ route($locale . '.posts.show', $post->nextPost()) }}">
                                {{ $post->nextPost()->title }}

                                <x-icons.right-arrow />

                            </a>
                        @endif
                    </div>
                </div>

            </div>

            @include('posts.partials.__sidebar')

        </div>

    </div>

    <img src="{{ asset('images/icons/blog-travel-doodles.svg') }}" alt="">


</x-app-layout>
