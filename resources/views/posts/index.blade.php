<x-app-layout>

    @section('metatitle', 'Blog | World of Travel')
    @section('metadescription')

    @section('og_title', 'Blog | World of Travel')
    @section('og_description', '')

    @section('og_image', asset('images/bulk/blog-header.webp'))

    <x-slot:header>
        <div class="relative">

            {{-- Desktop with parallax --}}
            <div class="hidden lg:block lg:min-h-[600px] bg-fixed bg-bottom bg-no-repeat bg-content"
                style="background-image: url('{{ asset('images/bulk/blog-header.webp') }}')">
            </div>
            {{-- Mobile without parallax --}}
            <div class="lg:hidden">
                <img src="{{ asset('images/bulk/blog-header.webp') }}"
                    alt="" class="">
            </div>

            <img src="{{ asset('images/icons/scribble.svg') }}" alt="" class="absolute -bottom-1">
        </div>

        <div
            class="relative md:absolute md:bottom-[30%] md:right-[20%] lg:right-[15%] 2xl:bottom-[30%] 2xl:right-[10%] text-center">
            <h1
                class="{{ isset($tag) ? 'text-darkCyan' : 'text-pacific-500' }}  text-3xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl font-bold">
                {{ __('blog.title') }} </h1>
            <h2 class="md:text-white text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-funny md:mt-3">
                {{ __('blog.subtitle') }}
            </h2>
        </div>

    </x-slot>

    <img src="{{ asset('images/icons/blog-travel-doodles.svg') }}" alt="">

    {{-- Breadcrumbs --}}
    <div class="w-[90%] lg:w-4/5 mx-auto -mt-3 z-10 relative ">
        <ul class="!text-sm text-gray-500 flex !list-none">
            <li>
                <a href="/"><x-icons.home-icon /></a>
            </li>

            <li class="before:content-['/\00a0'] before:p-2">
                <a href="{{ route($locale . '.posts.index') }}">Blog</a>
            </li>

        </ul>
    </div>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden">
                <div class="p-6 text-gray-900 flex flex-wrap justify-between gap-6">
                    @foreach ($posts as $post)
                        @if ($loop->first)
                            @include('posts.partials.__first-item')
                        @else
                            @include('posts.partials.__post-item')
                        @endif
                    @endforeach
                </div>
            </div>
            <div class="mx-auto my-4">
                {{ $posts->links() }}
            </div>
        </div>

    </div>


</x-app-layout>
